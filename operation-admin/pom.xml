<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>operation</artifactId>
        <groupId>com.hzforward</groupId>
        <version>3.8.4</version>
    </parent>
    <packaging>jar</packaging>
    <artifactId>operation-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!-- swagger3-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <!-- 防止进入swagger页面报类型转换错误，排除3.0.0中的引用，手动增加1.6.2版本 -->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- jcifs 共享文件夹目录 -->
        <dependency>
            <groupId>org.samba.jcifs</groupId>
            <artifactId>jcifs</artifactId>
            <version>1.3.3</version>
        </dependency>

         <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-generator</artifactId>
        </dependency>

        <!-- 生产设备管理 -->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-equipmentManage</artifactId>
        </dependency>

        <!-- 物料采购-->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-material</artifactId>
        </dependency>

        <!-- 件号系统-->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-partCode</artifactId>
        </dependency>

        <!-- 文件共享系统-->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-fileSharing</artifactId>
        </dependency>

        <!-- IT资产台账-->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-itProperty</artifactId>
        </dependency>

        <!-- 数据中心 -->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-dataCenter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-wechat</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-dinnerManage</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-assemble</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-meter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-sync</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-sign</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-callStation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-speedLimiterMarking</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-nonstandardReview</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-dataCheck</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-dataReview</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-visitor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-cp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-processCheck</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-wms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-oee</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
           </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>
