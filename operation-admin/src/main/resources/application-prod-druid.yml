# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: ********************************************************************************************************************************************************************************************************
                username: operation
                password: fwdFWD888+-*/
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
                url: *************************************************
                username: sa
                password: ',S74b?C0'
            # 从库数据源: 限速器/安全钳 追溯系统数据库
            slave1:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.mysql.cj.jdbc.Driver
                url: **************************************************************************************************************************************************
                username: root
                password: 123456
            # 从库数据源
            slave2:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
                url: *************************************************
                username: sa
                password: ',S74b?C0'
            # 从库数据源
            slave3:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.mysql.cj.jdbc.Driver
                url: *******************************************************************************************************************************************
                username: root
                password: ',S74b?C0'
            # 从库数据源
            slave4:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
                url: ****************************************************
                username: sa
                password: 'Xzfwd@1234'
            # 从库数据源
            slave5:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
                url: ****************************************************
                username: sa
                password: 'Xzfwd@1234'
            # 从库数据源
            slave6:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
                url: ****************************************************
                username: sa
                password: 'Xzfwd@1234'
            # 从库数据源
            slave7:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.microsoft.sqlserver.jdbc.SQLServerDriver
                url: ****************************************************
                username: sa
                password: ',S74b?C0'
            # 从库数据源: 限速器/安全钳 追溯系统数据库
            slave8:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: com.mysql.cj.jdbc.Driver
                url: ***********************************************************************************************************************************************
                username: root
                password: root
            # 从库数据源: 云枢数据库
            slave9:
                # 从数据源开关/默认关闭
                enabled: true
                driverClassName: oracle.jdbc.OracleDriver
                url: **************************************
                username: fwd
                password: oraclefwd
                initialSize: 5
                minIdle: 5
                maxActive: 20
                maxWait: 60000
                timeBetweenEvictionRunsMillis: 60000
                minEvictableIdleTimeMillis: 300000
                validationQuery: SELECT 1 FROM DUAL
                testWhileIdle: true
                testOnBorrow: false
                testOnReturn: false
                poolPreparedStatements: true
                maxPoolPreparedStatementPerConnectionSize: 20
                filters: stat,wall
                connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 200
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: operation
                login-password: fwdFWD888+-*/
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 30000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
