# Spring配置
spring:
  # 环境配置 dev开发环境  prod生产环境
  profiles:
    active: dev,dev-druid
#    active: dev,prod-druid
#    active: prod,prod-druid

  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  200MB
      # 设置总上传的文件大小
      max-request-size:  500MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: false
      additional-paths: src/main
  # redis 配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 40s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

  # 域连接配置
  ldap:
    urls: ldap://**********:389
    username: wei.jian<PERSON>@elevator.xizi.local
    password: Wjb123456789
    base: OU=Users,OU=HzForward,OU=XIZIELEVATOR,DC=ELEVATOR,DC=xizi,DC=local

  #邮箱基本配置
  mail:
    #配置smtp服务主机地址
    host: smtp.mxhichina.com
    #发送者邮箱
    username: <EMAIL>
    #配置密码
    password: zjhls2011
    #端口号465或587
    port: 587
    #默认的邮件编码为UTF-8
    default-encoding: UTF-8
    #其他参数
    properties:
      mail:
        #配置SSL 加密工厂
        smtp:
          ssl:
            #本地测试，先放开ssl
            enable: false
            required: false
          #开启debug模式，这样邮件发送过程的日志会在控制台打印出来，方便排查错误
        debug: false

# 项目相关配置
operation:
  # 名称
  name: operation
  # 版本
  version: 3.8.4
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，9999
  port: 9999
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值25
      min-spare: 100

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 600

# MyBatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.hzforward.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  #MyBatisPlus全局配置
  global-config:
    db-config:
      #全局配置逻辑删除字段名
      logic-delete-field: delFlag
      #逻辑已删除值（默认1）
      logic-delete-value: 2
      #逻辑未删除值（默认0）
      logic-not-delete-value: 0
  # 加载全局的配置文件

# PageHelper分页插件
pagehelper:
  supportMethodsArguments: true
  params: count=countSql
  #默认值为 false。设置为 true 时，允许在运行时根据多数据源自动识别对应方言的分页
  autoRuntimeDialect: true

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*