# 项目相关配置
operation:
  # 上传文件路径
  profile: D:/operation/uploadPath
  wechatDefaultApplet: operation

# 证书配置
server:
  ssl:
    key-store: D:\operation\ssl\se-hzforward-com-tomcat.jks
    key-store-type: JKS
    key-store-password: sT18m3OM

logging:
  file:
    path: D:/operation/logs
  level:
    com.hzforward: warn
    org.springframework: warn

spring:
  rabbitmq:
    host: ********** # 你的虚拟机IP
    port: 31563 # 端口
    virtual-host: /hzforward-prod # 虚拟主机
    username: hzforward # 用户名
    password: Hzforward_operation # 密码
    listener:
      simple:
        retry:
          enabled: true # 开启消费者失败重试
          initial-interval: 1000ms # 初识的失败等待时长为1秒
          multiplier: 1 # 失败的等待时长倍数，下次等待时长 = multiplier * last-interval
          max-attempts: 5 # 最大重试次数
          stateless: true # true无状态；false有状态。如果业务中包含事务，这里改为false