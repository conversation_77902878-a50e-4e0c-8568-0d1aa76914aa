package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.domain.DailyInspectionReport;
import com.hzforward.equipmentManage.service.IDailyInspectionReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备点检报告Controller
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@RestController
@RequestMapping("/equipmentManage/dailyInspectionReport")
public class DailyInspectionReportController extends BaseController
{
    @Resource
    private IDailyInspectionReportService equipmentDailyInspectionReportService;

    /**
     * 查询设备点检报告列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(DailyInspectionReport dailyInspectionReport)
    {
        startPage();
        List<DailyInspectionReport> list = equipmentDailyInspectionReportService.selectDailyInspectionReportList(dailyInspectionReport);
        return getDataTable(list);
    }

    /**
     * 导出设备点检报告列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionReport:export')")
    @Log(title = "设备点检报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DailyInspectionReport dailyInspectionReport)
    {
        List<DailyInspectionReport> list = equipmentDailyInspectionReportService.selectDailyInspectionReportList(dailyInspectionReport);
        ExcelUtil<DailyInspectionReport> util = new ExcelUtil<>(DailyInspectionReport.class);
        util.exportExcel(response, list, "设备点检报告数据");
    }

    /**
     * 获取设备点检报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionReport:query')")
    @GetMapping(value = "/{reportId}")
    public AjaxResult getInfo(@PathVariable("reportId") Long reportId)
    {
        return AjaxResult.success(equipmentDailyInspectionReportService.selectDailyInspectionReportByReportId(reportId));
    }

    /**
     * 新增设备点检报告
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionReport:add')")
    @Log(title = "设备点检报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DailyInspectionReport dailyInspectionReport)
    {
        return toAjax(equipmentDailyInspectionReportService.insertDailyInspectionReport(dailyInspectionReport));
    }

    /**
     * 修改设备点检报告
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionReport:edit')")
    @Log(title = "设备点检报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DailyInspectionReport dailyInspectionReport)
    {
        return toAjax(equipmentDailyInspectionReportService.updateDailyInspectionReport(dailyInspectionReport));
    }

    /**
     * 删除设备点检报告
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionReport:remove')")
    @Log(title = "设备点检报告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reportIds}")
    public AjaxResult remove(@PathVariable List<Long> reportIds)
    {
        return toAjax(equipmentDailyInspectionReportService.deleteDailyInspectionReportByReportIds(reportIds));
    }

    /**
     * 查询设备当天点检列表以及结果
     */
    @PostMapping("/getDailyInspectionReportByDate")
    public AjaxResult getDailyInspectionReportByDate(@RequestBody DailyInspectionReport dailyInspectionReport)
    {
        return  AjaxResult.success(equipmentDailyInspectionReportService.getDailyInspectionReportByDate(dailyInspectionReport));
    }

    /**
     * 处理移动端点检请求
     */
    @PostMapping("/inspectionReportSubmit")
    public AjaxResult inspectionReportSubmit(@RequestBody DailyInspectionReport dailyInspectionReport)
    {
        return toAjax(equipmentDailyInspectionReportService.inspectionReportSubmit(dailyInspectionReport));
    }
}
