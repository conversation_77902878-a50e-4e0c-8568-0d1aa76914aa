package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.equipmentManage.service.IStaffCommonService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 员工常用设备列表Controller
 *
 * <AUTHOR>
 * @date 2025-04-07
 */
@RestController
@RequestMapping("/equipmentManage/staffCommon")
public class StaffCommonController extends BaseController
{
    @Resource
    private IStaffCommonService staffCommonService;

    /**
     * 查询员工常用设备列表
     */
    @GetMapping("/getDict/{userName}")
    public AjaxResult getDict(@PathVariable String userName)
    {
        return AjaxResult.success(staffCommonService.getCommonEquipmentDict(userName));
    }

    /**
     *  员工移动端手动修改常用设备
     */
    @PostMapping("/setCommonEquipment/{type}&{userName}&{equipNo}")
    public AjaxResult setCommonEquipment(@PathVariable("type") String type, @PathVariable("userName") String userName, @PathVariable("equipNo") String equipNo)
    {
        return AjaxResult.success(staffCommonService.setCommonEquipment(type, userName, equipNo));
    }

}
