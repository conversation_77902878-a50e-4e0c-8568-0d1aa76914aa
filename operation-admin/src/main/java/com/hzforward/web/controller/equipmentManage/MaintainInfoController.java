package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.domain.MaintainInfo;
import com.hzforward.equipmentManage.service.IMaintainInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保养计划Controller
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@RestController
@RequestMapping("/equipmentManage/maintainInfo")
public class MaintainInfoController extends BaseController
{
    @Resource
    private IMaintainInfoService maintainInfoService;

    /**
     * 查询保养计划列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaintainInfo maintainInfo)
    {
        startPage();
        List<MaintainInfo> list = maintainInfoService.selectMaintainInfoList(maintainInfo);
        return getDataTable(list);
    }

    /**
     * 导出保养计划列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainInfo:export')")
    @Log(title = "保养计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaintainInfo maintainInfo)
    {
        List<MaintainInfo> list = maintainInfoService.selectMaintainInfoList(maintainInfo);
        ExcelUtil<MaintainInfo> util = new ExcelUtil<>(MaintainInfo.class);
        util.exportExcel(response, list, "保养计划数据");
    }

    /**
     * 获取保养计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainInfo:query')")
    @GetMapping(value = "/{maintainId}")
    public AjaxResult getInfo(@PathVariable("maintainId") Long maintainId)
    {
        return AjaxResult.success(maintainInfoService.getById(maintainId));
    }

    /**
     * 新增保养计划
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainInfo:add')")
    @Log(title = "保养计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaintainInfo maintainInfo)
    {
        return toAjax(maintainInfoService.save(maintainInfo));
    }

    /**
     * 修改保养计划
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainInfo:edit')")
    @Log(title = "保养计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaintainInfo maintainInfo)
    {
        return toAjax(maintainInfoService.updateById(maintainInfo));
    }

    /**
     * 删除保养计划
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainInfo:remove')")
    @Log(title = "保养计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{maintainIds}")
    public AjaxResult remove(@PathVariable List<Long> maintainIds)
    {
        return toAjax(maintainInfoService.removeBatchByIds(maintainIds));
    }

    /*
     *   导出导入模板
     */
    @PostMapping("/importMaintainInfoTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<MaintainInfo> util = new ExcelUtil<>(MaintainInfo.class);
        util.importTemplateExcel(response, "设备保养计划");
    }

    /**
     * 导入保养计划
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainInfo:import')")
    @Log(title = "保养计划", businessType = BusinessType.INSERT)
    @PostMapping("/importMaintainInfo")
    public AjaxResult importMaintainInfo(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<MaintainInfo> util = new ExcelUtil<>(MaintainInfo.class);
        List<MaintainInfo> maintainInfoList = util.importExcel(file.getInputStream());
        String message = maintainInfoService.importMaintainInfo(maintainInfoList, updateSupport);
        return AjaxResult.success(message);
    }
}

