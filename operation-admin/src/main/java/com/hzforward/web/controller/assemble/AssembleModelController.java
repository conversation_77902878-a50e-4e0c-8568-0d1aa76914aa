package com.hzforward.web.controller.assemble;


import com.hzforward.assemble.domain.AssembleModel;
import com.hzforward.assemble.service.IAssembleModelService;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 配送清单机型Controller
 * 
 * <AUTHOR> hongjun
 * @date 2020-05-14
 */
@RestController
@RequestMapping("/assemble/model")
public class AssembleModelController extends BaseController
{
    @Resource
    private IAssembleModelService assembleModelService;

    /**
     * 查询配送清单机型列表
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssembleModel assembleModel)
    {
        startPage();
        List<AssembleModel> list = assembleModelService.selectAssembleModelList(assembleModel);
        return getDataTable(list);
    }

    /**
     * 导出配送清单机型列表
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:export')")
    @Log(title = "配送清单机型", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(AssembleModel assembleModel)
    {
        List<AssembleModel> list = assembleModelService.selectAssembleModelList(assembleModel);
        ExcelUtil<AssembleModel> util = new ExcelUtil<>(AssembleModel.class);

        return util.exportExcel(list, "model");
    }

    /**
     * 获取配送清单机型详细信息
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(assembleModelService.selectAssembleModelById(id));
    }

    /**
     * 新增配送清单机型
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:add')")
    @Log(title = "配送清单机型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssembleModel assembleModel)
    {
        return toAjax(assembleModelService.insertAssembleModel(assembleModel));
    }

    /**
     * 修改配送清单机型
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:edit')")
    @Log(title = "配送清单机型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssembleModel assembleModel)
    {
        return toAjax(assembleModelService.updateAssembleModel(assembleModel));
    }

    /**
     * 删除配送清单机型
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:remove')")
    @Log(title = "配送清单机型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(assembleModelService.deleteAssembleModelByIds(ids));
    }

    /**
     * 配送清单导入
     * @param file
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:import')")
    @Log(title = "配送清单管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        ExcelUtil<AssembleModel> util = new ExcelUtil<>(AssembleModel.class);
        List<AssembleModel> userList = util.importExcel(file.getInputStream());
        String message = assembleModelService.importData(userList);
        return AjaxResult.success(message);
    }
}
