package com.hzforward.web.controller.visitor;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.visitor.domain.ReservationInfo;
import com.hzforward.visitor.service.IReservationInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 访客预约信息Controller
 *
 * <AUTHOR>
 * @date 2024-08-17
 */
@RestController
@RequestMapping("/visitor/reservationInfo")
@RequiredArgsConstructor
public class ReservationInfoController extends BaseController
{
    private final IReservationInfoService reservationInfoService;

    /**
     * 查询访客预约信息列表
     */
    @PreAuthorize("@ss.hasPermi('visitor:reservationInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(ReservationInfo reservationInfo)
    {
        startPage();
        List<ReservationInfo> list = reservationInfoService.selectReservationInfoList(reservationInfo);
        return getDataTable(list);
    }

	/**
	 * 获取访客预约信息详细信息
	 */
	@PreAuthorize("@ss.hasPermi('visitor:reservationInfo:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id)
	{
		return AjaxResult.success(reservationInfoService.getById(id));
	}

    /**
     * 新增访客预约信息
     */
    @PreAuthorize("@ss.hasPermi('visitor:reservationInfo:add')")
    @Log(title = "访客预约信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ReservationInfo reservationInfo)
    {
        return toAjax(reservationInfoService.save(reservationInfo));
    }

    /**
     * 修改访客预约信息
     */
    @PreAuthorize("@ss.hasPermi('visitor:reservationInfo:edit')")
    @Log(title = "访客预约信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ReservationInfo reservationInfo)
    {
        return toAjax(reservationInfoService.updateById(reservationInfo));
    }

    /**
     * 删除访客预约信息
     */
    @PreAuthorize("@ss.hasPermi('visitor:reservationInfo:remove')")
    @Log(title = "访客预约信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(reservationInfoService.removeBatchByIds(ids));
    }

    @PostMapping("/reservationInfoSubmit")
    public AjaxResult reservationInfoSubmit(@RequestBody ReservationInfo reservationInfo)
    {
        return toAjax(reservationInfoService.reservationInfoSubmit(reservationInfo));
    }

    @GetMapping(value = "/record/{id}")
    public AjaxResult record(@PathVariable("id") Long id)
    {
        return AjaxResult.success(reservationInfoService.getById(id));
    }
}
