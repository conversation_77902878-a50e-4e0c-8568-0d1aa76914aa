package com.hzforward.web.controller.assemble;


import com.hzforward.assemble.domain.AssembleProduction;
import com.hzforward.assemble.service.IAssembleProductionService;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产数据管理Controller
 * 
 * <AUTHOR>
 * @date 2020-04-21
 */
@RestController
@RequestMapping("/assemble/production")
public class AssembleProductionController extends BaseController
{
    @Resource
    private IAssembleProductionService assembleProductionService;

    /**
     * 查询生产数据管理列表
     */
    @PreAuthorize("@ss.hasPermi('data:production:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssembleProduction assembleProduction)
    {
        startPage();
        List<AssembleProduction> list = assembleProductionService.selectproductionList(assembleProduction);
        return getDataTable(list);
    }

    /**
     * 导出生产数据管理列表
     */
    @PreAuthorize("@ss.hasPermi('data:production:export')")
    @Log(title = "生产数据管理", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(AssembleProduction assembleProduction)
    {
        List<AssembleProduction> list = assembleProductionService.selectproductionList(assembleProduction);
        ExcelUtil<AssembleProduction> util = new ExcelUtil<>(AssembleProduction.class);
        return util.exportExcel(list, "production");
    }

    /**
     * 获取生产数据管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('data:production:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(assembleProductionService.selectproductionById(id));
    }

    /**
     * 新增生产数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:production:add')")
    @Log(title = "生产数据管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssembleProduction assembleProduction)
    {
        return toAjax(assembleProductionService.insertproduction(assembleProduction));
    }

    /**
     * 修改生产数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:production:edit')")
    @Log(title = "生产数据管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssembleProduction assembleProduction)
    {
        return toAjax(assembleProductionService.updateproduction(assembleProduction));
    }

    /**
     * 删除生产数据管理
     */
    @PreAuthorize("@ss.hasPermi('data:production:remove')")
    @Log(title = "生产数据管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(assembleProductionService.deleteproductionByIds(ids));
    }
}
