package com.hzforward.web.controller.sync;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.sync.service.ISyncStatService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 统计查询
 */
@Api(tags = {"统计查询"})
@RestController
@RequestMapping("/sync/stat")
public class SyncStatController {

    @Resource
    private ISyncStatService syncStatService;

    /**
     * 查询量具点检完成率
     */
    @Anonymous
    @ApiOperation("查询量具点检完成率")
    @GetMapping("/measuringToolInspectionCompletionRate")
    public AjaxResult measuringToolInspectionCompletionRate(@RequestParam("line") String line) {
        return syncStatService.measuringToolInspectionCompletionRate(line);
    }

    /**
     * 查询排产完成情况
     */
    @Anonymous
    @ApiOperation("查询排产完成情况")
    @GetMapping("/productionComplete")
    public AjaxResult productionComplete(@RequestParam("line") String line) {
        return AjaxResult.success(syncStatService.productionComplete(line, null));
    }

    /**
     * 查询当天生产进度
     */
    @Anonymous
    @ApiOperation("查询当天生产进度")
    @GetMapping("/toDayProductionSchedule")
    public AjaxResult toDayProductionSchedule(@RequestParam("line") String line) {
        return syncStatService.toDayProductionSchedule(line, null);
    }

    /**
     * 每小时产出和实际理论差异
     */
    @Anonymous
    @ApiOperation("每小时产出和实际理论差异")
    @GetMapping("/everyHourProduce")
    public AjaxResult everyHourProduce(@RequestParam("line") String line) {
        return AjaxResult.success(syncStatService.everyHourProduce(line, null,null));
    }

    /**
     * 当前生产订单明细
     */
    @Anonymous
    @ApiOperation("当前生产订单明细")
    @GetMapping("/currentProduceOrderInfo")
    public AjaxResult currentProduceOrderInfo(@RequestParam("line") String line) {
        return syncStatService.currentProduceOrderInfo(line);
    }

    /**
     * 查询各个线体到岗情况
     */
    @Anonymous
    @ApiOperation("查询各个线体到岗情况")
    @GetMapping("/lineOnDuty")
    public AjaxResult lineOnDuty(@RequestParam("line") String line) {
        return syncStatService.lineOnDuty(line);
    }

    /**
     * 查询服务器时间
     */
    @Anonymous
    @ApiOperation("查询服务器时间")
    @GetMapping("/getTime")
    public AjaxResult getTime() {
        return AjaxResult.success(DateUtils.getNowDate().getTime());
    }

    /**
     * 查询耐压测试数据
     */
    @Anonymous
    @ApiOperation("查询耐压测试数据")
    @GetMapping("/getPressureResistanceTestData")
    public AjaxResult getPressureResistanceTestData(@RequestParam("line") String line) {
        return syncStatService.getPressureResistanceTestData(line);
    }

    /**
     * 查询条形T线测试数据
     */
    @Anonymous
    @ApiOperation("查询条形T线测试数据")
    @GetMapping("/getBarTLineTestData")
    public AjaxResult getBarTLineTestData(@RequestParam("line") String line) {
        return syncStatService.getBarTLineTestData(line);
    }


}
