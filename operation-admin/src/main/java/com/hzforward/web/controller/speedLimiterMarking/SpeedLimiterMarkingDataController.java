package com.hzforward.web.controller.speedLimiterMarking;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.speedLimiterMarking.domain.SpeedLimiterMarkingData;
import com.hzforward.speedLimiterMarking.service.ISpeedLimiterMarkingDataService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 打标数据Controller
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RestController
@RequestMapping("/speedLimiterMarking/markingData")
public class SpeedLimiterMarkingDataController extends BaseController
{
    @Resource
    private ISpeedLimiterMarkingDataService speedLimiterMarkingDataService;

    /**
     * 查询打标数据列表
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:markingData:list')")
    @GetMapping("/list")
    public TableDataInfo list(SpeedLimiterMarkingData speedLimiterMarkingData)
    {
        startPage();
        List<SpeedLimiterMarkingData> list = speedLimiterMarkingDataService.selectSpeedLimiterMarkingDataList(speedLimiterMarkingData);
        return getDataTable(list);
    }

    /**
     * 导出打标数据列表
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:markingData:export')")
    @Log(title = "打标数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SpeedLimiterMarkingData speedLimiterMarkingData)
    {
        List<SpeedLimiterMarkingData> list = speedLimiterMarkingDataService.selectSpeedLimiterMarkingDataList(speedLimiterMarkingData);
        ExcelUtil<SpeedLimiterMarkingData> util = new ExcelUtil<>(SpeedLimiterMarkingData.class);
        util.exportExcel(response, list, "打标数据数据");
    }

    /**
     * 获取打标数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:markingData:query')")
    @GetMapping(value = "/getInfoById/{markingId}")
    public AjaxResult getInfo(@PathVariable("markingId") Long markingId)
    {
        return AjaxResult.success(speedLimiterMarkingDataService.selectSpeedLimiterMarkingDataByMarkingId(markingId));
    }

    /**
     * 新增打标数据
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:markingData:add')")
    @Log(title = "打标数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SpeedLimiterMarkingData speedLimiterMarkingData)
    {
        return toAjax(speedLimiterMarkingDataService.insertSpeedLimiterMarkingData(speedLimiterMarkingData));
    }

    /**
     * 修改打标数据
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:markingData:edit')")
    @Log(title = "打标数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SpeedLimiterMarkingData speedLimiterMarkingData)
    {
        return toAjax(speedLimiterMarkingDataService.updateSpeedLimiterMarkingData(speedLimiterMarkingData));
    }

    /**
     * 删除打标数据
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:markingData:remove')")
    @Log(title = "打标数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{markingIds}")
    public AjaxResult remove(@PathVariable List<Long> markingIds)
    {
        return toAjax(speedLimiterMarkingDataService.deleteSpeedLimiterMarkingDataByMarkingIds(markingIds));
    }

    @Anonymous
    @GetMapping("/{productCode}")
    public AjaxResult selectSpeedLimiterMarkingData(@PathVariable String productCode)
    {
        return AjaxResult.success(speedLimiterMarkingDataService.selectSpeedLimiterMarkingData(productCode));
    }

    @Anonymous
    @PutMapping("/{markingId}&{printResults}")
    public AjaxResult selectSpeedLimiterMarkingData(@PathVariable Long markingId, @PathVariable String printResults)
    {
        return AjaxResult.success(speedLimiterMarkingDataService.pushMarkingPrintResults(markingId, printResults));
    }
}
