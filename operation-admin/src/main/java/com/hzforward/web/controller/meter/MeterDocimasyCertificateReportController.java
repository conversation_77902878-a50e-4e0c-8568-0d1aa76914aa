package com.hzforward.web.controller.meter;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.meter.domain.MeterDocimasyCertificateReport;
import com.hzforward.meter.service.IMeterDocimasyCertificateReportService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 量具检定证书报告Controller
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@RestController
@RequestMapping("/meter/docimasyCertificateReport")
public class MeterDocimasyCertificateReportController extends BaseController
{
    @Resource
    private IMeterDocimasyCertificateReportService meterDocimasyCertificateReportService;

    /**
     * 查询量具检定证书报告列表
     */
    @PreAuthorize("@ss.hasPermi('meter:docimasyCertificateReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeterDocimasyCertificateReport meterDocimasyCertificateReport)
    {
        startPage();
        List<MeterDocimasyCertificateReport> list = meterDocimasyCertificateReportService.selectMeterDocimasyCertificateReportList(meterDocimasyCertificateReport);
        return getDataTable(list);
    }

    /**
     * 导出量具检定证书报告列表
     */
    @PreAuthorize("@ss.hasPermi('meter:docimasyCertificateReport:export')")
    @Log(title = "量具检定证书报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeterDocimasyCertificateReport meterDocimasyCertificateReport)
    {
        List<MeterDocimasyCertificateReport> list = meterDocimasyCertificateReportService.selectMeterDocimasyCertificateReportList(meterDocimasyCertificateReport);
        ExcelUtil<MeterDocimasyCertificateReport> util = new ExcelUtil<>(MeterDocimasyCertificateReport.class);
        util.exportExcel(response, list, "量具检定证书报告数据");
    }

    /**
     * 获取量具检定证书报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('meter:docimasyCertificateReport:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(meterDocimasyCertificateReportService.selectMeterDocimasyCertificateReportById(id));
    }

    /**
     * 新增量具检定证书报告
     */
    @PreAuthorize("@ss.hasPermi('meter:docimasyCertificateReport:add')")
    @Log(title = "量具检定证书报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeterDocimasyCertificateReport meterDocimasyCertificateReport)
    {
        return toAjax(meterDocimasyCertificateReportService.insertMeterDocimasyCertificateReport(meterDocimasyCertificateReport));
    }

    /**
     * 修改量具检定证书报告
     */
    @PreAuthorize("@ss.hasPermi('meter:docimasyCertificateReport:edit')")
    @Log(title = "量具检定证书报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MeterDocimasyCertificateReport meterDocimasyCertificateReport)
    {
        return toAjax(meterDocimasyCertificateReportService.updateMeterDocimasyCertificateReport(meterDocimasyCertificateReport));
    }

    /**
     * 删除量具检定证书报告
     */
    @PreAuthorize("@ss.hasPermi('meter:docimasyCertificateReport:remove')")
    @Log(title = "量具检定证书报告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(meterDocimasyCertificateReportService.deleteMeterDocimasyCertificateReportByIds(ids));
    }

    /**
     * 下载文件
     */
    @Anonymous
    @PreAuthorize("@ss.hasPermi('meter:docimasyCertificateReport:download')")
    @GetMapping("/downLoad/{id}")
    public void downLoad(HttpServletResponse response, @PathVariable Long id)
    {
        meterDocimasyCertificateReportService.downLoad(response,id);
    }
}
