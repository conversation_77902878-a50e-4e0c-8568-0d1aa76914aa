package com.hzforward.web.controller.meter;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.meter.domain.MeterInspectionReport;
import com.hzforward.meter.domain.MeterInspectionReportDetail;
import com.hzforward.meter.domain.req.MeterDocimasyForm;
import com.hzforward.meter.domain.req.MeterInspectionListReq;
import com.hzforward.meter.service.IMeterInspectionReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 量具点检报告Controller
 *
 * <AUTHOR>
 * @date 2023-12-22
 */
@RestController
@RequestMapping("/meter/report")
public class MeterInspectionReportController extends BaseController
{
    @Resource
    private IMeterInspectionReportService meterInspectionReportService;

    /**
     * 查询量具点检报告列表
     */
    @PreAuthorize("@ss.hasPermi('meter:meterInspectionReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeterInspectionListReq meterInspectionListReq)
    {
        startPage();
        List<MeterInspectionReportDetail> list = meterInspectionReportService.selectMeterInspectionReportList(meterInspectionListReq);
        return getDataTable(list);
    }

    /**
     * 查询量具当天点检列表以及结果
     */
    @PostMapping("/getMeterInspectionReportByDate")
    public AjaxResult selectMeterInspectionReport(@RequestBody MeterInspectionReport meterInspectionReport)
    {
        return  AjaxResult.success(meterInspectionReportService.getMeterInspectionReportByDate(meterInspectionReport));
    }

    /**
     * 导出量具点检报告列表
     */
    @PreAuthorize("@ss.hasPermi('meter:dailyInspectionReport:export')")
    @Log(title = "量具点检报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeterInspectionListReq meterInspectionListReq)
    {
        List<MeterInspectionReportDetail> list = meterInspectionReportService.selectMeterInspectionReportList(meterInspectionListReq);
        ExcelUtil<MeterInspectionReportDetail> util = new ExcelUtil<>(MeterInspectionReportDetail.class);
        util.exportExcel(response, list, "量具点检报告数据");
    }

    /**
     * 获取量具点检报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('meter:dailyInspectionReport:detail')")
    @PostMapping("/selectMeterInspectionReportDetailList")
    public AjaxResult getInspectionReportDetailList(@RequestBody MeterInspectionReport meterInspectionReport)
    {
        return  AjaxResult.success(meterInspectionReportService.selectMeterInspectionReportDetailList(meterInspectionReport));
    }

    /**
     * 新增量具点检报告
     */
    @PreAuthorize("@ss.hasPermi('meter:report:add')")
    @Log(title = "量具点检报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeterInspectionReport meterInspectionReport)
    {
        return toAjax(meterInspectionReportService.insertMeterInspectionReport(meterInspectionReport));
    }

    /**
     * 修改量具点检报告
     */
    @PreAuthorize("@ss.hasPermi('meter:report:edit')")
    @Log(title = "量具点检报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MeterInspectionReport meterInspectionReport)
    {
        return toAjax(meterInspectionReportService.updateMeterInspectionReport(meterInspectionReport));
    }

    /**
     * 删除量具点检报告
     */
    @PreAuthorize("@ss.hasPermi('meter:dailyInspectionReport:remove')")
    @Log(title = "量具点检报告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reportIds}")
    public AjaxResult remove(@PathVariable Long[] reportIds)
    {
        return toAjax(meterInspectionReportService.deleteMeterInspectionReportByReportIds(reportIds));
    }

    /**
     * 处理移动端量具点检请求
     */
    @PostMapping("/meterInspectionReportSubmit")
    public AjaxResult inspectionReportSubmit(@RequestBody MeterDocimasyForm meterDocimasyForm)
    {
        return toAjax(meterInspectionReportService.meterInspectionReportSubmit(meterDocimasyForm));
    }
}
