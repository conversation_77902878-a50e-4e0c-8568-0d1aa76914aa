package com.hzforward.web.controller.meter;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.meter.domain.MeterCertificateReport;
import com.hzforward.meter.service.IMeterCertificateReportService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 量具证书报告Controller
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@RestController
@RequestMapping("/meter/certificateReport")
public class MeterCertificateReportController extends BaseController
{
    @Resource
    private IMeterCertificateReportService meterCertificateReportService;

    /**
     * 查询量具证书报告列表
     */
    @PreAuthorize("@ss.hasPermi('meter:certificateReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeterCertificateReport meterCertificateReport)
    {
        startPage();
        List<MeterCertificateReport> list = meterCertificateReportService.selectMeterCertificateReportList(meterCertificateReport);
        return getDataTable(list);
    }

    /**
     * 导出量具证书报告列表
     */
    @PreAuthorize("@ss.hasPermi('meter:certificateReport:export')")
    @Log(title = "量具证书报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeterCertificateReport meterCertificateReport)
    {
        List<MeterCertificateReport> list = meterCertificateReportService.selectMeterCertificateReportList(meterCertificateReport);
        ExcelUtil<MeterCertificateReport> util = new ExcelUtil<>(MeterCertificateReport.class);
        util.exportExcel(response, list, "量具证书报告数据");
    }

    /**
     * 获取量具证书报告详细信息
     */
    @PreAuthorize("@ss.hasPermi('meter:certificateReport:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(meterCertificateReportService.selectMeterCertificateReportById(id));
    }

    /**
     * 新增量具证书报告
     */
    @PreAuthorize("@ss.hasPermi('meter:certificateReport:add')")
    @Log(title = "量具证书报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeterCertificateReport meterCertificateReport)
    {
        return toAjax(meterCertificateReportService.insertMeterCertificateReport(meterCertificateReport));
    }

    /**
     * 修改量具证书报告
     */
    @PreAuthorize("@ss.hasPermi('meter:certificateReport:edit')")
    @Log(title = "量具证书报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MeterCertificateReport meterCertificateReport)
    {
        return toAjax(meterCertificateReportService.updateMeterCertificateReport(meterCertificateReport));
    }

    /**
     * 删除量具证书报告
     */
    @PreAuthorize("@ss.hasPermi('meter:certificateReport:remove')")
    @Log(title = "量具证书报告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(meterCertificateReportService.deleteMeterCertificateReportByIds(ids));
    }

    /**
     * 下载文件
     */
    @Anonymous
    @GetMapping("/downLoad/{id}")
    public void downLoad(HttpServletResponse response, @PathVariable Long id)
    {
        meterCertificateReportService.downLoad(response,id);
    }
}
