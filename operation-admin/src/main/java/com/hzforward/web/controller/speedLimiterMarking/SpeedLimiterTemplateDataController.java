package com.hzforward.web.controller.speedLimiterMarking;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.speedLimiterMarking.domain.SpeedLimiterTemplateData;
import com.hzforward.speedLimiterMarking.service.ISpeedLimiterTemplateDataService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 模板规则Controller
 *
 * <AUTHOR>
 * @date 2023-06-08
 */
@RestController
@RequestMapping("/speedLimiterMarking/templateData")
public class SpeedLimiterTemplateDataController extends BaseController
{
    @Resource
    private ISpeedLimiterTemplateDataService speedLimiterTemplateDataService;

    /**
     * 查询模板规则列表
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:templateData:list')")
    @GetMapping("/list")
    public TableDataInfo list(SpeedLimiterTemplateData speedLimiterTemplateData)
    {
        startPage();
        List<SpeedLimiterTemplateData> list = speedLimiterTemplateDataService.selectSpeedLimiterTemplateDataList(speedLimiterTemplateData);
        return getDataTable(list);
    }

    /**
     * 导出模板规则列表
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:templateData:export')")
    @Log(title = "模板规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SpeedLimiterTemplateData speedLimiterTemplateData)
    {
        List<SpeedLimiterTemplateData> list = speedLimiterTemplateDataService.selectSpeedLimiterTemplateDataList(speedLimiterTemplateData);
        ExcelUtil<SpeedLimiterTemplateData> util = new ExcelUtil<>(SpeedLimiterTemplateData.class);
        util.exportExcel(response, list, "模板规则数据");
    }

    /**
     * 获取模板规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:templateData:query')")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId)
    {
        return AjaxResult.success(speedLimiterTemplateDataService.selectSpeedLimiterTemplateDataByTemplateId(templateId));
    }

    /**
     * 新增模板规则
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:templateData:add')")
    @Log(title = "模板规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SpeedLimiterTemplateData speedLimiterTemplateData)
    {
        return toAjax(speedLimiterTemplateDataService.insertSpeedLimiterTemplateData(speedLimiterTemplateData));
    }

    /**
     * 修改模板规则
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:templateData:edit')")
    @Log(title = "模板规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SpeedLimiterTemplateData speedLimiterTemplateData)
    {
        return toAjax(speedLimiterTemplateDataService.updateSpeedLimiterTemplateData(speedLimiterTemplateData));
    }

    /**
     * 删除模板规则
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:templateData:remove')")
    @Log(title = "模板规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable List<Long> templateIds)
    {
        return toAjax(speedLimiterTemplateDataService.deleteSpeedLimiterTemplateDataByTemplateIds(templateIds));
    }
}
