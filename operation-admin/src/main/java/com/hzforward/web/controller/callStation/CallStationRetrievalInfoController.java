package com.hzforward.web.controller.callStation;

import com.hzforward.callStation.domain.CallStationRetrievalInfo;
import com.hzforward.callStation.service.ICallStationRetrievalInfoService;
import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.annotation.RepeatSubmit;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 取号信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-25
 */
@RestController
@RequestMapping("/callStation/retrievalInfo")
public class CallStationRetrievalInfoController extends BaseController
{
    @Resource
    private ICallStationRetrievalInfoService callStationRetrievalInfoService;

    /**
     * 查询取号信息列表
     */
    @PreAuthorize("@ss.hasPermi('callStation:retrievalInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(CallStationRetrievalInfo callStationRetrievalInfo)
    {
        startPage();
        List<CallStationRetrievalInfo> list = callStationRetrievalInfoService.selectCallStationRetrievalInfoList(callStationRetrievalInfo);
        return getDataTable(list);
    }

    /**
     * 导出取号信息列表
     */
    @PreAuthorize("@ss.hasPermi('callStation:retrievalInfo:export')")
    @Log(title = "取号信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CallStationRetrievalInfo callStationRetrievalInfo)
    {
        List<CallStationRetrievalInfo> list = callStationRetrievalInfoService.selectCallStationRetrievalInfoList(callStationRetrievalInfo);
        ExcelUtil<CallStationRetrievalInfo> util = new ExcelUtil<>(CallStationRetrievalInfo.class);
        util.exportExcel(response, list, "取号信息数据");
    }

    /**
     * 获取取号信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('callStation:retrievalInfo:query')")
    @GetMapping(value = "/{retrievalId}")
    public AjaxResult getInfo(@PathVariable("retrievalId") Long retrievalId)
    {
        return AjaxResult.success(callStationRetrievalInfoService.selectCallStationRetrievalInfoByRetrievalId(retrievalId));
    }

    /**
     * 新增取号信息
     */
    @Anonymous
    @RepeatSubmit
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CallStationRetrievalInfo callStationRetrievalInfo)
    {
        return AjaxResult.success(callStationRetrievalInfoService.insertCallStationRetrievalInfo(callStationRetrievalInfo));
    }

    /**
     * 修改取号信息
     */
    @PreAuthorize("@ss.hasPermi('callStation:retrievalInfo:edit')")
    @Log(title = "取号信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CallStationRetrievalInfo callStationRetrievalInfo)
    {
        return toAjax(callStationRetrievalInfoService.updateCallStationRetrievalInfo(callStationRetrievalInfo));
    }

    /**
     * 删除取号信息
     */
    @PreAuthorize("@ss.hasPermi('callStation:retrievalInfo:remove')")
    @Log(title = "取号信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{retrievalIds}")
    public AjaxResult remove(@PathVariable List<Long> retrievalIds)
    {
        return toAjax(callStationRetrievalInfoService.deleteCallStationRetrievalInfoByRetrievalIds(retrievalIds));
    }
}
