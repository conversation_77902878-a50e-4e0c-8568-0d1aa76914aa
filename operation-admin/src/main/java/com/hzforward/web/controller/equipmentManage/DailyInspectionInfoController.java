package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.domain.DailyInspectionInfo;
import com.hzforward.equipmentManage.service.IDailyInspectionInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备点检内容Controller
 *
 * <AUTHOR>
 * @date 2023-04-12
 */
@RestController
@RequestMapping("/equipmentManage/dailyInspectionInfo")
public class DailyInspectionInfoController extends BaseController
{
    @Resource
    private IDailyInspectionInfoService dailyInspectionInfoService;

    /**
     * 查询设备点检内容列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(DailyInspectionInfo dailyInspectionInfo)
    {
        startPage();
        List<DailyInspectionInfo> list = dailyInspectionInfoService.selectDailyInspectionInfoList(dailyInspectionInfo);
        return getDataTable(list);
    }

    /**
     * 导出设备点检内容列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionInfo:export')")
    @Log(title = "设备点检内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DailyInspectionInfo dailyInspectionInfo)
    {
        List<DailyInspectionInfo> list = dailyInspectionInfoService.selectDailyInspectionInfoList(dailyInspectionInfo);
        ExcelUtil<DailyInspectionInfo> util = new ExcelUtil<>(DailyInspectionInfo.class);
        util.exportExcel(response, list, "设备点检内容数据");
    }

    /**
     * 获取设备点检内容详细信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionInfo:query')")
    @GetMapping(value = "/{inspectionId}")
    public AjaxResult getInfo(@PathVariable("inspectionId") Long inspectionId)
    {
        return AjaxResult.success(dailyInspectionInfoService.getById(inspectionId));
    }

    /**
     * 新增设备点检内容
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionInfo:add')")
    @Log(title = "设备点检内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DailyInspectionInfo dailyInspectionInfo)
    {
        return toAjax(dailyInspectionInfoService.save(dailyInspectionInfo));
    }

    /**
     * 修改设备点检内容
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionInfo:edit')")
    @Log(title = "设备点检内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DailyInspectionInfo dailyInspectionInfo)
    {
        return toAjax(dailyInspectionInfoService.updateById(dailyInspectionInfo));
    }

    /**
     * 删除设备点检内容
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionInfo:remove')")
    @Log(title = "设备点检内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{inspectionIds}")
    public AjaxResult remove(@PathVariable List<Long> inspectionIds)
    {
        return toAjax(dailyInspectionInfoService.removeBatchByIds(inspectionIds));
    }

    /**
     * 下载设备点检信息模板
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionInfo:import')")
    @PostMapping("/importInspectionInfoTemplate")
    public void importInspectionInfoTemplate(HttpServletResponse response)
    {
        ExcelUtil<DailyInspectionInfo> util = new ExcelUtil<>(DailyInspectionInfo.class);
        util.importTemplateExcel(response, "设备点检信息");
    }

    /**
     * 导入设备设备点检信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:dailyInspectionInfo:import')")
    @Log(title = "设备点检信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importInspectionInfo")
    public AjaxResult importInspectionInfo(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<DailyInspectionInfo> util = new ExcelUtil<>(DailyInspectionInfo.class);
        List<DailyInspectionInfo> dailyInspectionInfoList = util.importExcel(file.getInputStream());
        String message = dailyInspectionInfoService.importDailyInspectionInfo(dailyInspectionInfoList, updateSupport);
        return AjaxResult.success(message);
    }

}
