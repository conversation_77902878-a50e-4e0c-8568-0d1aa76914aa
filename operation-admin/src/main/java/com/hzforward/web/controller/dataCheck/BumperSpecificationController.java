package com.hzforward.web.controller.dataCheck;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.dataCheck.domain.BumperSpecification;
import com.hzforward.dataCheck.service.IBumperSpecificationService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 缓冲器规格Controller
 *
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/dataCheck/bumperSpecification")
public class BumperSpecificationController extends BaseController
{
    @Resource
    private IBumperSpecificationService bumperSpecificationService;

    /**
     * 查询缓冲器规格列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperSpecification:list')")
    @GetMapping("/list")
    public TableDataInfo list(BumperSpecification bumperSpecification)
    {
        startPage();
        List<BumperSpecification> list = bumperSpecificationService.selectBumperSpecificationList(bumperSpecification);
        return getDataTable(list);
    }


    /**
     * 导出缓冲器规格列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperSpecification:export')")
    @Log(title = "缓冲器规格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BumperSpecification bumperSpecification)
    {
        List<BumperSpecification> list = bumperSpecificationService.selectBumperSpecificationList(bumperSpecification);
        ExcelUtil<BumperSpecification> util = new ExcelUtil<>(BumperSpecification.class);
        util.exportExcel(response, list, "缓冲器规格数据");
    }

    /**
     * 获取缓冲器规格详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperSpecification:query')")
    @GetMapping(value = "/{specificationId}")
    public AjaxResult getInfo(@PathVariable("specificationId") Long specificationId)
    {
        return AjaxResult.success(bumperSpecificationService.selectBumperSpecificationBySpecificationId(specificationId));
    }

    /**
     * 获取缓冲器规格详细信息
     */
    @Anonymous
    @GetMapping(value = "getInfoByOrderPartCode/{orderPartCode}")
    public AjaxResult getInfoByOrderPartCode(@PathVariable("orderPartCode") String orderPartCode)
    {
        return AjaxResult.success(bumperSpecificationService.selectBumperSpecificationByOrderPartCode(orderPartCode));
    }

    /**
     * 新增缓冲器规格
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperSpecification:add')")
    @Log(title = "缓冲器规格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BumperSpecification bumperSpecification)
    {
        return toAjax(bumperSpecificationService.insertBumperSpecification(bumperSpecification));
    }

    /**
     * 修改缓冲器规格
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperSpecification:edit')")
    @Log(title = "缓冲器规格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BumperSpecification bumperSpecification)
    {
        return toAjax(bumperSpecificationService.updateBumperSpecification(bumperSpecification));
    }

    /**
     * 删除缓冲器规格
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperSpecification:remove')")
    @Log(title = "缓冲器规格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{specificationIds}")
    public AjaxResult remove(@PathVariable List<Long> specificationIds)
    {
        return toAjax(bumperSpecificationService.deleteBumperSpecificationBySpecificationIds(specificationIds));
    }

    /**
     * 下载缓冲器规格导入模板
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperSpecification:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<BumperSpecification> util = new ExcelUtil<>(BumperSpecification.class);
        util.importTemplateExcel(response, "缓冲器排产信息");
    }

    /**
     * 导入缓冲器规格信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperSpecification:import')")
    @Log(title = "缓冲器规格", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BumperSpecification> util = new ExcelUtil<>(BumperSpecification.class);
        List<BumperSpecification> bumperSpecificationList = util.importExcel(file.getInputStream());
        String message = bumperSpecificationService.importData(bumperSpecificationList, updateSupport);
        return AjaxResult.success(message);
    }
}
