package com.hzforward.web.controller.speedLimiterMarking;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.speedLimiterMarking.domain.SpeedLimiterSpecificationsData;
import com.hzforward.speedLimiterMarking.service.ISpeedLimiterSpecificationsDataService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 限速器打标-限速器规格Controller
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/speedLimiterMarking/specificationsData")
public class SpeedLimiterSpecificationsDataController extends BaseController
{
    @Resource
    private ISpeedLimiterSpecificationsDataService speedLimiterSpecificationsDataService;

    /**
     * 查询限速器打标-限速器规格列表
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:specificationsData:list')")
    @GetMapping("/list")
    public TableDataInfo list(SpeedLimiterSpecificationsData speedLimiterSpecificationsData)
    {
        startPage();
        List<SpeedLimiterSpecificationsData> list = speedLimiterSpecificationsDataService.selectSpeedLimiterSpecificationsDataList(speedLimiterSpecificationsData);
        return getDataTable(list);
    }

    /**
     * 导出限速器打标-限速器规格列表
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:specificationsData:export')")
    @Log(title = "限速器打标-限速器规格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SpeedLimiterSpecificationsData speedLimiterSpecificationsData)
    {
        List<SpeedLimiterSpecificationsData> list = speedLimiterSpecificationsDataService.selectSpeedLimiterSpecificationsDataList(speedLimiterSpecificationsData);
        ExcelUtil<SpeedLimiterSpecificationsData> util = new ExcelUtil<>(SpeedLimiterSpecificationsData.class);
        util.exportExcel(response, list, "限速器打标-限速器规格数据");
    }

    /**
     * 导入限速器打标-限速器规格列表
     */
    @Log(title = "限速器打标-限速器规格", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:specificationsData:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SpeedLimiterSpecificationsData> util = new ExcelUtil<>(SpeedLimiterSpecificationsData.class);
        List<SpeedLimiterSpecificationsData> speedLimiterSpecificationsDataList = util.importExcel(file.getInputStream());
        String message = speedLimiterSpecificationsDataService.importSpecificationsData(speedLimiterSpecificationsDataList, updateSupport);
        return AjaxResult.success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SpeedLimiterSpecificationsData> util = new ExcelUtil<>(SpeedLimiterSpecificationsData.class);
        util.importTemplateExcel(response, "限速器规格");
    }

    /**
     * 获取限速器打标-限速器规格详细信息
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:specificationsData:query')")
    @GetMapping(value = "/{customPartCode}")
    public AjaxResult getInfo(@PathVariable("customPartCode") String customPartCode)
    {
        return AjaxResult.success(speedLimiterSpecificationsDataService.selectSpeedLimiterSpecificationsDataByCustomPartCode(customPartCode));
    }

    /**
     * 新增限速器打标-限速器规格
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:specificationsData:add')")
    @Log(title = "限速器打标-限速器规格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SpeedLimiterSpecificationsData speedLimiterSpecificationsData)
    {
        return toAjax(speedLimiterSpecificationsDataService.insertSpeedLimiterSpecificationsData(speedLimiterSpecificationsData));
    }

    /**
     * 修改限速器打标-限速器规格
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:specificationsData:edit')")
    @Log(title = "限速器打标-限速器规格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SpeedLimiterSpecificationsData speedLimiterSpecificationsData)
    {
        return toAjax(speedLimiterSpecificationsDataService.updateSpeedLimiterSpecificationsData(speedLimiterSpecificationsData));
    }

    /**
     * 删除限速器打标-限速器规格
     */
    @PreAuthorize("@ss.hasPermi('speedLimiterMarking:specificationsData:remove')")
    @Log(title = "限速器打标-限速器规格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{customPartCodes}")
    public AjaxResult remove(@PathVariable List<String> customPartCodes)
    {
        return toAjax(speedLimiterSpecificationsDataService.deleteSpeedLimiterSpecificationsDataByCustomPartCodes(customPartCodes));
    }
}
