package com.hzforward.web.controller.dinerManage;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.dinnerManage.domain.DinnerManageExamineApprove;
import com.hzforward.dinnerManage.service.IDinnerManageExamineApproveService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 用餐审批内容Controller
 *
 * <AUTHOR>
 * @date 2024-03-13
 */
@RestController
@RequestMapping("/dinnerManage/examineApprove")
public class DinnerManageExamineApproveController extends BaseController
{
    @Resource
    private IDinnerManageExamineApproveService dinnerManageExamineApproveService;

    /**
     * 查询用餐审批内容列表
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:examineApprove:list')")
    @GetMapping("/list")
    public TableDataInfo list(DinnerManageExamineApprove dinnerManageExamineApprove)
    {
        startPage();
        List<DinnerManageExamineApprove> list = dinnerManageExamineApproveService.selectDinnerManageExamineApproveList(dinnerManageExamineApprove);
        return getDataTable(list);
    }


    /**
     * 导出用餐审批内容列表
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:examineApprove:export')")
    @Log(title = "用餐审批内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DinnerManageExamineApprove dinnerManageExamineApprove)
    {
        List<DinnerManageExamineApprove> list = dinnerManageExamineApproveService.selectDinnerManageExamineApproveList(dinnerManageExamineApprove);
        ExcelUtil<DinnerManageExamineApprove> util = new ExcelUtil<>(DinnerManageExamineApprove.class);
        util.exportExcel(response, list, "用餐审批内容数据");
    }

    /**
     * 获取用餐审批内容详细信息
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:examineApprove:query')")
    @GetMapping(value = "/{approveId}")
    public AjaxResult getInfo(@PathVariable("approveId") Long approveId)
    {
        return AjaxResult.success(dinnerManageExamineApproveService.selectDinnerManageExamineApproveByApproveId(approveId));
    }

    /**
     * 新增用餐审批内容
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:examineApprove:add')")
    @Log(title = "用餐审批内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DinnerManageExamineApprove dinnerManageExamineApprove)
    {
        return toAjax(dinnerManageExamineApproveService.insertDinnerManageExamineApprove(dinnerManageExamineApprove));
    }

    /**
     * 修改用餐审批内容
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:examineApprove:edit')")
    @Log(title = "用餐审批内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DinnerManageExamineApprove dinnerManageExamineApprove)
    {
        return toAjax(dinnerManageExamineApproveService.updateDinnerManageExamineApprove(dinnerManageExamineApprove));
    }

    /**
     * 删除用餐审批内容
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:examineApprove:remove')")
    @Log(title = "用餐审批内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{approveIds}")
    public AjaxResult remove(@PathVariable List<Long> approveIds)
    {
        return toAjax(dinnerManageExamineApproveService.deleteDinnerManageExamineApproveByApproveIds(approveIds));
    }

    @Anonymous
    @GetMapping("/examineApproveOver")
    public void examineApproveOver(@RequestParam("processInstanceId") String processInstanceId) {
        dinnerManageExamineApproveService.examineApproveOver(processInstanceId);
    }

    @GetMapping("/manuallySynchronizingProcessInformation")
    public void manuallySynchronizingProcessInformation() {
        dinnerManageExamineApproveService.manuallySynchronizingProcessInformation();
    }

}
