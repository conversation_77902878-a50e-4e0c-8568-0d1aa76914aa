package com.hzforward.web.controller.dataCheck;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.dataCheck.domain.CertificationInfo;
import com.hzforward.dataCheck.service.ICertificationInfoService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 认证清单Controller
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
@RestController
@RequestMapping("/dataCheck/certificationInfo")
public class CertificationInfoController extends BaseController
{
    @Resource
    private ICertificationInfoService certificationInfoService;

    /**
     * 查询认证清单列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:certificationInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(CertificationInfo certificationInfo)
    {
        startPage();
        List<CertificationInfo> list = certificationInfoService.selectCertificationInfoList(certificationInfo);
        return getDataTable(list);
    }

    /**
     * 导出认证清单列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:certificationInfo:export')")
    @Log(title = "认证清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CertificationInfo certificationInfo)
    {
        List<CertificationInfo> list = certificationInfoService.selectCertificationInfoList(certificationInfo);
        ExcelUtil<CertificationInfo> util = new ExcelUtil<>(CertificationInfo.class);
        util.exportExcel(response, list, "认证清单数据");
    }

	/**
	 * 获取认证清单详细信息
	 */
	@PreAuthorize("@ss.hasPermi('dataCheck:certificationInfo:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id)
	{
		return AjaxResult.success(certificationInfoService.getById(id));
	}

    /**
     * 新增认证清单
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:certificationInfo:add')")
    @Log(title = "认证清单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CertificationInfo certificationInfo)
    {
        return toAjax(certificationInfoService.addCertificateinfo(certificationInfo));
    }

    /**
     * 修改认证清单
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:certificationInfo:edit')")
    @Log(title = "认证清单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CertificationInfo certificationInfo)
    {
        return toAjax(certificationInfoService.updateInfo(certificationInfo));
    }

    /**
     * 删除认证清单
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:certificationInfo:remove')")
    @Log(title = "认证清单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(certificationInfoService.removeBatchByIds(ids));
    }

    /**
     * 导入认证清单数据
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:certificationInfo:import')")
    @Log(title = "导入数据", businessType = BusinessType.INSERT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<CertificationInfo> util = new ExcelUtil<>(CertificationInfo.class);
        List<CertificationInfo> importCertificationInfoList = util.importExcel(file.getInputStream());
        String message = certificationInfoService.importCertificationInfo(importCertificationInfoList);
        return AjaxResult.success(message);
    }
}
