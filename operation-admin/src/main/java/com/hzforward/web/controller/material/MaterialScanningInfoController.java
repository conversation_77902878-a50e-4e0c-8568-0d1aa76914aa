package com.hzforward.web.controller.material;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.material.domain.MaterialScanningInfo;
import com.hzforward.material.service.IMaterialScanningInfoService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 物料扫描数据Controller
 *
 * <AUTHOR>
 * @date 2022-03-25
 */
@RestController
@RequestMapping("/material/materialScanningInfo")
public class MaterialScanningInfoController extends BaseController
{
    @Resource
    private IMaterialScanningInfoService materialScanningInfoService;

    /**
     * 查询物料扫描数据列表
     */
    @PreAuthorize("@ss.hasPermi('material:materialScanningInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaterialScanningInfo materialScanningInfo)
    {
        startPage();
        List<MaterialScanningInfo> list = materialScanningInfoService.selectMaterialScanningInfoList(materialScanningInfo);
        return getDataTable(list);
    }

    /**
     * 导出物料扫描数据列表
     */
    @PreAuthorize("@ss.hasPermi('material:materialScanningInfo:export')")
    @Log(title = "物料扫描", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaterialScanningInfo materialScanningInfo)
    {
        List<MaterialScanningInfo> list = materialScanningInfoService.exportMaterialScanningInfoList(materialScanningInfo);
        ExcelUtil<MaterialScanningInfo> util = new ExcelUtil<>(MaterialScanningInfo.class);
        util.exportExcel(response, list, "物料扫描数据数据");
    }

    /**
     * 获取物料扫描数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:materialScanningInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(materialScanningInfoService.selectMaterialScanningInfoById(id));
    }

    /**
     * 新增物料扫描数据
     */
    @PreAuthorize("@ss.hasPermi('material:materialScanningInfo:add')")
    @Log(title = "物料扫描", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialScanningInfo materialScanningInfo)
    {
        return toAjax(materialScanningInfoService.insertMaterialScanningInfo(materialScanningInfo));
    }

    /**
     * 修改物料扫描数据
     */
    @PreAuthorize("@ss.hasPermi('material:materialScanningInfo:edit')")
    @Log(title = "物料扫描", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialScanningInfo materialScanningInfo)
    {
        return toAjax(materialScanningInfoService.updateMaterialScanningInfo(materialScanningInfo));
    }

    /**
     * 删除物料扫描数据
     */
    @PreAuthorize("@ss.hasPermi('material:materialScanningInfo:remove')")
    @Log(title = "物料扫描", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(materialScanningInfoService.deleteMaterialScanningInfoByIds(ids));
    }

}
