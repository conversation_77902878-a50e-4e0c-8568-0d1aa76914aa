package com.hzforward.web.controller.dataCenter;


import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.dataCenter.domain.ProductionPlanDetail;
import com.hzforward.dataCenter.service.IProductionPlanService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 产线计划controller
 *
 * <AUTHOR>
 * @date 2022-07-21
 */
@RestController
@RequestMapping("/dataCenter/productionPlan")
public class ProductionPlanController extends BaseController
{
    @Resource
    private IProductionPlanService productionPlanService;

    /**
     * 查询产线计划报表数据
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:productionPlan:reportList')")
    @GetMapping("/reportList")
    public AjaxResult reportList(@RequestParam(value="planPackDate") String planPackDate)
    {
        return AjaxResult.success(productionPlanService.selectProductionPlanReportList(planPackDate));
    }

    /**
     * 查询产线计划详情数据
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:productionPlan:detailList')")
    @GetMapping("/detailList")
    public TableDataInfo detailList(ProductionPlanDetail productionPlanDetail)
    {
        startPage();
        List<ProductionPlanDetail> list = productionPlanService.selectProductionPlanDetailList(productionPlanDetail);
        return getDataTable(list);
    }

    /**
     * 查询产线计划详情数据
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:productionPlan:detailList')")
    @GetMapping("/wmsData")
    public AjaxResult getWMSData(@RequestParam(value="orderNo") String orderNo,@RequestParam(value="contractNo") String contractNo)
    {
        return AjaxResult.success(productionPlanService.selectProductionPlanDetailWMSData(orderNo,contractNo));
    }

    /**
     * 导出产线计划详情数据
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:productionPlan:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductionPlanDetail productionPlanDetail)
    {
        List<ProductionPlanDetail> list = productionPlanService.exportProductionPlanDetail(productionPlanDetail);
        ExcelUtil<ProductionPlanDetail> util = new ExcelUtil<>(ProductionPlanDetail.class);
        util.exportExcel(response, list, "产线计划数据");
    }


}
