package com.hzforward.web.controller.dataReview.brakeSystem;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.dataReview.brakeSystem.domain.AssembleInfo;
import com.hzforward.dataReview.brakeSystem.service.IAssembleInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 制动器装配信息Controller
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/dataReview/assembleInfo")
public class AssembleInfoController extends BaseController
{
    @Resource
    private IAssembleInfoService assembleInfoService;

	/**
	 * 获取制动器装配信息详细信息
	 */
	@PreAuthorize("@ss.hasPermi('dataReview:assembleInfo:query')")
	@GetMapping(value = "/{assembleId}")
	public AjaxResult getInfo(@PathVariable("assembleId") Long assembleId)
	{
		return AjaxResult.success(assembleInfoService.selectAssembleInfoByAssembleId(assembleId));
	}

    /**
     * 查询制动器装配信息列表
     */
    @PreAuthorize("@ss.hasPermi('dataReview:assembleInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssembleInfo assembleInfo)
    {
        startPage();
        List<AssembleInfo> list = assembleInfoService.selectAssembleInfoList(assembleInfo);
        return getDataTable(list);
    }

    /**
     * 查询制动器装配信息列表
     */
    @GetMapping("/getAssembleInfoByFactoryNumber/{factoryNumber}")
    public AjaxResult selectAssembleInfoListByFactoryNumber(@PathVariable String factoryNumber)
    {
        return AjaxResult.success(assembleInfoService.selectAssembleInfoListByFactoryNumber(factoryNumber));
    }

    /**
     * 导出制动器装配信息列表
     */
    @PreAuthorize("@ss.hasPermi('dataReview:assembleInfo:export')")
    @Log(title = "制动器装配信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssembleInfo assembleInfo)
    {
        List<AssembleInfo> list = assembleInfoService.selectAssembleInfoList(assembleInfo);
        ExcelUtil<AssembleInfo> util = new ExcelUtil<>(AssembleInfo.class);
        util.exportExcel(response, list, "制动器装配信息数据");
    }

    /**
     * 新增制动器装配信息
     */
    @PreAuthorize("@ss.hasPermi('dataReview:assembleInfo:add')")
    @Log(title = "制动器装配信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssembleInfo assembleInfo)
    {
        return toAjax(assembleInfoService.insertAssembleInfo(assembleInfo));
    }

    /**
     * 修改制动器装配信息
     */
    @PreAuthorize("@ss.hasPermi('dataReview:assembleInfo:edit')")
    @Log(title = "制动器装配信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssembleInfo assembleInfo)
    {
        return toAjax(assembleInfoService.updateAssembleInfo(assembleInfo));
    }

    /**
     * 删除制动器装配信息
     */
    @PreAuthorize("@ss.hasPermi('dataReview:assembleInfo:remove')")
    @Log(title = "制动器装配信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{assembleIds}")
    public AjaxResult remove(@PathVariable List<Long> assembleIds)
    {
        return toAjax(assembleInfoService.deleteAssembleInfoByAssembleIds(assembleIds));
    }

    /**
     * 部件装配
     */
    @Log(title = "制动器装配信息", businessType = BusinessType.INSERT)
    @PostMapping("/plateAssemble")
    public AjaxResult plateAssemble(@RequestBody AssembleInfo assembleInfo)
    {
        return toAjax(assembleInfoService.plateAssemble(assembleInfo));
    }

    /**
     * 手动提交部件装配信息
     */
    @Log(title = "制动器装配信息", businessType = BusinessType.INSERT)
    @PostMapping("/handleSubmitAssembleInfo")
    public AjaxResult handleSubmitAssembleInfo(@RequestBody List<AssembleInfo> assembleInfoList)
    {
        return toAjax(assembleInfoService.handleSubmitAssembleInfo(assembleInfoList));
    }
}
