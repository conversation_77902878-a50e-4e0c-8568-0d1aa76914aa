package com.hzforward.web.controller.partCode;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.annotation.RepeatSubmit;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.partCode.domain.PartCodeDetail;
import com.hzforward.partCode.domain.PartCodeOaApply;
import com.hzforward.partCode.service.IPartCodeDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 件号详情Controller
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@RestController
@RequestMapping("/partCode/partCodeDetail")
public class PartCodeDetailController extends BaseController
{
    @Resource
    private IPartCodeDetailService partCodeDetailService;

    /**
     * 查询件号详情列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeDetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartCodeDetail partCodeDetail)
    {
        startPage();
        List<PartCodeDetail> list = partCodeDetailService.selectPartCodeDetailList(partCodeDetail);
        return getDataTable(list);
    }

    /**
     * 导出件号详情列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeDetail:export')")
    @Log(title = "件号管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PartCodeDetail partCodeDetail)
    {
        List<PartCodeDetail> list = partCodeDetailService.selectPartCodeDetailList(partCodeDetail);
        ExcelUtil<PartCodeDetail> util = new ExcelUtil<>(PartCodeDetail.class);
        util.exportExcel(response, list, "件号详情数据");
    }

    /**
     * 获取件号详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeDetail:query')")
    @GetMapping(value = "/{partId}")
    public AjaxResult getInfo(@PathVariable("partId") Long partId)
    {
        return AjaxResult.success(partCodeDetailService.selectPartCodeDetailByPartId(partId));
    }

    /**
     * 新增件号详情
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeDetail:add')")
    @Log(title = "件号管理", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody @RequestParam(value="fileList" ,required=false) MultipartFile[] fileList, PartCodeDetail partCodeDetail)
    {
        return toAjax(partCodeDetailService.createPartCodeDetail(fileList,partCodeDetail));
    }

    /**
     * 修改件号详情
     */
    @PreAuthorize("@ss.hasAnyPermi('partCode:partCodeDetail:edit,partCode:partCodeDetail:selfEdit')")
    @Log(title = "件号管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody @RequestParam(value="fileList" ,required=false) MultipartFile[] fileList, PartCodeDetail partCodeDetail)
    {
        return toAjax(partCodeDetailService.updatePartCodeDetail(fileList,partCodeDetail));
    }

    /**
     * 修改件号OA状态/使用状态
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeDetail:edit')")
    @Log(title = "件号管理", businessType = BusinessType.UPDATE)
    @PutMapping("/partState")
    public AjaxResult updatePartState(@RequestBody PartCodeDetail partCodeDetail)
    {
        return toAjax(partCodeDetailService.updatePartCodeDetail(null,partCodeDetail));
    }

    /**
     * 删除件号详情
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeDetail:remove')")
    @Log(title = "件号管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{partIds}")
    public AjaxResult remove(@PathVariable Long[] partIds)
    {
        return toAjax(partCodeDetailService.deletePartCodeDetailByPartIds(partIds));
    }

    /**
     * 删除件号详情
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeDetail:selfRemove')")
    @Log(title = "件号管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/selfRemove/{partIds}")
    public AjaxResult selfRemove(@PathVariable Long[] partIds)
    {
        return toAjax(partCodeDetailService.deletePartCodeDetailByPartIds(partIds));
    }

    /**
     * 件号详情导出
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeDetail:exportPartCodeDetail')")
    @Log(title = "件号管理", businessType = BusinessType.EXPORT)
    @PostMapping("/exportPartCodeDetailByPartId")
    public void exportPartCodeDetailByPartId(Long partId, HttpServletResponse response)
    {
        partCodeDetailService.exportPartCodeDetailByPartId(partId,response);
    }

    /**
     * OA件号申请
     */
    @RepeatSubmit(interval = 30000, message="为防止重复提交OA申请,OA申请需间隔30秒")
    @Log(title = "件号管理", businessType = BusinessType.UPDATE)
    @PostMapping("/oaApply")
    public AjaxResult oaApply(@RequestBody PartCodeOaApply partCodeOaApply)
    {
        return partCodeDetailService.oaApply(partCodeOaApply);
    }

    /**
     * 查询曳引轮是否符合规则
     */
    @PostMapping("/validationTractionWheel")
    public AjaxResult validationTractionWheel(@RequestBody PartCodeDetail partCodeDetail)
    {
        partCodeDetailService.validationTractionWheel(partCodeDetail);
        return AjaxResult.success();
    }

}
