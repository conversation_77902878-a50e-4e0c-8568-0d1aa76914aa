package com.hzforward.web.controller.meter;

import com.github.pagehelper.PageInfo;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.meter.domain.MeterMaintainHistory;
import com.hzforward.meter.domain.req.MeterMaintainHistoryListReq;
import com.hzforward.meter.domain.res.MeterMaintainHistoryListRes;
import com.hzforward.meter.service.IMeterMaintainHistoryService;
import javax.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 量具维修记录Controller
 *
 * <AUTHOR>
 * @date 2023-03-10
 */
@RestController
@RequestMapping("/meter/maintainHistory")
public class MeterMaintainHistoryController extends BaseController
{
    @Resource
    private IMeterMaintainHistoryService meterMaintainHistoryService;

    /**
     * 查询量具维修记录列表
     */
    @PreAuthorize("@ss.hasPermi('meter:maintainHistory:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeterMaintainHistoryListReq meterMaintainHistoryListReq)
    {
        startPage();
        PageInfo<MeterMaintainHistoryListRes> list = meterMaintainHistoryService.selectMeterMaintainHistoryList(meterMaintainHistoryListReq);
        return getDataTable(list);
    }

    /**
     * 导出量具维修记录列表
     */
//    @PreAuthorize("@ss.hasPermi('meter:maintainHistory:export')")
//    @Log(title = "量具维修记录", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, MeterMaintainHistory meterMaintainHistory)
//    {
//        List<MeterMaintainHistory> list = meterMaintainHistoryService.selectMeterMaintainHistoryList(meterMaintainHistory);
//        ExcelUtil<MeterMaintainHistory> util = new ExcelUtil<>(MeterMaintainHistory.class);
//        util.exportExcel(response, list, "量具维修记录数据");
//    }

    /**
     * 获取量具维修记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('meter:maintainHistory:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(meterMaintainHistoryService.selectMeterMaintainHistoryById(id));
    }

    /**
     * 新增量具维修记录
     */
    @PreAuthorize("@ss.hasPermi('meter:maintainHistory:add')")
    @Log(title = "量具维修记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeterMaintainHistory meterMaintainHistory)
    {
        return toAjax(meterMaintainHistoryService.insertMeterMaintainHistory(meterMaintainHistory));
    }

    /**
     * 修改量具维修记录
     */
    @PreAuthorize("@ss.hasPermi('meter:maintainHistory:edit')")
    @Log(title = "量具维修记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MeterMaintainHistory meterMaintainHistory)
    {
        return toAjax(meterMaintainHistoryService.updateMeterMaintainHistory(meterMaintainHistory));
    }

    /**
     * 删除量具维修记录
     */
    @PreAuthorize("@ss.hasPermi('meter:maintainHistory:remove')")
    @Log(title = "量具维修记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(meterMaintainHistoryService.deleteMeterMaintainHistoryByIds(ids));
    }
}
