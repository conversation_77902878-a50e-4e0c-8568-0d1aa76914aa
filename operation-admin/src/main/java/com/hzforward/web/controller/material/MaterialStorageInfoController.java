package com.hzforward.web.controller.material;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.material.domain.MaterialStorageInfo;
import com.hzforward.material.service.IMaterialStorageInfoService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 物料仓储信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-16
 */
@RestController
@RequestMapping("/material/storageInfo")
public class MaterialStorageInfoController extends BaseController
{
    @Resource
    private IMaterialStorageInfoService materialStorageInfoService;

    /**
     * 查询物料仓储信息列表
     */
    @PreAuthorize("@ss.hasPermi('material:storageInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaterialStorageInfo materialStorageInfo)
    {
        startPage();
        List<MaterialStorageInfo> list = materialStorageInfoService.selectMaterialStorageInfoList(materialStorageInfo);
        return getDataTable(list);
    }


    /**
     * 导出物料仓储信息列表
     */
    @PreAuthorize("@ss.hasPermi('material:storageInfo:export')")
    @Log(title = "物料仓储信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaterialStorageInfo materialStorageInfo)
    {
        List<MaterialStorageInfo> list = materialStorageInfoService.selectMaterialStorageInfoList(materialStorageInfo);
        ExcelUtil<MaterialStorageInfo> util = new ExcelUtil<>(MaterialStorageInfo.class);
        util.exportExcel(response, list, "物料仓储信息数据");
    }

    /**
     * 获取物料仓储信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:storageInfo:query')")
    @GetMapping(value = "/{storageId}")
    public AjaxResult getInfo(@PathVariable("storageId") Long storageId)
    {
        return AjaxResult.success(materialStorageInfoService.selectMaterialStorageInfoByStorageId(storageId));
    }

    /**
     * 获取物料仓储信息详细信息
     */
    @GetMapping(value = "/getInfoByPartCode/{partCode}")
    public AjaxResult getInfoByPartCode(@PathVariable("partCode") String partCode)
    {
        return AjaxResult.success(materialStorageInfoService.selectMaterialStorageInfoByPartCode(partCode));
    }

    /**
     * 新增物料仓储信息
     */
    @PreAuthorize("@ss.hasPermi('material:storageInfo:add')")
    @Log(title = "物料仓储信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialStorageInfo materialStorageInfo)
    {
        return toAjax(materialStorageInfoService.insertMaterialStorageInfo(materialStorageInfo));
    }

    /**
     * 修改物料仓储信息
     */
    @PreAuthorize("@ss.hasPermi('material:storageInfo:edit')")
    @Log(title = "物料仓储信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialStorageInfo materialStorageInfo)
    {
        return toAjax(materialStorageInfoService.updateMaterialStorageInfo(materialStorageInfo));
    }

    /**
     * 删除物料仓储信息
     */
    @PreAuthorize("@ss.hasPermi('material:storageInfo:remove')")
    @Log(title = "物料仓储信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{storageIds}")
    public AjaxResult remove(@PathVariable List<Long> storageIds)
    {
        return toAjax(materialStorageInfoService.deleteMaterialStorageInfoByStorageIds(storageIds));
    }

    /*
     * 导出导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<MaterialStorageInfo> util = new ExcelUtil<>(MaterialStorageInfo.class);
        util.importTemplateExcel(response, "物料仓储信息");
    }

    /**
     * 导入物料仓储信息
     */
    @PreAuthorize("@ss.hasPermi('material:storageInfo:import')")
    @Log(title = "物料仓储信息", businessType = BusinessType.OTHER)
    @PostMapping("/import")
    public AjaxResult importMaterialStorageInfo(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<MaterialStorageInfo> util = new ExcelUtil<>(MaterialStorageInfo.class);
        List<MaterialStorageInfo> materialStorageInfoList = util.importExcel(file.getInputStream());
        String message = materialStorageInfoService.importMaterialStorageInfo(materialStorageInfoList, updateSupport);
        return AjaxResult.success(message);
    }

    @InitBinder  //类初始化是调用的方法注解
    public void initBinder(WebDataBinder binder) {
        binder.setAutoGrowNestedPaths(true);
        //给这个controller配置接收list的长度100000，仅在这个controller有效
        binder.setAutoGrowCollectionLimit(10000);
    }

    /**
     * 导出看板数据
     */
    @PreAuthorize("@ss.hasPermi('material:storageInfo:export')")
    @Log(title = "物料采购", businessType = BusinessType.EXPORT)
    @PostMapping("/exportBulletinBoard")
    public void exportBulletinBoard(HttpServletResponse response, MaterialStorageInfo materialStorageInfo){
        materialStorageInfoService.exportBulletinBoard(response, materialStorageInfo.getStorageIds());
    }
}
