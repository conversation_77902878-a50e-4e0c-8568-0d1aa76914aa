package com.hzforward.web.controller.equipmentManage;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.equipmentManage.domain.MaintenanceInfo;
import com.hzforward.equipmentManage.service.IMaintenanceInfoService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 设备保养Controller
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@RestController
@RequestMapping("/equipmentManage/maintenanceInfo")
public class MaintenanceInfoController extends BaseController
{
    @Resource
    private IMaintenanceInfoService maintenanceInfoService;

    /**
     * 查询设备保养列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintenanceInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaintenanceInfo maintenanceInfo)
    {
        startPage();
        List<MaintenanceInfo> list = maintenanceInfoService.selectMaintenanceInfoList(maintenanceInfo);
        return getDataTable(list);
    }

    /**
     * 导出设备保养列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintenanceInfo:export')")
    @Log(title = "设备保养", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaintenanceInfo maintenanceInfo)
    {
        List<MaintenanceInfo> list = maintenanceInfoService.selectMaintenanceInfoList(maintenanceInfo);
        ExcelUtil<MaintenanceInfo> util = new ExcelUtil<>(MaintenanceInfo.class);
        util.exportExcel(response, list, "设备保养数据");
    }

	/**
	 * 获取设备保养详细信息
	 */
	@PreAuthorize("@ss.hasPermi('equipmentManage:maintenanceInfo:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id)
	{
		return AjaxResult.success(maintenanceInfoService.getById(id));
	}

    /**
     * 新增设备保养
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintenanceInfo:add')")
    @Log(title = "设备保养", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaintenanceInfo maintenanceInfo)
    {
        return toAjax(maintenanceInfoService.save(maintenanceInfo));
    }

    /**
     * 修改设备保养
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintenanceInfo:edit')")
    @Log(title = "设备保养", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaintenanceInfo maintenanceInfo)
    {
        return toAjax(maintenanceInfoService.updateById(maintenanceInfo));
    }

    /**
     * 删除设备保养
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintenanceInfo:remove')")
    @Log(title = "设备保养", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(maintenanceInfoService.removeBatchByIds(ids));
    }
}
