package com.hzforward.web.controller.callStation;

import com.hzforward.callStation.domain.CallStationUserInfo;
import com.hzforward.callStation.service.ICallStationUserInfoService;
import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.RepeatSubmit;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 * @date 2023-09-06
 */
@RestController
@RequestMapping("/callStation/userInfo")
public class CallStationUserInfoController extends BaseController
{
    @Resource
    private ICallStationUserInfoService callStationUserInfoService;


    /**
     * 获取用户信息详细信息
     */
    @Anonymous
    @GetMapping(value = "/getInfoByWechatOpenId/{wechatOpenId}")
    public AjaxResult getInfoByWechatOpenId(@PathVariable("wechatOpenId") String wechatOpenId)
    {
        return AjaxResult.success(callStationUserInfoService.selectCallStationUserInfoByWechatOpenId(wechatOpenId));
    }

    @Anonymous
    @PostMapping(value = "/setUserInfo")
    @RepeatSubmit
    public AjaxResult setUserInfo(@RequestBody CallStationUserInfo callStationUserInfo) {
        return toAjax(callStationUserInfoService.setUserInfo(callStationUserInfo));
    }

}
