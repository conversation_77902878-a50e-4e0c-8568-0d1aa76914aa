package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.SecurityUtils;
import com.hzforward.equipmentManage.domain.repair.RepairMaintainer;
import com.hzforward.equipmentManage.service.IRepairMaintainerService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 维修人信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-12
 */
@RestController
@RequestMapping("/equipmentManage/maintainer")
public class RepairMaintainerController extends BaseController
{
    @Resource
    private IRepairMaintainerService repairMaintainerService;

    /**
     * 查询维修人信息列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainer:list')")
    @GetMapping("/list")
    public TableDataInfo list(RepairMaintainer repairMaintainer)
    {
        startPage();
        List<RepairMaintainer> list = repairMaintainerService.selectRepairMaintainerList(repairMaintainer);
        return getDataTable(list);
    }

    /**
     * 获取维修单人员列表
     */
    @GetMapping( "/getMaintainerDict")
    public AjaxResult getMaintainerDict(){
        return AjaxResult.success(repairMaintainerService.getMaintainerDict());
    }

    /**
     * 新增维修人信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainer:add')")
    @Log(title = "维修人信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RepairMaintainer repairMaintainer)
    {
        repairMaintainerService.addRepairMaintainer(repairMaintainer);
        return AjaxResult.success();
    }

    /**
     * 删除维修人信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainer:remove')")
    @Log(title = "维修人信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{maintainerId}")
    public AjaxResult remove(@PathVariable String maintainerId)
    {
        repairMaintainerService.deleteRepairMaintainer(maintainerId);
        return AjaxResult.success();
    }

    /**
     * 校验维修人是否在工作
     */
    @GetMapping( "/checkUserIsWorking")
    public AjaxResult checkUserIsWorking(){
        String maintainerId = SecurityUtils.getLoginUser().getUsername();
        return AjaxResult.success(repairMaintainerService.checkUserIsWorking(maintainerId));
    }

}
