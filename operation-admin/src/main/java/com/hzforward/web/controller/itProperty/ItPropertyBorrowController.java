package com.hzforward.web.controller.itProperty;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.itProperty.domain.ItPropertyBorrow;
import com.hzforward.itProperty.service.IItPropertyBorrowService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 资产借还Controller
 *
 * <AUTHOR>
 * @date 2022-06-20
 */
@RestController
@RequestMapping("/itProperty/itPropertyBorrow")
public class ItPropertyBorrowController extends BaseController
{
    @Resource
    private IItPropertyBorrowService itPropertyBorrowService;

    /**
     * 查询资产借还列表
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyBorrow:list')")
    @GetMapping("/list")
    public TableDataInfo list(ItPropertyBorrow itPropertyBorrow)
    {
        startPage();
        List<ItPropertyBorrow> list = itPropertyBorrowService.selectItPropertyBorrowList(itPropertyBorrow);
        return getDataTable(list);
    }

    /**
     * 导出资产借还列表
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyBorrow:export')")
    @Log(title = "资产管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ItPropertyBorrow itPropertyBorrow)
    {
        List<ItPropertyBorrow> list = itPropertyBorrowService.selectItPropertyBorrowList(itPropertyBorrow);
        ExcelUtil<ItPropertyBorrow> util = new ExcelUtil<>(ItPropertyBorrow.class);
        util.exportExcel(response, list, "资产借还数据");
    }

    /**
     * 获取资产借还详细信息
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyBorrow:query')")
    @GetMapping(value = "/{borrowId}")
    public AjaxResult getInfo(@PathVariable("borrowId") Long borrowId)
    {
        return AjaxResult.success(itPropertyBorrowService.selectItPropertyBorrowByBorrowId(borrowId));
    }

    /**
     * 新增资产借还
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyBorrow:add')")
    @Log(title = "资产管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ItPropertyBorrow itPropertyBorrow)
    {
        return toAjax(itPropertyBorrowService.insertItPropertyBorrow(itPropertyBorrow));
    }

    /**
     * 修改资产借还
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyBorrow:edit')")
    @Log(title = "资产管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ItPropertyBorrow itPropertyBorrow)
    {
        return toAjax(itPropertyBorrowService.updateItPropertyBorrow(itPropertyBorrow));
    }

    /**
     * 修改资产借还
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyBorrow:edit')")
    @Log(title = "资产管理", businessType = BusinessType.UPDATE)
    @PostMapping("/returnProperty")
    public AjaxResult returnProperty(@RequestBody ItPropertyBorrow itPropertyBorrow)
    {
        return toAjax(itPropertyBorrowService.returnProperty(itPropertyBorrow));
    }


    /**
     * 删除资产借还
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyBorrow:remove')")
    @Log(title = "资产管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{borrowIds}")
    public AjaxResult remove(@PathVariable Long[] borrowIds)
    {
        return toAjax(itPropertyBorrowService.deleteItPropertyBorrowByBorrowIds(borrowIds));
    }


}
