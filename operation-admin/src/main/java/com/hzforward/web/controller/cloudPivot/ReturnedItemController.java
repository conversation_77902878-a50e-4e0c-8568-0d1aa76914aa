package com.hzforward.web.controller.cloudPivot;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.cp.domain.ContractReviewInfo;
import com.hzforward.cp.service.ReviewReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 评审证书报告Controller
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@RestController
@RequestMapping("/cloudPivot/reviewReport")
public class ReturnedItemController extends BaseController {
    @Resource
    private ReviewReportService reviewReportService;
    /**
     * 证书报告上传
     */
    @PreAuthorize("@ss.hasPermi('cloudPivot:reviewReport:uploadFile')")
    @Log(title = "证书报告上传", businessType = BusinessType.IMPORT)
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(MultipartFile[] fileList)
    {
        return toAjax(reviewReportService.uploadFile(fileList));
    }

    /**
     * 查询量具证书报告列表
     */
    @PreAuthorize("@ss.hasPermi('cloudPivot:reviewReport:fileList')")
    @GetMapping("/list")
    public TableDataInfo list()
    {
        startPage();
        List<ContractReviewInfo> list = reviewReportService.selectContractReviewFileList();
        return getDataTable(list);
    }


    /**
     * 下载文件
     */
    @Anonymous
    @GetMapping("/downLoad/{id}")
    public void downLoad(HttpServletResponse response, @PathVariable Long id)
    {
        reviewReportService.downLoad(response,id);
    }

    /**
     * 获取量具管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('cloudPivot:reviewReport:geturl')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(reviewReportService.getFileUrlByRefid(id));
    }
}
