package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.domain.LedgerInfo;
import com.hzforward.equipmentManage.service.ILedgerInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备台账数据Controller
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@RestController
@RequestMapping("/equipmentManage/ledgerInfo")
public class ledgerInfoController extends BaseController
{
    @Resource
    private ILedgerInfoService ledgerInfoService;

    /**
     * 查询设备台账数据列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(LedgerInfo ledgerInfo)
    {
        startPage();
        List<LedgerInfo> list = ledgerInfoService.selectLedgerInfoList(ledgerInfo);
        return getDataTable(list);
    }

    @GetMapping("/dict")
    public AjaxResult getLedgerInfoDict(LedgerInfo ledgerInfo){
        return AjaxResult.success(ledgerInfoService.getLedgerInfoDict(ledgerInfo));
    }

    /**
     * 导出设备台账数据列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:export')")
    @Log(title = "设备台账数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LedgerInfo ledgerInfo)
    {
        List<LedgerInfo> list = ledgerInfoService.exportLedgerInfo(ledgerInfo);
        ExcelUtil<LedgerInfo> util = new ExcelUtil<>(LedgerInfo.class);
        util.exportExcel(response, list, "设备台账数据数据");
    }

    /**
     * 获取设备台账数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:query')")
    @GetMapping(value = "/{equipId}")
    public AjaxResult getInfo(@PathVariable("equipId") Long equipId)
    {
        return AjaxResult.success(ledgerInfoService.getById(equipId));
    }

    /**
     * 查询设备台账数据
     */
    @GetMapping("/getInfoByEquipNo/{equipNo}")
    public AjaxResult getInfoByEquipNo(@PathVariable("equipNo") String equipNo)
    {
        return AjaxResult.success(ledgerInfoService.getInfoByEquipNo(equipNo));
    }

    /**
     * 新增设备台账数据
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:add')")
    @Log(title = "设备台账数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LedgerInfo ledgerInfo)
    {
        ledgerInfoService.checkEquipNoUnique(ledgerInfo.getEquipNo());
        return toAjax(ledgerInfoService.save(ledgerInfo));
    }

    /**
     * 修改设备台账数据
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:edit')")
    @Log(title = "设备台账数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LedgerInfo ledgerInfo)
    {
        return toAjax(ledgerInfoService.updateById(ledgerInfo));
    }

    /**
     * 修改设备编号
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:edit')")
    @Log(title = "设备台账数据", businessType = BusinessType.UPDATE)
    @PutMapping("/updateEquipNo/{oldEquipNo}&{newEquipNo}")
    public AjaxResult updateEquipNo(@PathVariable String oldEquipNo, @PathVariable String newEquipNo)
    {
        ledgerInfoService.updateEquipNo(oldEquipNo, newEquipNo);
        return AjaxResult.success();
    }

    /**
     * 删除设备台账数据
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:remove')")
    @Log(title = "设备台账数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{equipIds}")
    public AjaxResult remove(@PathVariable List<Long> equipIds)
    {
        return toAjax(ledgerInfoService.removeBatchByIds(equipIds));
    }

    /**
     * 下载设备台账信息模板
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:import')")
    @PostMapping("/importLedgerInfoTemplate")
    public void importLedgerInfoTemplate(HttpServletResponse response)
    {
        ExcelUtil<LedgerInfo> util = new ExcelUtil<>(LedgerInfo.class);
        util.importTemplateExcel(response, "设备台账信息");
    }

    /**
     * 导入设备台账信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:ledgerInfo:import')")
    @Log(title = "设备台账数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importLedgerInfo")
    public AjaxResult importLedgerInfo(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<LedgerInfo> util = new ExcelUtil<>(LedgerInfo.class);
        List<LedgerInfo> ledgerInfoList = util.importExcel(file.getInputStream());
        String message = ledgerInfoService.importLedgerInfo(ledgerInfoList, updateSupport);
        return AjaxResult.success(message);
    }
}
