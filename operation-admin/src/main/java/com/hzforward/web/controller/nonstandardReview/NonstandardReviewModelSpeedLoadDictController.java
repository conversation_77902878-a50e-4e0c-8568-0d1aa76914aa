package com.hzforward.web.controller.nonstandardReview;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.nonstandardReview.domain.NonstandardReviewModelSpeedLoadDict;
import com.hzforward.nonstandardReview.service.INonstandardReviewModelSpeedLoadDictService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户ODF字典Controller
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@RestController
@RequestMapping("/nonstandardReview/modelSpeedLoadDict")
public class NonstandardReviewModelSpeedLoadDictController extends BaseController
{
    @Resource
    private INonstandardReviewModelSpeedLoadDictService nonstandardReviewModelSpeedLoadDictService;

    /**
     * 查询客户ODF字典列表
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:modelSpeedLoadDict:list')")
    @GetMapping("/list")
    public TableDataInfo list(NonstandardReviewModelSpeedLoadDict nonstandardReviewModelSpeedLoadDict)
    {
        startPage();
        List<NonstandardReviewModelSpeedLoadDict> list = nonstandardReviewModelSpeedLoadDictService.selectNonstandardReviewModelSpeedLoadDictList(nonstandardReviewModelSpeedLoadDict);
        return getDataTable(list);
    }

    /**
     * 根据已选择的机型载重速度设置 可选范围
     */
    @Anonymous
    @GetMapping("/getModelSpeedLoadDict")
    public AjaxResult getModelSpeedLoadDict(NonstandardReviewModelSpeedLoadDict nonstandardReviewModelSpeedLoadDict)
    {
        return AjaxResult.success(nonstandardReviewModelSpeedLoadDictService.getModelSpeedLoadDict(nonstandardReviewModelSpeedLoadDict));
    }


    /**
     * 导出客户ODF字典列表
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:modelSpeedLoadDict:export')")
    @Log(title = "客户ODF字典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NonstandardReviewModelSpeedLoadDict nonstandardReviewModelSpeedLoadDict)
    {
        List<NonstandardReviewModelSpeedLoadDict> list = nonstandardReviewModelSpeedLoadDictService.selectNonstandardReviewModelSpeedLoadDictList(nonstandardReviewModelSpeedLoadDict);
        ExcelUtil<NonstandardReviewModelSpeedLoadDict> util = new ExcelUtil<>(NonstandardReviewModelSpeedLoadDict.class);
        util.exportExcel(response, list, "客户ODF字典数据");
    }

    /**
     * 获取客户ODF字典详细信息
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:modelSpeedLoadDict:query')")
    @GetMapping(value = "/{dictId}")
    public AjaxResult getInfo(@PathVariable("dictId") Long dictId)
    {
        return AjaxResult.success(nonstandardReviewModelSpeedLoadDictService.selectNonstandardReviewModelSpeedLoadDictByDictId(dictId));
    }

    /**
     * 新增客户ODF字典
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:modelSpeedLoadDict:add')")
    @Log(title = "客户ODF字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NonstandardReviewModelSpeedLoadDict nonstandardReviewModelSpeedLoadDict)
    {
        return toAjax(nonstandardReviewModelSpeedLoadDictService.insertNonstandardReviewModelSpeedLoadDict(nonstandardReviewModelSpeedLoadDict));
    }

    /**
     * 修改客户ODF字典
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:modelSpeedLoadDict:edit')")
    @Log(title = "客户ODF字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NonstandardReviewModelSpeedLoadDict nonstandardReviewModelSpeedLoadDict)
    {
        return toAjax(nonstandardReviewModelSpeedLoadDictService.updateNonstandardReviewModelSpeedLoadDict(nonstandardReviewModelSpeedLoadDict));
    }

    /**
     * 删除客户ODF字典
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:modelSpeedLoadDict:remove')")
    @Log(title = "客户ODF字典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dictIds}")
    public AjaxResult remove(@PathVariable List<Long> dictIds)
    {
        return toAjax(nonstandardReviewModelSpeedLoadDictService.deleteNonstandardReviewModelSpeedLoadDictByDictIds(dictIds));
    }

    /**
     * 下载客户ODF字典模板
     */
    @PostMapping("/importModelSpeedLoadDictTemplate")
    public void importBaseInfoTemplate(HttpServletResponse response)
    {
        ExcelUtil<NonstandardReviewModelSpeedLoadDict> util = new ExcelUtil<>(NonstandardReviewModelSpeedLoadDict.class);
        util.importTemplateExcel(response, "客户ODF字典");
    }

    /**
     * 导入客户ODF字典
     */
    @Log(title = "客户ODF字典", businessType = BusinessType.IMPORT)
    @PostMapping("/importModelSpeedLoadDict")
    public AjaxResult importModelSpeedLoadDict(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<NonstandardReviewModelSpeedLoadDict> util = new ExcelUtil<>(NonstandardReviewModelSpeedLoadDict.class);
        List<NonstandardReviewModelSpeedLoadDict> modelSpeedLoadDictList = util.importExcel(file.getInputStream());
        nonstandardReviewModelSpeedLoadDictService.importModelSpeedLoadDict(modelSpeedLoadDictList, updateSupport);
        return AjaxResult.success();
    }
}
