package com.hzforward.web.controller.processCheck;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.process.domain.ProcessDailyInspectionProjectReportDetail;
import com.hzforward.process.service.IProcessDailyInspectionProjectReportDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 工序巡检报告Controller
 *
 * <AUTHOR>
 * @date 2024-07-23
 */
@RestController
@RequestMapping("/processCheck/processDailyInspectionReport")
public class ProcessDailyInspectionProjectReportDetailController extends BaseController
{
    @Resource
    private IProcessDailyInspectionProjectReportDetailService processDailyInspectionProjectReportDetailService;

	/**
	 * 获取工序巡检报告详细信息
	 */
	@PreAuthorize("@ss.hasPermi('processCheck:processDailyInspectionReport:query')")
	@GetMapping(value = "/{reportDetailId}")
	public AjaxResult getInfo(@PathVariable("reportDetailId") Long reportDetailId)
	{
		return AjaxResult.success(processDailyInspectionProjectReportDetailService.selectProcessDailyInspectionProjectReportDetailByReportDetailId(reportDetailId));
	}

    /**
     * 查询工序巡检报告列表
     */
    @PreAuthorize("@ss.hasPermi('processCheck:processDailyInspectionReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessDailyInspectionProjectReportDetail processDailyInspectionProjectReportDetail)
    {
        startPage();
        List<ProcessDailyInspectionProjectReportDetail> list = processDailyInspectionProjectReportDetailService.selectProcessDailyInspectionProjectReportDetailList(processDailyInspectionProjectReportDetail);
        return getDataTable(list);
    }

    /**
     * 导出工序巡检报告列表
     */
    @PreAuthorize("@ss.hasPermi('processCheck:processDailyInspectionReport:export')")
    @Log(title = "工序巡检报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcessDailyInspectionProjectReportDetail processDailyInspectionProjectReportDetail)
    {
        List<ProcessDailyInspectionProjectReportDetail> list = processDailyInspectionProjectReportDetailService.selectProcessDailyInspectionProjectReportDetailList(processDailyInspectionProjectReportDetail);
        ExcelUtil<ProcessDailyInspectionProjectReportDetail> util = new ExcelUtil<>(ProcessDailyInspectionProjectReportDetail.class);
        util.exportExcel(response, list, "工序巡检报告数据");
    }

    /**
     * 新增工序巡检报告
     */
    @PreAuthorize("@ss.hasPermi('processCheck:processDailyInspectionReport:add')")
    @Log(title = "工序巡检报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessDailyInspectionProjectReportDetail processDailyInspectionProjectReportDetail)
    {
        return toAjax(processDailyInspectionProjectReportDetailService.insertProcessDailyInspectionProjectReportDetail(processDailyInspectionProjectReportDetail));
    }

    /**
     * 修改工序巡检报告
     */
//    @PreAuthorize("@ss.hasPermi('processCheck:processDailyInspectionReport:edit')")
//    @Log(title = "工序巡检报告", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody ProcessDailyInspectionProjectReportDetail processDailyInspectionProjectReportDetail)
//    {
//        return toAjax(processDailyInspectionProjectReportDetailService.updateProcessDailyInspectionProjectReportDetail(processDailyInspectionProjectReportDetail));
//    }

    /**
     * 删除工序巡检报告
     */
    @PreAuthorize("@ss.hasPermi('processCheck:processDailyInspectionReport:remove')")
    @Log(title = "工序巡检报告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reportDetailIds}")
    public AjaxResult remove(@PathVariable List<Long> reportDetailIds)
    {
        return toAjax(processDailyInspectionProjectReportDetailService.deleteProcessDailyInspectionProjectReportDetailByReportDetailIds(reportDetailIds));
    }

    @PreAuthorize("@ss.hasPermi('processCheck:processDailyInspectionReport:editDetail')")
    @GetMapping("/detail")
    public ProcessDailyInspectionProjectReportDetail getInspectionDetailInfo(Long inspectionId,Long reportId)
    {
        return processDailyInspectionProjectReportDetailService.getInspectionDetailInfo(inspectionId,reportId);
    }

    /**
     * 修改工序巡检报告
     */
    @PreAuthorize("@ss.hasPermi('processCheck:processDailyInspectionReport:edit')")
    @Log(title = "工序巡检报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult editDetail(@RequestBody ProcessDailyInspectionProjectReportDetail processDailyInspectionProjectReportDetail)
    {
        return toAjax(processDailyInspectionProjectReportDetailService.updateDailyInspectionReportDetail(processDailyInspectionProjectReportDetail));
    }
}
