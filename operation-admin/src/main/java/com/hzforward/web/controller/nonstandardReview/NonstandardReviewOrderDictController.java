package com.hzforward.web.controller.nonstandardReview;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.nonstandardReview.domain.NonstandardReviewOrderDict;
import com.hzforward.nonstandardReview.service.INonstandardReviewOrderDictService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户ODF字典Controller
 *
 * <AUTHOR>
 * @date 2024-03-09
 */
@RestController
@RequestMapping("/nonstandardReview/orderDict")
public class NonstandardReviewOrderDictController extends BaseController
{
    @Resource
    private INonstandardReviewOrderDictService nonstandardReviewOrderDictService;

    /**
     * 查询客户ODF字典列表
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderDict:list')")
    @GetMapping("/list")
    public TableDataInfo list(NonstandardReviewOrderDict nonstandardReviewOrderDict)
    {
        startPage();
        List<NonstandardReviewOrderDict> list = nonstandardReviewOrderDictService.selectNonstandardReviewOrderDictList(nonstandardReviewOrderDict);
        return getDataTable(list);
    }

    /**
     * 获取所有客户填写PDF表时的字典表
     */
    @Anonymous
    @GetMapping("/getAllOrderDict")
    public AjaxResult getAllOrderDict()
    {
        return AjaxResult.success(nonstandardReviewOrderDictService.getAllOrderDict());
    }


    /**
     * 导出客户ODF字典列表
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderDict:export')")
    @Log(title = "客户ODF字典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NonstandardReviewOrderDict nonstandardReviewOrderDict)
    {
        List<NonstandardReviewOrderDict> list = nonstandardReviewOrderDictService.selectNonstandardReviewOrderDictList(nonstandardReviewOrderDict);
        ExcelUtil<NonstandardReviewOrderDict> util = new ExcelUtil<>(NonstandardReviewOrderDict.class);
        util.exportExcel(response, list, "客户ODF字典数据");
    }

    /**
     * 获取客户ODF字典详细信息
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderDict:query')")
    @GetMapping(value = "/{dictId}")
    public AjaxResult getInfo(@PathVariable("dictId") Long dictId)
    {
        return AjaxResult.success(nonstandardReviewOrderDictService.selectNonstandardReviewOrderDictByDictId(dictId));
    }

    /**
     * 新增客户ODF字典
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderDict:add')")
    @Log(title = "客户ODF字典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NonstandardReviewOrderDict nonstandardReviewOrderDict)
    {
        return toAjax(nonstandardReviewOrderDictService.insertNonstandardReviewOrderDict(nonstandardReviewOrderDict));
    }

    /**
     * 修改客户ODF字典
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderDict:edit')")
    @Log(title = "客户ODF字典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NonstandardReviewOrderDict nonstandardReviewOrderDict)
    {
        return toAjax(nonstandardReviewOrderDictService.updateNonstandardReviewOrderDict(nonstandardReviewOrderDict));
    }

    /**
     * 删除客户ODF字典
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderDict:remove')")
    @Log(title = "客户ODF字典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dictIds}")
    public AjaxResult remove(@PathVariable List<Long> dictIds)
    {
        return toAjax(nonstandardReviewOrderDictService.deleteNonstandardReviewOrderDictByDictIds(dictIds));
    }
}
