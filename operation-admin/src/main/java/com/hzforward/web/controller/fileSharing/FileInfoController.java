package com.hzforward.web.controller.fileSharing;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.fileSharing.domain.FileInfo;
import com.hzforward.fileSharing.service.IFileInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件共享Controller
 *
 * <AUTHOR>
 * @date 2022-06-06
 */
@RestController
@RequestMapping("/fileSharing/fileInfo")
public class FileInfoController extends BaseController
{
    @Resource
    private IFileInfoService fileInfoService;

    /**
     * 查询文件列表
     */
    @Anonymous
    @GetMapping("/list")
    public TableDataInfo list(FileInfo fileInfo)
    {
        startPage();
        List<FileInfo> list = fileInfoService.selectFileInfoList(fileInfo);
        return getDataTable(list);
    }

    /**
     * 查询主机说明书主文件夹id
     */
    @Anonymous
    @GetMapping("/getInstructionsFileId")
    public AjaxResult getInstructionsFileId()
    {
        return AjaxResult.success(fileInfoService.getInstructionsFileId());
    }

    /**
     * 获取文件详细信息
     */
    @Anonymous
    @GetMapping(value = "/{fileId}")
    public AjaxResult getInfo(@PathVariable("fileId") Long fileId)
    {
        return AjaxResult.success(fileInfoService.selectFileInfoByFileId(fileId));
    }

    /**
     * 新建文件夹
     */
    @PreAuthorize("@ss.hasPermi('fileSharing:fileInfo:add')")
    @Log(title = "文件共享", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FileInfo fileInfo)
    {
        return toAjax(fileInfoService.newFolder(fileInfo));
    }

    /**
     * 修改文件(夹)名称
     */
    @PreAuthorize("@ss.hasPermi('fileSharing:fileInfo:edit')")
    @Log(title = "文件共享", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FileInfo fileInfo)
    {
        return toAjax(fileInfoService.updateFileInfo(fileInfo));
    }

    /**
     * 删除文件共享
     */
    @PreAuthorize("@ss.hasPermi('fileSharing:fileInfo:remove')")
    @Log(title = "文件共享", businessType = BusinessType.DELETE)
	@DeleteMapping("/{fileId}")
    public AjaxResult remove(@PathVariable Long fileId)
    {
        return toAjax(fileInfoService.deleteFileInfoByFileId(fileId));
    }

    /**
     * 重构目录
     */
    @PreAuthorize("@ss.hasPermi('fileSharing:fileInfo:refactoringDirectory')")
    @Log(title = "文件共享", businessType = BusinessType.INSERT)
    @GetMapping(value = "/refactoringDirectory")
    public void refactoringDirectory()
    {
        fileInfoService.refactoringDirectory();
    }

    /*
     * 上传文件
     */
    @PreAuthorize("@ss.hasPermi('fileSharing:fileInfo:uploadFile')")
    @Log(title = "文件共享", businessType = BusinessType.IMPORT)
    @PostMapping("/uploadFile")
    public AjaxResult uploadFile(MultipartFile[] fileList, Long parentFileId)
    {
        return toAjax(fileInfoService.uploadFile(fileList, parentFileId));
    }

    /**
     * 下载文件
     */
    @Anonymous
    @GetMapping("/downLoad/{fileId}")
    public void downLoad(HttpServletResponse response, @PathVariable Long fileId)
    {
        fileInfoService.downLoad(response,fileId);
    }

}