package com.hzforward.web.controller.partCode;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.partCode.domain.PartCodeCenterType;
import com.hzforward.partCode.service.IPartCodeCenterTypeService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 中类管理Controller
 *
 * <AUTHOR>
 * @date 2022-04-02
 */
@RestController
@RequestMapping("/partCode/partCodeCenterType")
public class PartCodeCenterTypeController extends BaseController
{
    @Resource
    private IPartCodeCenterTypeService partCodeCenterTypeService;

    /**
     * 查询中类管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCenterType:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartCodeCenterType partCodeCenterType)
    {
        startPage();
        List<PartCodeCenterType> list = partCodeCenterTypeService.selectPartCodeCenterTypeList(partCodeCenterType);
        return getDataTable(list);
    }

    /**
     * 获取中类字典表
     */
    @GetMapping("/dict")
    public AjaxResult getPartCodeCenterTypeDict(PartCodeCenterType partCodeCenterType)
    {
        return AjaxResult.success(partCodeCenterTypeService.getPartCodeCenterTypeDict(partCodeCenterType));
    }

    /**
     * 导出中类管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCenterType:export')")
    @Log(title = "中类管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PartCodeCenterType partCodeCenterType)
    {
        List<PartCodeCenterType> list = partCodeCenterTypeService.selectPartCodeCenterTypeList(partCodeCenterType);
        ExcelUtil<PartCodeCenterType> util = new ExcelUtil<>(PartCodeCenterType.class);
        util.exportExcel(response, list, "中类管理数据");
    }

    /**
     * 获取中类管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCenterType:query')")
    @GetMapping(value = "/{centerTypeId}")
    public AjaxResult getInfo(@PathVariable("centerTypeId") Long centerTypeId)
    {
        return AjaxResult.success(partCodeCenterTypeService.selectPartCodeCenterTypeByCenterTypeId(centerTypeId));
    }

    /**
     * 新增中类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCenterType:add')")
    @Log(title = "中类管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PartCodeCenterType partCodeCenterType)
    {
        return toAjax(partCodeCenterTypeService.insertPartCodeCenterType(partCodeCenterType));
    }

    /**
     * 修改中类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCenterType:edit')")
    @Log(title = "中类管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PartCodeCenterType partCodeCenterType)
    {
        return toAjax(partCodeCenterTypeService.updatePartCodeCenterType(partCodeCenterType));
    }

    /**
     * 删除中类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCenterType:remove')")
    @Log(title = "中类管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{centerTypeIds}")
    public AjaxResult remove(@PathVariable Long[] centerTypeIds)
    {
        return toAjax(partCodeCenterTypeService.deletePartCodeCenterTypeByCenterTypeIds(centerTypeIds));
    }
}
