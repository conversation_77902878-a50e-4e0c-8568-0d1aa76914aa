package com.hzforward.web.controller.partCode;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.partCode.domain.PartCodeSmallType;
import com.hzforward.partCode.service.IPartCodeSmallTypeService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 小类管理Controller
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@RestController
@RequestMapping("/partCode/partCodeSmallType")
public class PartCodeSmallTypeController extends BaseController
{
    @Resource
    private IPartCodeSmallTypeService partCodeSmallTypeService;

    /**
     * 查询小类管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSmallType:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartCodeSmallType partCodeSmallType)
    {
        startPage();
        List<PartCodeSmallType> list = partCodeSmallTypeService.selectPartCodeSmallTypeList(partCodeSmallType);
        return getDataTable(list);
    }

    /**
     * 获取小类管理字典表
     */
    @GetMapping("/dict")
    public AjaxResult getPartCodeSmallTypeDict(PartCodeSmallType partCodeSmallType)
    {
        return AjaxResult.success(partCodeSmallTypeService.getPartCodeSmallTypeDict(partCodeSmallType));
    }

    /**
     * 获取小类管理字典表
     */
    @GetMapping("/tractionWheelDict")
    public AjaxResult getTractionWheelPartCodeSmallTypeDict(PartCodeSmallType partCodeSmallType)
    {
        return AjaxResult.success(partCodeSmallTypeService.getTractionWheelPartCodeSmallTypeDict(partCodeSmallType));
    }



    /**
     * 导出小类管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSmallType:export')")
    @Log(title = "小类管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PartCodeSmallType partCodeSmallType)
    {
        List<PartCodeSmallType> list = partCodeSmallTypeService.selectPartCodeSmallTypeList(partCodeSmallType);
        ExcelUtil<PartCodeSmallType> util = new ExcelUtil<>(PartCodeSmallType.class);
        util.exportExcel(response, list, "小类管理数据");
    }

    /**
     * 获取小类管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSmallType:query')")
    @GetMapping(value = "/{smallTypeId}")
    public AjaxResult getInfo(@PathVariable("smallTypeId") Long smallTypeId)
    {
        return AjaxResult.success(partCodeSmallTypeService.selectPartCodeSmallTypeBySmallTypeId(smallTypeId));
    }

    /**
     * 新增小类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSmallType:add')")
    @Log(title = "小类管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PartCodeSmallType partCodeSmallType)
    {
        return toAjax(partCodeSmallTypeService.insertPartCodeSmallType(partCodeSmallType));
    }

    /**
     * 修改小类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSmallType:edit')")
    @Log(title = "小类管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PartCodeSmallType partCodeSmallType)
    {
        return toAjax(partCodeSmallTypeService.updatePartCodeSmallType(partCodeSmallType));
    }

    /**
     * 删除小类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSmallType:remove')")
    @Log(title = "小类管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{smallTypeIds}")
    public AjaxResult remove(@PathVariable Long[] smallTypeIds)
    {
        return toAjax(partCodeSmallTypeService.deletePartCodeSmallTypeBySmallTypeIds(smallTypeIds));
    }
}
