package com.hzforward.web.controller.monitor;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.system.domain.SysOperateLog;
import com.hzforward.system.service.ISysOperateLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 操作日志记录
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/operateLog")
public class SysOperateLogController extends BaseController
{
    @Resource
    private ISysOperateLogService operateLogService;

    @PreAuthorize("@ss.hasPermi('monitor:operateLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysOperateLog operateLog)
    {
        startPage();
        List<SysOperateLog> list = operateLogService.selectOperateLogList(operateLog);
        return getDataTable(list);
    }

    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('monitor:operateLog:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysOperateLog operateLog)
    {
        List<SysOperateLog> list = operateLogService.selectOperateLogList(operateLog);
        ExcelUtil<SysOperateLog> util = new ExcelUtil<>(SysOperateLog.class);
        util.exportExcel(response, list, "操作日志");
    }

    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @PreAuthorize("@ss.hasPermi('monitor:operateLog:remove')")
    @DeleteMapping("/{operateIds}")
    public AjaxResult remove(@PathVariable List<Long> operateIds)
    {
        return toAjax(operateLogService.deleteOperateLogByIds(operateIds));
    }

    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @PreAuthorize("@ss.hasPermi('monitor:operateLog:remove')")
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        operateLogService.cleanOperateLog();
        return AjaxResult.success();
    }
}
