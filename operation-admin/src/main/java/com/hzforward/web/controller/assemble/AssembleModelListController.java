package com.hzforward.web.controller.assemble;

import com.hzforward.assemble.domain.AssembleModelList;
import com.hzforward.assemble.service.IAssembleModelListService;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 配送机型零件清单Controller
 * 
 * <AUTHOR>
 * @date 2020-05-15
 */
@RestController
@RequestMapping("/assemble/modelList")
public class AssembleModelListController extends BaseController
{
    @Resource
    private IAssembleModelListService assembleModelListService;

    /**
     * 查询配送机型零件清单列表
     */
    @PreAuthorize("@ss.hasPermi('assemble:modelList:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssembleModelList assembleModelList)
    {
        startPage();
        List<AssembleModelList> list = assembleModelListService.selectAssembleModelListList(assembleModelList);
        return getDataTable(list);
    }

    /**
     * 导出配送机型零件清单列表
     */
    @PreAuthorize("@ss.hasPermi('assemble:modelList:export')")
    @Log(title = "配送机型零件清单 ", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(AssembleModelList assembleModelList)
    {
        List<AssembleModelList> list = assembleModelListService.selectAssembleModelListList(assembleModelList);
        ExcelUtil<AssembleModelList> util = new ExcelUtil<>(AssembleModelList.class);
        return util.exportExcel(list, "modelList");
    }

    /**
     * 获取配送机型零件清单详细信息
     */
    @PreAuthorize("@ss.hasPermi('assemble:modelList:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(assembleModelListService.selectAssembleModelListById(id));
    }

    /**
     * 新增配送机型零件清单
     */
    @PreAuthorize("@ss.hasPermi('assemble:modelList:add')")
    @Log(title = "配送机型零件清单 ", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssembleModelList assembleModelList)
    {
        return toAjax(assembleModelListService.insertAssembleModelList(assembleModelList));
    }

    /**
     * 修改配送机型零件清单
     */
    @PreAuthorize("@ss.hasPermi('assemble:modelList:edit')")
    @Log(title = "配送机型零件清单 ", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssembleModelList assembleModelList)
    {
        return toAjax(assembleModelListService.updateAssembleModelList(assembleModelList));
    }

    /**
     * 删除配送机型零件清单
     */
    @PreAuthorize("@ss.hasPermi('assemble:modelList:remove')")
    @Log(title = "配送机型零件清单 ", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(assembleModelListService.deleteAssembleModelListByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('assemble:modelList:import')")
    @Log(title = "配送机型零件清单", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        ExcelUtil<AssembleModelList> util = new ExcelUtil<>(AssembleModelList.class);
        List<AssembleModelList> userList = util.importExcel(file.getInputStream());
        String message = assembleModelListService.importData(userList);
        return AjaxResult.success(message);
    }

    @GetMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<AssembleModelList> util = new ExcelUtil<>(AssembleModelList.class);
        return util.importTemplateExcel("配送机型零件清单数据");
    }
}
