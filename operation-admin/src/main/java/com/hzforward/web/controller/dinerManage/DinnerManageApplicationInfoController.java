package com.hzforward.web.controller.dinerManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.annotation.RepeatSubmit;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.dinnerManage.domain.DinnerManageApplicationInfo;
import com.hzforward.dinnerManage.service.IDinnerManageApplicationInfoService;
import com.hzforward.system.service.ISysDeptService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用餐管理_申请领餐Controller
 *
 * <AUTHOR>
 * @date 2022-12-13
 */
@RestController
@RequestMapping("/dinnerManage/dinnerManageApplicationInfo")
public class DinnerManageApplicationInfoController extends BaseController
{
    @Resource
    private IDinnerManageApplicationInfoService dinnerManageApplicationInfoService;

    @Resource
    private ISysDeptService sysDeptService;

    /**
     * 查询用餐管理_申请领餐列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DinnerManageApplicationInfo dinnerManageApplicationInfo)
    {
        if(StringUtils.isNotNull(dinnerManageApplicationInfo.getQueryDeptId())){
            dinnerManageApplicationInfo.setQueryDeptIdList(sysDeptService.selectDeptIdListByTopDeptId(dinnerManageApplicationInfo.getQueryDeptId()));
        }

        startPage();
        List<DinnerManageApplicationInfo> list = dinnerManageApplicationInfoService.selectDinnerManageApplicationInfoList(dinnerManageApplicationInfo);
        return getDataTable(list);
    }

    /**
     * 导出用餐管理_申请领餐列表
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:dinnerManageApplicationInfo:export')")
    @Log(title = "用餐管理_申请领餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DinnerManageApplicationInfo dinnerManageApplicationInfo)
    {
        if(StringUtils.isNotNull(dinnerManageApplicationInfo.getQueryDeptId())){
            dinnerManageApplicationInfo.setQueryDeptIdList(sysDeptService.selectDeptIdListByTopDeptId(dinnerManageApplicationInfo.getQueryDeptId()));
        }

        List<DinnerManageApplicationInfo> list = dinnerManageApplicationInfoService.selectDinnerManageApplicationInfoList(dinnerManageApplicationInfo);
        ExcelUtil<DinnerManageApplicationInfo> util = new ExcelUtil<>(DinnerManageApplicationInfo.class);
        util.exportExcel(response, list, "用餐管理_申请领餐数据");
    }

    /**
     * 获取用餐管理_申请领餐详细信息
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:dinnerManageApplicationInfo:query')")
    @GetMapping(value = "/{applicationId}")
    public AjaxResult getInfo(@PathVariable("applicationId") Long applicationId)
    {
        return AjaxResult.success(dinnerManageApplicationInfoService.selectDinnerManageApplicationInfoByApplicationId(applicationId));
    }

    /**
     * 新增用餐管理_申请领餐
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:dinnerManageApplicationInfo:add')")
    @Log(title = "用餐管理_申请领餐", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody DinnerManageApplicationInfo dinnerManageApplicationInfo)
    {
        return toAjax(dinnerManageApplicationInfoService.insertDinnerManageApplicationInfo(dinnerManageApplicationInfo));
    }

    /**
     * 修改用餐管理_申请领餐
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:dinnerManageApplicationInfo:edit')")
    @Log(title = "用餐管理_申请领餐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DinnerManageApplicationInfo dinnerManageApplicationInfo)
    {
        return toAjax(dinnerManageApplicationInfoService.updateDinnerManageApplicationInfo(dinnerManageApplicationInfo));
    }

    /**
     * 删除用餐管理_申请领餐
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:dinnerManageApplicationInfo:remove')")
    @Log(title = "用餐管理_申请领餐", businessType = BusinessType.DELETE)
	@DeleteMapping("/{applicationIds}")
    public AjaxResult remove(@PathVariable List<Long> applicationIds)
    {
        return toAjax(dinnerManageApplicationInfoService.deleteDinnerManageApplicationInfoByApplicationIds(applicationIds));
    }

    /**
     * 用餐申请
     */
    @PostMapping(value = "/dinnerApplication")
    @RepeatSubmit
    public AjaxResult dinnerApplication(@RequestBody DinnerManageApplicationInfo dinnerManageApplicationInfo)
    {
        return toAjax(dinnerManageApplicationInfoService.dinnerApplication(dinnerManageApplicationInfo));
    }

    /**
     * 取消用餐申请
     */
    @PutMapping(value = "/cancelDinnerApplication/{applicationId}")
    public AjaxResult cancelDinnerApplication(@PathVariable Long applicationId)
    {
        return toAjax(dinnerManageApplicationInfoService.cancelDinnerApplication(applicationId));
    }

    /**
     * 获取取餐二维码
     */
    @GetMapping(value = "/getPickQrCode/{userName}")
    public AjaxResult getPickQrCode(@PathVariable String userName)
    {
        return AjaxResult.success(dinnerManageApplicationInfoService.getPickQrCode(userName));
    }

    /**
     * 获取用餐总人数
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:dinnerManageApplicationInfo:query')")
    @GetMapping(value = "/getDinnerPickedCounts")
    public AjaxResult getDinnerPickedCounts(DinnerManageApplicationInfo dinnerManageApplicationInfo)
    {
        return AjaxResult.success(dinnerManageApplicationInfoService.getDinnerPickedCounts(dinnerManageApplicationInfo));
    }

    /**
     * 用餐扫描
     */
    @PreAuthorize("@ss.hasAnyPermi('dinnerManage:dinnerManageApplicationInfo:dinnerScanning,dinnerManage:applet:dinnerScanning')")
    @PostMapping(value = "/dinnerScanning")
    public AjaxResult dinnerScanning(@RequestBody DinnerManageApplicationInfo dinnerManageApplicationInfo)
    {
        return AjaxResult.success(dinnerManageApplicationInfoService.dinnerScanning(dinnerManageApplicationInfo));
    }

    /**
     * 手动设置已用餐
     */
    @PostMapping(value = "/setApplicationDinnerPicked")
    @PreAuthorize("@ss.hasPermi('dinnerManage:applet:setApplicationDinnerPicked')")
    @RepeatSubmit
    public AjaxResult setApplicationDinnerPicked(@RequestBody DinnerManageApplicationInfo dinnerManageApplicationInfo)
    {
        return toAjax(dinnerManageApplicationInfoService.setApplicationDinnerPicked(dinnerManageApplicationInfo));
    }

}
