package com.hzforward.web.controller.common;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiProcessinstanceGetRequest;
import com.dingtalk.api.response.OapiProcessinstanceGetResponse;
import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.constant.DingTalkURLConstant;
import com.hzforward.common.utils.aliyun.SendSmsUtil;
import com.hzforward.common.utils.dingtalk.AccessTokenUtil;
import com.hzforward.common.utils.dingtalk.DingCallbackCrypto;
import com.hzforward.visitor.domain.ReservationInfo;
import com.hzforward.visitor.service.IReservationInfoService;
import com.taobao.api.ApiException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 钉钉回调控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/ding")
public class DingCallbackController {
    @Resource
    private IReservationInfoService reservationInfoService;

    private static final Logger log = LoggerFactory.getLogger(DingCallbackController.class);

    // 加解密需要用到的token，在钉钉开发者后台配置的
    private static final String TOKEN = "eMkSPQm4IehvzBuUjpixDpGjFl30L6TWw7tc1ea5yzSTjMiDurn7";
    // 数据加密密钥。用于回调数据的加密，长度固定为43个字符，从a-z, A-Z, 0-9共62个字符中选取
    private static final String ENCODING_AES_KEY = "Tir3vcsDyp3nO9LqqWW0RIMPCRIhaOQqHFTFWggBStY";

    private static final String DING_APP_KEY = "dingp5whrgyqcskv7gs6";

    private static final String COMPANY_CODE = "FWD";

    private static final String DING_APP_SECRET = "eGLefjTBGuPCL0fx3uJiRxZdFkbJ2ytdtNKj6-xXpqja5NkKoEaefG9_t8Wr7Sh3";




    /**
     * 钉钉回调接口
     *
     * @return 处理结果
     */
    @Anonymous
    @PostMapping("/callback")
    public Map<String, String> callback(
            @RequestParam(value = "signature", required = false) String signature,
            @RequestParam(value = "timestamp", required = false) String timestamp,
            @RequestParam(value = "nonce", required = false) String nonce,
            @RequestBody(required = false) JSONObject json)
    {
        log.info("收到钉钉回调请求 - signature: {}, timestamp: {}, nonce: {}, body: {}", 
                signature, timestamp, nonce, json);
        // 5. 返回success的加密数据
        try {
            DingCallbackCrypto callbackCrypto = new DingCallbackCrypto(TOKEN,
                    ENCODING_AES_KEY,
                    DING_APP_KEY);

            // 处理URL验证请求
            if (signature == null || timestamp == null || nonce == null || json == null) {
                log.info("收到URL验证请求");
                Map<String, String> successMap = new HashMap<>();
                successMap.put("success", "true");
                return successMap;
            }

            String encryptMsg = json.getString("encrypt");
            String decryptMsg = callbackCrypto.getDecryptMsg(signature, timestamp, nonce, encryptMsg);

            // 3. 反序列化回调事件json数据
            JSONObject eventJson = JSON.parseObject(decryptMsg);
            String eventType = eventJson.getString("EventType");

            log.info("发生了：" + eventType + "事件");
            log.info(eventJson.toJSONString());

            // 4. 根据EventType分类处理
            if ("check_url".equals(eventType)) {
                // 测试回调url的正确性
                log.info("测试回调url的正确性");
            } else if ("user_add_org".equals(eventType)) {
                log.info("user_add_org");
            } else if ("bpms_task_change".equals(eventType)) {
                long dingUserId;
                String processCode = eventJson.getString("processCode");
                String taskId = eventJson.getString("taskId");
                String processInstanceId = eventJson.getString("processInstanceId");
                String type = eventJson.getString("type");

                // 处理指定流程的审批节点监听
                if ("PROC-87CAED59-00B9-4F4E-B168-5B40DF0F33D1".equals(processCode)) {
                    log.info("监听到指定流程的审批节点变化: processCode={}, type={}, taskId={}", processCode, type, taskId);

                    if ("start".equals(type)) {
                        // 新的审批节点开始，创建提醒任务
                        String approverDingUserId = eventJson.getString("actualActionerId");
                        log.info("新的审批节点开始，创建提醒任务: taskId={}, approverDingUserId={}", taskId, approverDingUserId);
                    } else if ("finish".equals(type)) {
                        // 审批节点完成，标记任务已完成
                        String accessToken = AccessTokenUtil.getTokenByCompany(COMPANY_CODE, DING_APP_KEY, DING_APP_SECRET);
                        DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.PROCESS_INSTANCE_GET);
                        OapiProcessinstanceGetRequest request = new OapiProcessinstanceGetRequest();
                        request.setProcessInstanceId(processInstanceId);
                        OapiProcessinstanceGetResponse response = client.execute(request, accessToken);
                        JSONObject resJson = JSONObject.parseObject(response.getBody());
                        JSONObject processInstance = resJson.getJSONObject("process_instance");
                        JSONArray formComponentValues = processInstance.getJSONArray("form_component_values");
                        log.info("监听到form_component_values:{}", formComponentValues);
                        
                        // 获取申请人信息
                        String hostName = "";
                        String hostDept = "";
                        String hostPhone = "";
                        for (int i = 0; i < formComponentValues.size(); i++) {
                            JSONObject component = formComponentValues.getJSONObject(i);
                            String id = component.getString("id");
                            if ("InnerContactField_1YFZ5HS51L1C0".equals(id)) {
                                hostName = component.getString("value");
                            } else if ("DepartmentField_1K6G8NLFTMDC0".equals(id)) {
                                hostDept = component.getString("value");
                            } else if ("PhoneField_8GHMCQ2WW0O0".equals(id)) {
                                hostPhone = component.getString("value");
                            }
                        }
                        
                        // 使用List存储多行数据
                        List<Map<String, String>> customerDataList = new ArrayList<>();
                        List<ReservationInfo> reservationInfoList = new ArrayList<>();

                        for (int i = 0; i < formComponentValues.size(); i++) {
                            JSONObject component = formComponentValues.getJSONObject(i);
                            String id = component.getString("id");
                            // 获取表格数据
                            if ("TableField_4EDH60GUGDC0".equals(id)) {
                                String value = component.getString("value");
                                JSONArray tableData = JSON.parseArray(value);
                                if (tableData != null && !tableData.isEmpty()) {
                                    // 遍历每一行数据
                                    for (int rowIndex = 0; rowIndex < tableData.size(); rowIndex++) {
                                        JSONObject row = tableData.getJSONObject(rowIndex);
                                        JSONArray rowValues = row.getJSONArray("rowValue");
                                        
                                        Map<String, String> customerData = new HashMap<>();
                                        
                                        for (int j = 0; j < rowValues.size(); j++) {
                                            JSONObject field = rowValues.getJSONObject(j);
                                            String label = field.getString("label");
                                            String fieldValue = field.getString("value");
                                            
                                            if ("客户姓名".equals(label)) {
                                                customerData.put("customerName", fieldValue);
                                            } else if ("联系方式".equals(label)) {
                                                customerData.put("phone", fieldValue);
                                            } else if ("客户单位".equals(label)) {
                                                customerData.put("customerUnit", fieldValue);
                                            }
                                        }
                                        
                                        // 只有当所有必要字段都有值时才添加到列表
                                        if (customerData.containsKey("customerName") && 
                                            customerData.containsKey("phone") && 
                                            customerData.containsKey("customerUnit")) {
                                            customerDataList.add(customerData);
                                            
                                            // 创建预约信息对象
                                            ReservationInfo reservationInfo = new ReservationInfo();
                                            reservationInfo.setVisitorName(customerData.get("customerName"));
                                            reservationInfo.setMobilePhone(customerData.get("phone"));
                                            reservationInfo.setVisitingUnits(customerData.get("customerUnit"));
                                            reservationInfo.setInterviewee(hostName); // 设置被访人
                                            reservationInfo.setVisitedDept(hostDept); // 设置被访部门
                                            reservationInfo.setVisitedPhone(hostPhone); // 设置被访人联系方式
                                            reservationInfoList.add(reservationInfo);
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 批量保存所有预约信息
                        if (!reservationInfoList.isEmpty()) {
                            // 保存预约信息
                            reservationInfoService.saveBatch(reservationInfoList);
                            log.info("批量保存预约信息，共{}条", reservationInfoList.size());
                            
                            // 发送短信，使用保存后的ID作为验证码
                            for (ReservationInfo info : reservationInfoList) {
                                String signName = "浙江西子富沃德";
                                String templateCode = "SMS_485410269";
                                String phoneNumber = info.getMobilePhone();
                                String templateParam = "{\"name\":\"" + info.getVisitorName() + "\",\"code\":\"" + info.getId() + "\"}";
                                SendSmsResponse sendSmsResponse = SendSmsUtil.sendSms(signName, templateCode, phoneNumber, templateParam);
                                log.info("status:{}", sendSmsResponse.statusCode);
                                log.info("body: code={}, message={}, bizId={}, requestId={}",
                                    sendSmsResponse.body.getCode(),
                                    sendSmsResponse.body.getMessage(),
                                    sendSmsResponse.body.getBizId(),
                                    sendSmsResponse.body.getRequestId());
                            }
                        }
                        
                        // 记录所有客户数据
                        for (Map<String, String> customerData : customerDataList) {
                            log.info("监听到客户数据: 客户姓名={}, 联系方式={}, 客户单位={}, 被访人={}",
                                customerData.get("customerName"), 
                                customerData.get("phone"), 
                                customerData.get("customerUnit"),
                                hostName);
                        }
                        
                        log.info("审批节点完成，标记任务已完成: taskId={}", taskId);
                    }
                }

            }

            //5.返回success的加密数据
            Map<String, String> successMap = callbackCrypto.getEncryptedMap("success");
            log.info("successMap {}",successMap);
            return successMap;
        } catch (DingCallbackCrypto.DingTalkEncryptException e) {
            throw new RuntimeException(e);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }

    }

}
