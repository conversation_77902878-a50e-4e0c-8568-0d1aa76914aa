package com.hzforward.web.controller.nonstandardReview;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.nonstandardReview.domain.NonstandardReviewOrderInfo;
import com.hzforward.nonstandardReview.service.INonstandardReviewOrderInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 客户ODFController
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@RestController
@RequestMapping("/nonstandardReview/orderInfo")
public class NonstandardReviewOrderInfoController extends BaseController
{
    @Resource
    private INonstandardReviewOrderInfoService nonstandardReviewOrderInfoService;

    /**
     * 查询客户ODF列表
     */
    @Anonymous
    @GetMapping("/list")
    public TableDataInfo list(NonstandardReviewOrderInfo nonstandardReviewOrderInfo)
    {
        startPage();
        List<NonstandardReviewOrderInfo> list = nonstandardReviewOrderInfoService.selectNonstandardReviewOrderInfoList(nonstandardReviewOrderInfo);
        return getDataTable(list);
    }


    /**
     * 导出客户ODF列表
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderInfo:export')")
    @Log(title = "客户ODF", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NonstandardReviewOrderInfo nonstandardReviewOrderInfo)
    {
        List<NonstandardReviewOrderInfo> list = nonstandardReviewOrderInfoService.selectNonstandardReviewOrderInfoList(nonstandardReviewOrderInfo);
        ExcelUtil<NonstandardReviewOrderInfo> util = new ExcelUtil<>(NonstandardReviewOrderInfo.class);
        util.exportExcel(response, list, "客户ODF数据");
    }

    /**
     * 获取客户ODF详细信息
     */
    @Anonymous
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId)
    {
        return AjaxResult.success(nonstandardReviewOrderInfoService.selectNonstandardReviewOrderInfoByOrderId(orderId));
    }

    /**
     * 新增客户ODF
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderInfo:add')")
    @Log(title = "客户ODF", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NonstandardReviewOrderInfo nonstandardReviewOrderInfo)
    {
        return toAjax(nonstandardReviewOrderInfoService.insertNonstandardReviewOrderInfo(nonstandardReviewOrderInfo));
    }

    /**
     * 微信新增客户ODF
     */
    @Anonymous
    @Log(title = "客户ODF", businessType = BusinessType.INSERT)
    @PostMapping("/wechatAdd")
    public AjaxResult wechatAdd(@RequestBody NonstandardReviewOrderInfo nonstandardReviewOrderInfo)
    {
        return toAjax(nonstandardReviewOrderInfoService.wechatAdd(nonstandardReviewOrderInfo));
    }

    /**
     * 修改客户ODF
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderInfo:edit')")
    @Log(title = "客户ODF", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NonstandardReviewOrderInfo nonstandardReviewOrderInfo)
    {
        return toAjax(nonstandardReviewOrderInfoService.updateNonstandardReviewOrderInfo(nonstandardReviewOrderInfo));
    }

    /**
     * 修改个人ODF表
     */
    @Anonymous
    @Log(title = "客户ODF", businessType = BusinessType.UPDATE)
    @PutMapping("/editOwnOrder")
    public AjaxResult editOwnOrder(@RequestBody NonstandardReviewOrderInfo nonstandardReviewOrderInfo)
    {
        return toAjax(nonstandardReviewOrderInfoService.editOwnOrder(nonstandardReviewOrderInfo));
    }

    /**
     * 删除客户ODF
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderInfo:remove')")
    @Log(title = "客户ODF", businessType = BusinessType.DELETE)
	@DeleteMapping("/{orderIds}")
    public AjaxResult remove(@PathVariable List<Long> orderIds)
    {
        return toAjax(nonstandardReviewOrderInfoService.deleteNonstandardReviewOrderInfoByOrderIds(orderIds));
    }

    /**
     * 修改订单状态
     */
    @PreAuthorize("@ss.hasPermi('nonstandardReview:orderInfo:edit')")
    @Log(title = "件号管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateOrderState")
    public AjaxResult updateOrderState(@RequestBody NonstandardReviewOrderInfo nonstandardReviewOrderInfo)
    {
        return toAjax(nonstandardReviewOrderInfoService.updateNonstandardReviewOrderInfo(nonstandardReviewOrderInfo));
    }
}
