package com.hzforward.web.controller.sync;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.domain.entity.SysDictData;
import com.hzforward.common.utils.DictUtils;
import com.hzforward.sync.service.IStatOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单统计
 */
@Api(tags = {"订单统计"})
@RestController
@RequestMapping("/device/stat")
public class StatDeviceController {

    @Resource
    private IStatOrderService statOrderService;

    /**
     * 获取销售信息
     */
    @Anonymous
    @ApiOperation("获取转子设备动态")
    @GetMapping("/getRotorProcessing")
    public AjaxResult getRotorProcessing() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "rotor_list");
        JSONObject object = new JSONObject();
        object.put("details", JSON.parseArray(details.getRemark()));
        return AjaxResult.success(object);
    }

    /**
     * 获取销售信息
     */
    @Anonymous
    @ApiOperation("获取转子日产出")
    @GetMapping("/getProducedDay")
    public AjaxResult getProducedDay() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "produced_day_list");
        JSONObject object = new JSONObject();
        JSONObject jsonObj = JSON.parseObject(details.getRemark());
        object.put("timeList", jsonObj.getJSONArray("timeList"));
        object.put("oneList", jsonObj.getJSONArray("oneList"));
        return AjaxResult.success(object);
    }

    /**
     * 主轴转速倍率
     */
    @Anonymous
    @ApiOperation("主轴转速倍率")
    @GetMapping("/getMainShaftSpeedMagnification")
    public AjaxResult getMainShaftSpeedMagnification() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "main_shaft_speed_magnification");
        JSONObject object = new JSONObject();
        JSONObject jsonObj = JSON.parseObject(details.getRemark());
        object.put("timeList", jsonObj.getJSONArray("timeList"));
        object.put("oneList", jsonObj.getJSONArray("oneList"));
        object.put("twoList", jsonObj.getJSONArray("twoList"));
        object.put("threeList", jsonObj.getJSONArray("threeList"));
        object.put("fourList", jsonObj.getJSONArray("fourList"));
        return AjaxResult.success(object);
    }

    /**
     * 设备明细
     */
    @Anonymous
    @ApiOperation("设备明细")
    @GetMapping("/getDeviceDetailList")
    public AjaxResult getDeviceDetailList() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "device_detail_list");
        JSONObject object = new JSONObject();
        object.put("details", JSON.parseArray(details.getRemark()));
        return AjaxResult.success(object);
    }

    /**
     * 主轴转速倍率
     */
    @Anonymous
    @ApiOperation("主轴转速倍率")
    @GetMapping("/getDeviceList")
    public AjaxResult getDeviceList() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "device_list");
        JSONObject object = new JSONObject();
        object.put("details", JSON.parseArray(details.getRemark()));
        return AjaxResult.success(object);
    }

    /**
     * 主轴转速倍率
     */
    @Anonymous
    @ApiOperation("设备运行统计")
    @GetMapping("/getDeviceCount")
    public AjaxResult getDeviceCount() {
        // 主数据
        BigDecimal useRate = new BigDecimal(0);
        SysDictData deviceDetails = DictUtils.getDictCache("big_screen_data", "device_list");
        JSONArray deviceDetailList = JSON.parseArray(deviceDetails.getRemark());
        for (int i = 0; i < deviceDetailList.size(); i++) {
            JSONArray array = deviceDetailList.getJSONArray(i);
            useRate = useRate.add(array.getBigDecimal(4));
        }
        // 明细
        useRate = useRate.divide(new BigDecimal(deviceDetailList.size()), 2, RoundingMode.HALF_UP);
        SysDictData details = DictUtils.getDictCache("big_screen_data", "device_detail_list");
        JSONArray detailList = JSON.parseArray(details.getRemark());
        BigDecimal deviceNo = new BigDecimal(detailList.size());
        BigDecimal runNo = new BigDecimal(0);
        for (int i = 0; i < detailList.size(); i++) {
            JSONArray array = detailList.getJSONArray(i);
            if (array.getInteger(4).equals(1)) {
                runNo = runNo.add(new BigDecimal(1));
            }
        }
        BigDecimal errNo = deviceNo.subtract(runNo);
        BigDecimal errRate = errNo.divide(deviceNo, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        BigDecimal completeRate = new BigDecimal(100).subtract(errRate);
        JSONObject resObj = new JSONObject();
        resObj.put("deviceNo", deviceNo);
        resObj.put("runNo", runNo);
        resObj.put("useRate", useRate);
        resObj.put("errNo", errNo);
        resObj.put("errRate", errRate);
        resObj.put("completeRate", completeRate);
        return AjaxResult.success(resObj);
    }

    /**
     * 主轴转速倍率
     */
    @Anonymous
    @ApiOperation("清洗喷漆")
    @GetMapping("/getCleaningPaintingList")
    public AjaxResult getCleaningPaintingList() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "cleaning_painting");
        JSONObject object = new JSONObject();
        object.put("details", JSON.parseArray(details.getRemark()));
        return AjaxResult.success(object);
    }

    /**
     * 主轴转速倍率
     */
    @Anonymous
    @ApiOperation("清洗喷漆")
    @GetMapping("/getCleaningPaintingPhCount")
    public AjaxResult getCleaningPaintingPhCount() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "cleaning_painting");
        JSONObject object = new JSONObject();
        JSONArray detailList = JSON.parseArray(details.getRemark());
        List<BigDecimal> oneList = new ArrayList<>();
        List<BigDecimal> twoList = new ArrayList<>();
        List<BigDecimal> threeList = new ArrayList<>();
        List<BigDecimal> fourList = new ArrayList<>();
        List<String> times = new ArrayList<>();
        for (int i = 0; i < detailList.size(); i++) {
            JSONArray arr = detailList.getJSONArray(i);
            times.add(arr.getString(0).substring(10, arr.getString(0).length() - 3));
            oneList.add(arr.getBigDecimal(1));
            twoList.add(arr.getBigDecimal(2));
            threeList.add(arr.getBigDecimal(3));
            fourList.add(arr.getBigDecimal(4));
        }
        object.put("timeList", times);
        object.put("oneList", oneList);
        object.put("twoList", twoList);
        object.put("threeList", threeList);
        object.put("fourList", fourList);
        return AjaxResult.success(object);
    }

    /**
     * 获取请洗澡温度
     */
    @Anonymous
    @ApiOperation("获取请洗澡温度")
    @GetMapping("/getCleaningPaintingTemperatureCount")
    public AjaxResult getCleaningPaintingTemperatureCount() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "cleaning_painting");
        JSONObject object = new JSONObject();
        JSONArray detailList = JSON.parseArray(details.getRemark());
        List<BigDecimal> oneList = new ArrayList<>();
        List<BigDecimal> twoList = new ArrayList<>();
        List<BigDecimal> threeList = new ArrayList<>();
        List<BigDecimal> fourList = new ArrayList<>();
        List<String> times = new ArrayList<>();
        for (int i = 0; i < detailList.size(); i++) {
            JSONArray arr = detailList.getJSONArray(i);
            times.add(arr.getString(0).substring(10, arr.getString(0).length() - 3));
            oneList.add(arr.getBigDecimal(5));
            twoList.add(arr.getBigDecimal(6));
            threeList.add(arr.getBigDecimal(7));
            fourList.add(arr.getBigDecimal(8));
        }
        object.put("timeList", times);
        object.put("oneList", oneList);
        object.put("twoList", twoList);
        object.put("threeList", threeList);
        object.put("fourList", fourList);
        return AjaxResult.success(object);
    }

    /**
     * 获取油漆温度
     */
    @Anonymous
    @ApiOperation("获取油漆温度")
    @GetMapping("/getCleaningPaintingPaintTemperatureCount")
    public AjaxResult getCleaningPaintingPaintTemperatureCount() {
        SysDictData details = DictUtils.getDictCache("big_screen_data", "cleaning_painting");
        JSONObject object = new JSONObject();
        JSONArray detailList = JSON.parseArray(details.getRemark());
        List<BigDecimal> oneList = new ArrayList<>();
        List<BigDecimal> twoList = new ArrayList<>();
        List<String> times = new ArrayList<>();
        for (int i = 0; i < detailList.size(); i++) {
            JSONArray arr = detailList.getJSONArray(i);
            times.add(arr.getString(0).substring(10, arr.getString(0).length() - 3));
            oneList.add(arr.getBigDecimal(9));
            twoList.add(arr.getBigDecimal(10));
        }
        object.put("timeList", times);
        object.put("oneList", oneList);
        object.put("twoList", twoList);
        return AjaxResult.success(object);
    }


}
