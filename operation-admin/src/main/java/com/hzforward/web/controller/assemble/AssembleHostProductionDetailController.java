package com.hzforward.web.controller.assemble;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.assemble.domain.AssembleHostProductionDetail;
import com.hzforward.assemble.service.IAssembleHostProductionDetailService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 主机装配生产清单Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@RestController
@RequestMapping("/assemble/hostProductionDetail")
public class AssembleHostProductionDetailController extends BaseController
{
    @Resource
    private IAssembleHostProductionDetailService assembleHostProductionDetailService;

    /**
     * 查询主机装配生产清单列表
     */
//    @PreAuthorize("@ss.hasPermi('assemble:hostProductionDetail:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(AssembleHostProductionDetail assembleHostProductionDetail)
    {
        startPage();
        List<AssembleHostProductionDetail> list = assembleHostProductionDetailService.selectAssembleHostProductionDetailList(assembleHostProductionDetail);
        return getDataTable(list);
    }

    /**
     * 导出主机装配生产清单列表
     */
    @PreAuthorize("@ss.hasPermi('assemble:hostProductionDetail:export')")
    @Log(title = "主机装配生产清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssembleHostProductionDetail assembleHostProductionDetail)
    {
        List<AssembleHostProductionDetail> list = assembleHostProductionDetailService.selectAssembleHostProductionDetailList(assembleHostProductionDetail);
        ExcelUtil<AssembleHostProductionDetail> util = new ExcelUtil<>(AssembleHostProductionDetail.class);
        util.exportExcel(response, list, "主机装配生产清单数据");
    }

	/**
	 * 获取主机装配生产清单详细信息
	 */
	@PreAuthorize("@ss.hasPermi('assemble:hostProductionDetail:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id)
	{
		return AjaxResult.success(assembleHostProductionDetailService.getById(id));
	}

    /**
     * 新增主机装配生产清单
     */
    @PreAuthorize("@ss.hasPermi('assemble:hostProductionDetail:add')")
    @Log(title = "主机装配生产清单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssembleHostProductionDetail assembleHostProductionDetail)
    {
        return toAjax(assembleHostProductionDetailService.save(assembleHostProductionDetail));
    }

    /**
     * 修改主机装配生产清单
     */
//    @PreAuthorize("@ss.hasPermi('assemble:hostProductionDetail:edit')")
    @Log(title = "主机装配生产清单", businessType = BusinessType.UPDATE)
    @PutMapping
    @Anonymous
    public AjaxResult edit(@RequestBody AssembleHostProductionDetail assembleHostProductionDetail)
    {
        return toAjax(assembleHostProductionDetailService.updateById(assembleHostProductionDetail));
    }

    /**
     * 删除主机装配生产清单
     */
    @PreAuthorize("@ss.hasPermi('assemble:hostProductionDetail:remove')")
    @Log(title = "主机装配生产清单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(assembleHostProductionDetailService.removeBatchByIds(ids));
    }
}
