package com.hzforward.web.controller.assemble;

import com.hzforward.assemble.domain.AssembleHostProduction;
import com.hzforward.assemble.domain.req.AssembleHostProductionListReq;
import com.hzforward.assemble.service.IAssembleHostProductionService;
import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 主机装配生产信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@RestController
@RequestMapping("/assemble/hostProduction")
public class AssembleHostProductionController extends BaseController {
    @Resource
    private IAssembleHostProductionService assembleHostProductionService;

    /**
     * 查询主机装配生产信息列表
     */
//    @PreAuthorize("@ss.hasPermi('assemble:hostProduction:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(AssembleHostProductionListReq req) {
        startPage();
        List<AssembleHostProduction> list = assembleHostProductionService.selectAssembleHostProductionList(req);
        return getDataTable(list);
    }

    /**
     * 导出主机装配生产信息列表
     */
    @PreAuthorize("@ss.hasPermi('assemble:hostProduction:export')")
    @Log(title = "主机装配生产信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssembleHostProductionListReq req) {
        List<AssembleHostProduction> list = assembleHostProductionService.selectAssembleHostProductionList(req);
        for (AssembleHostProduction assembleHostProduction : list) {
            assembleHostProduction.setQuantity(1);
        }
        ExcelUtil<AssembleHostProduction> util = new ExcelUtil<>(AssembleHostProduction.class);
        util.exportExcel(response, list, "主机装配生产信息数据");
    }

    /**
     * 获取主机装配生产信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('assemble:hostProduction:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(assembleHostProductionService.getById(id));
    }

    /**
     * 新增主机装配生产信息
     */
    @PreAuthorize("@ss.hasPermi('assemble:hostProduction:add')")
    @Log(title = "主机装配生产信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssembleHostProduction assembleHostProduction) {
        return toAjax(assembleHostProductionService.save(assembleHostProduction));
    }

    /**
     * 修改主机装配生产信息
     */
//    @PreAuthorize("@ss.hasPermi('assemble:hostProduction:edit')")
    @Log(title = "主机装配生产信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @Anonymous
    public AjaxResult edit(@RequestBody AssembleHostProduction assembleHostProduction) {
        return toAjax(assembleHostProductionService.updateById(assembleHostProduction));
    }

    /**
     * 删除主机装配生产信息
     */
    @PreAuthorize("@ss.hasPermi('assemble:hostProduction:remove')")
    @Log(title = "主机装配生产信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids) {
        return toAjax(assembleHostProductionService.removeBatchByIds(ids));
    }

    /**
     * 主机装配生产信息导入
     *
     * @param file
     * @return
     * @throws Exception
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:import')")
    @Log(title = "主机装配生产信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, Date assembleDate) throws Exception {
        String message = assembleHostProductionService.importData(file, assembleDate);
        return AjaxResult.success(message);
    }
}
