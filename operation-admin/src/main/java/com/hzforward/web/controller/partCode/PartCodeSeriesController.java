package com.hzforward.web.controller.partCode;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.partCode.domain.PartCodeSeries;
import com.hzforward.partCode.service.IPartCodeSeriesService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 系列管理Controller
 *
 * <AUTHOR>
 * @date 2022-04-15
 */
@RestController
@RequestMapping("/partCode/partCodeSeries")
public class PartCodeSeriesController extends BaseController
{
    @Resource
    private IPartCodeSeriesService partCodeSeriesService;

    /**
     * 查询系列管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSeries:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartCodeSeries partCodeSeries)
    {
        startPage();
        List<PartCodeSeries> list = partCodeSeriesService.selectPartCodeSeriesList(partCodeSeries);
        return getDataTable(list);
    }

    /**
     * 获取系列管理字典表
     */
    @GetMapping("/dict")
    public AjaxResult getPartCodeSeriesDict(PartCodeSeries partCodeSeries)
    {
        return AjaxResult.success(partCodeSeriesService.getPartCodeSeriesDict(partCodeSeries));
    }

    /**
     * 导出系列管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSeries:export')")
    @Log(title = "系列管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PartCodeSeries partCodeSeries)
    {
        List<PartCodeSeries> list = partCodeSeriesService.selectPartCodeSeriesList(partCodeSeries);
        ExcelUtil<PartCodeSeries> util = new ExcelUtil<>(PartCodeSeries.class);
        util.exportExcel(response, list, "系列管理数据");
    }

    /**
     * 获取系列管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSeries:query')")
    @GetMapping(value = "/{seriesId}")
    public AjaxResult getInfo(@PathVariable("seriesId") Long seriesId)
    {
        return AjaxResult.success(partCodeSeriesService.selectPartCodeSeriesBySeriesId(seriesId));
    }

    /**
     * 新增系列管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSeries:add')")
    @Log(title = "系列管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PartCodeSeries partCodeSeries)
    {
        return toAjax(partCodeSeriesService.insertPartCodeSeries(partCodeSeries));
    }

    /**
     * 修改系列管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSeries:edit')")
    @Log(title = "系列管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PartCodeSeries partCodeSeries)
    {
        return toAjax(partCodeSeriesService.updatePartCodeSeries(partCodeSeries));
    }

    /**
     * 删除系列管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeSeries:remove')")
    @Log(title = "系列管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{seriesIds}")
    public AjaxResult remove(@PathVariable Long[] seriesIds)
    {
        return toAjax(partCodeSeriesService.deletePartCodeSeriesBySeriesIds(seriesIds));
    }

    /**
     * 获取曳引轮可选型号字典表
     */
    @GetMapping("/getSeriesModelDict")
    public AjaxResult getSeriesModelDict()
    {
        return AjaxResult.success(partCodeSeriesService.getSeriesModelDict());
    }
}
