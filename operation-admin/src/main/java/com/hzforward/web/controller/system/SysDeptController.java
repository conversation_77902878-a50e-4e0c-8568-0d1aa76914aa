package com.hzforward.web.controller.system;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.domain.entity.SysDept;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.system.service.ISysDeptService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/dept")
public class SysDeptController extends BaseController
{
    @Resource
    private ISysDeptService deptService;

    /**
     * 根据部门编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dept:query')")
    @GetMapping(value = "/{deptId}")
    public AjaxResult getInfo(@PathVariable Long deptId)
    {
        return AjaxResult.success(deptService.getById(deptId));
    }

    /**
     * 获取部门列表
     */
    @GetMapping("/list")
    public AjaxResult list(SysDept dept)
    {
        return AjaxResult.success(deptService.selectDeptList(dept));
    }

    /**
     * 根据部门ID获取子部门列表(包含当前部门)
     */
    @GetMapping("/selectDeptIdListByTopDeptId/{deptId}")
    public AjaxResult selectDeptIdListByTopDeptId(@PathVariable(value = "deptId") Long deptId)
    {
        return AjaxResult.success(deptService.selectDeptIdListByTopDeptId(deptId));
    }

    /**
     * 查询部门列表（排除节点）
     */
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId)
    {
        List<SysDept> deptList = deptService.selectDeptList(new SysDept());
        deptList.removeIf(d -> d.getDeptId().intValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), String.valueOf(deptId)));
        return AjaxResult.success(deptList);
    }

    /**
     * 获取部门树列表
     */
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDept dept)
    {
        return AjaxResult.success(deptService.selectDeptTreeList(dept));
    }

    /**
     * 新增部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:add')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept)
    {
        return toAjax(deptService.save(dept));
    }

    /**
     * 修改部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:edit')")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept)
    {
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:remove')")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId)
    {
        return toAjax(deptService.deleteDeptById(deptId));
    }

    /**
     * 同步钉钉部门
     */
    @PreAuthorize("@ss.hasPermi('system:dept:syncDingtalkDept')")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping("/syncDingtalkDept")
    public AjaxResult syncDingtalkDept(){
        deptService.syncDingtalkDept();
        return AjaxResult.success();
    }
}
