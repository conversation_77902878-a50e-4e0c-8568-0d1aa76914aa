package com.hzforward.web.controller.assemble;


import com.hzforward.assemble.domain.AssembleModelListDetail;
import com.hzforward.assemble.service.IAssembleModelListDetailService;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 配送机型配件清单详情Controller
 * 
 * <AUTHOR>
 * @date 2020-05-19
 */
@RestController
@RequestMapping("/assemble/modelListDetail")
public class AssembleModelListDetailController extends BaseController
{
    @Resource
    private IAssembleModelListDetailService assembleModelListDetailService;

    /**
     * 查询配送机型配件清单详情列表
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssembleModelListDetail assembleModelListDetail)
    {
        startPage();
        List<AssembleModelListDetail> list = assembleModelListDetailService.selectAssembleModelListDetailList(assembleModelListDetail);
        return getDataTable(list);
    }

    /**
     * 导出配送机型配件清单详情列表
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:export')")
    @Log(title = "配送机型配件清单详情", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(AssembleModelListDetail assembleModelListDetail)
    {
        List<AssembleModelListDetail> list = assembleModelListDetailService.selectAssembleModelListDetailList(assembleModelListDetail);
        ExcelUtil<AssembleModelListDetail> util = new ExcelUtil<>(AssembleModelListDetail.class);
        return util.exportExcel(list, "modelListDetail");
    }

    /**
     * 获取配送机型配件清单详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(assembleModelListDetailService.selectAssembleModelListDetailById(id));
    }

    /**
     * 新增配送机型配件清单详情
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:add')")
    @Log(title = "配送机型配件清单详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssembleModelListDetail assembleModelListDetail)
    {
        return toAjax(assembleModelListDetailService.insertAssembleModelListDetail(assembleModelListDetail));
    }

    /**
     * 修改配送机型配件清单详情
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:edit')")
    @Log(title = "配送机型配件清单详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssembleModelListDetail assembleModelListDetail)
    {
        return toAjax(assembleModelListDetailService.updateAssembleModelListDetail(assembleModelListDetail));
    }

    /**
     * 删除配送机型配件清单详情
     */
    @PreAuthorize("@ss.hasPermi('assemble:model:remove')")
    @Log(title = "配送机型配件清单详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(assembleModelListDetailService.deleteAssembleModelListDetailByIds(ids));
    }
}
