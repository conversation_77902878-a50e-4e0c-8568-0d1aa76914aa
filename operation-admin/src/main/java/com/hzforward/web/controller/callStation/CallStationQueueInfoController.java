package com.hzforward.web.controller.callStation;

import com.hzforward.callStation.domain.CallStationQueueInfo;
import com.hzforward.callStation.domain.CallStationRetrievalInfo;
import com.hzforward.callStation.service.ICallStationQueueInfoService;
import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 队列信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-26
 */
@RestController
@RequestMapping("/callStation/queueInfo")
public class CallStationQueueInfoController extends BaseController
{
    @Resource
    private ICallStationQueueInfoService callStationQueueInfoService;

    /**
     * 查询队列信息列表
     */
    @PreAuthorize("@ss.hasPermi('callStation:queueInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(CallStationQueueInfo callStationQueueInfo)
    {
        startPage();
        List<CallStationRetrievalInfo> list = callStationQueueInfoService.selectCallStationQueueInfoList(callStationQueueInfo);
        return getDataTable(list);
    }

    /**
     * 叫号
     */
    @PreAuthorize("@ss.hasPermi('callStation:applet:callStation')")
    @GetMapping("/callStation/{queuingId}")
    public AjaxResult callStation(@PathVariable("queuingId") Long queuingId)
    {
        return toAjax(callStationQueueInfoService.callStation(queuingId));
    }

    /**
     * 删除失败叫号的数据
     */
    @PreAuthorize("@ss.hasPermi('callStation:applet:callStation')")
    @GetMapping("/callStation/removeQueueInfo/{queuingId}")
    public AjaxResult removeQueueInfo(@PathVariable("queuingId") Long queuingId)
    {
        return toAjax(callStationQueueInfoService.removeQueueInfo(queuingId));
    }

    /**
     * 入厂
     */
    @PreAuthorize("@ss.hasPermi('callStation:applet:inOut')")
    @GetMapping("/callStation/enter/{queuingId}")
    public AjaxResult in(@PathVariable("queuingId") Long queuingId)
    {
        return toAjax(callStationQueueInfoService.enter(queuingId));
    }

    /**
     * 出厂
     */
    @PreAuthorize("@ss.hasPermi('callStation:applet:inOut')")
    @GetMapping("/callStation/out/{queuingId}")
    public AjaxResult out(@PathVariable("queuingId") Long queuingId)
    {
        return toAjax(callStationQueueInfoService.out(queuingId));
    }

    /**
     * 获取当前排队用户人数
     */
    @Anonymous
    @GetMapping("/getQueueNumber/{companyCode}")
    public AjaxResult getQueueNumber(@PathVariable String companyCode) {
        return AjaxResult.success(callStationQueueInfoService.getQueueNumber(companyCode));
    }

    /**
     * 根据用户wechatId查询队列位置
     */
    @Anonymous
    @GetMapping("/getQueueByWechatOpenId/{wechatOpenId}&{companyCode}")
    public AjaxResult getQueueByWechatOpenId(@PathVariable("wechatOpenId") String wechatOpenId, @PathVariable String companyCode) {
        return AjaxResult.success(callStationQueueInfoService.getQueueByWechatOpenId(wechatOpenId, companyCode));
    }

}
