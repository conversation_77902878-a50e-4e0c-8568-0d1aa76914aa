package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.annotation.RepeatSubmit;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.domain.repair.PackingOrder;
import com.hzforward.equipmentManage.domain.repair.RepairReport;
import com.hzforward.equipmentManage.service.IRepairReportService;
import com.hzforward.wms.util.WMSDictUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备报修Controller
 *
 * <AUTHOR>
 * @date 2023-10-12
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/equipmentManage/repairReport")
public class RepairReportController extends BaseController
{
    private final IRepairReportService repairReportService;

    /**
     * 查询设备报修列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RepairReport repairReport)
    {
        startPage();
        List<RepairReport> list = repairReportService.getAndSetList(repairReport);
        return getDataTable(list);
    }

    /**
     * 导出设备报修列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:repairReport:export')")
    @Log(title = "设备报修", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RepairReport repairReport)
    {
        List<RepairReport> list = repairReportService.getAndSetList(repairReport);
        list.forEach(repairReportService::setUserName);
        ExcelUtil<RepairReport> util = new ExcelUtil<>(RepairReport.class);
        util.exportExcel(response, list, "设备报修维修数据");
    }

    /**
     * 获取设备报修详细信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:repairReport:query')")
    @GetMapping(value = "/{repairId}")
    public AjaxResult getInfo(@PathVariable("repairId") Long repairId)
    {
        return AjaxResult.success(repairReportService.getAndSetById(repairId));
    }

    /**
     * 修改设备报修
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:repairReport:edit')")
    @Log(title = "设备报修", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RepairReport repairReport)
    {
        return toAjax(repairReportService.updateById(repairReport));
    }

    /**
     * 删除设备报修
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:repairReport:remove')")
    @Log(title = "设备报修", businessType = BusinessType.DELETE)
	@DeleteMapping("/{repairIds}")
    public AjaxResult remove(@PathVariable List<Long> repairIds)
    {
        return toAjax(repairReportService.removeBatchByIds(repairIds));
    }

    /**
     * 结束维修人当前维修工单
     */
    @RepeatSubmit
    @PutMapping("/endMaintainerWork")
    public AjaxResult endMaintainerWork() {
        repairReportService.endMaintainerWork();
        return AjaxResult.success();
    }

    /**
     * 新增维修单
     * @param repairReport 维修表单详情
     */
    @RepeatSubmit
    @PostMapping("/addRepairReport")
    public AjaxResult addRepairReport(@RequestBody RepairReport repairReport) {
        repairReportService.addRepairReport(repairReport);
        return AjaxResult.success();
    }

    /**
     * 接单
     * @param repairId 维修表单详情
     */
    @RepeatSubmit
    @PutMapping("/receivingRepairReport/{repairId}")
    public AjaxResult receivingRepairReport(@PathVariable Long repairId) {
        repairReportService.receivingRepairReport(repairId);
        return AjaxResult.success();
    }

    /**
     * 移交维修单
     * @param repairId 维修单id
     * @param transferToId 被移交人ID
     */
    @RepeatSubmit
    @PutMapping("/transferRepairReport/{repairId}&{transferToId}")
    public AjaxResult transferRepairReport(@PathVariable("repairId") Long repairId, @PathVariable("transferToId") String transferToId) {
        repairReportService.transferRepairReport(repairId, transferToId);
        return AjaxResult.success();
    }

    /**
     * 开始维修
     * @param repairReport 维修报告
     */
    @RepeatSubmit
    @PostMapping("/beginRepair")
    public AjaxResult beginRepair(@RequestBody RepairReport repairReport) {
        repairReportService.beginRepair(repairReport);
        return AjaxResult.success();
    }

    /**
     * 加入维修
     * @param repairId 维修单id
     */
    @RepeatSubmit
    @PutMapping("/joinRepair/{repairId}")
    public AjaxResult joinRepair(@PathVariable Long repairId) {
        repairReportService.joinRepair(repairId);
        return AjaxResult.success();
    }

    /**
     * 离开维修
     * @param repairId 维修单id
     */
    @RepeatSubmit
    @PutMapping("/leaveRepair/{repairId}")
    public AjaxResult leaveRepair(@PathVariable Long repairId) {
        repairReportService.leaveRepair(repairId);
        return AjaxResult.success();
    }

    /**
     * 暂停维修
     * @param repairReport 维修报告
     */
    @RepeatSubmit
    @PostMapping("/pauseRepair")
    public AjaxResult pauseRepair(@RequestBody RepairReport repairReport) {
        repairReportService.pauseRepair(repairReport);
        return AjaxResult.success();
    }

    /**
     * 继续维修
     * @param repairId 维修报告id
     */
    @RepeatSubmit
    @PutMapping("/continueRepair/{repairId}")
    public AjaxResult continueRepair(@PathVariable("repairId") Long repairId) {
        repairReportService.continueRepair(repairId);
        return AjaxResult.success();
    }

    /**
     * 获取成本中心字典表
     * @return 成本中心字典表
     */
    @GetMapping("/getCostCenterDict")
    public AjaxResult getCostCenterDict() {
        return AjaxResult.success(WMSDictUtil.getCostCenterDict());
    }

    /**
     * 获取备品备件仓储
     * @param itemCode 件号
     */
    @GetMapping("/getAllStock/{itemCode}")
    public AjaxResult getAllStock(@PathVariable("itemCode") String itemCode) {
        return AjaxResult.success(repairReportService.getAllStock(itemCode));
    }

    /**
     * 创建备品备件领料单
     * @param packingOrder 领料详情
     */
    @PostMapping("/createPickingOrder")
    public AjaxResult createPickingOrder(@RequestBody PackingOrder packingOrder) {
        repairReportService.createPickingOrder(packingOrder);
        return AjaxResult.success();
    }

    /**
     * 维修完成
     * @param repairReport 维修报告
     */
    @RepeatSubmit
    @PostMapping("/completeRepairReport")
    public AjaxResult completeRepairReport(@RequestBody RepairReport repairReport) {
        repairReportService.completeRepairReport(repairReport);
        return AjaxResult.success();
    }

}
