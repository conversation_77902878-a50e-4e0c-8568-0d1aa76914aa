package com.hzforward.web.controller.system;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.domain.entity.SysUser;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.SecurityUtils;
import com.hzforward.system.service.ISysUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController
{
    @Resource
    private ISysUserService userService;

    /**
     * 获取用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user)
    {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    /**
     * 获取用户字典表
     */
    @GetMapping("/userDict")
    public AjaxResult getUserDict(SysUser user)
    {
        return AjaxResult.success(userService.getUserDict(user));
    }

    /**
     * 根据用户编号获取详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = "/{userId}" )
    public AjaxResult getInfo(@PathVariable(value = "userId") Long userId)
    {
        return AjaxResult.success(userService.selectUserById(userId));
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysUser user)
    {
        if (userService.checkUserNameExist(user)) {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }

        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysUser user)
    {
        if (userService.checkUserNameExist(user)) {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，登录账号已存在");
        }

        return toAjax(userService.updateUser(user));
    }

    /**
     * 修改用户基础信息
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/updateUserProfile")
    public AjaxResult updateUserProfile(@RequestBody SysUser user)
    {
        return toAjax(userService.updateUserProfile(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable List<Long> userIds)
    {
        if (userIds.contains(getUserId())) {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user)
    {
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 同步域用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:syncLdapUser')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @GetMapping(value = "/syncLdapUser")
    public AjaxResult syncLdapUser() {
        return AjaxResult.success(userService.syncLdapUser());
    }

    /**
     * 同步钉钉用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:syncDingTalkUser')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @GetMapping(value = "/syncDingTalkUser")
    public AjaxResult getDingTalkUser() {
        return AjaxResult.success(userService.syncDingTalkUser());
    }

}
