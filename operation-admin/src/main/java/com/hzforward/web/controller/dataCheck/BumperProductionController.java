package com.hzforward.web.controller.dataCheck;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.dataCheck.domain.BumperProduction;
import com.hzforward.dataCheck.service.IBumperProductionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 缓冲器排产Controller
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@RestController
@RequestMapping("/dataCheck/bumperProduction")
public class BumperProductionController extends BaseController
{
    @Resource
    private IBumperProductionService bumperProductionService;

    /**
     * 查询缓冲器排产列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperProduction:list')")
    @GetMapping("/list")
    public TableDataInfo list(BumperProduction bumperProduction)
    {
        startPage();
        List<BumperProduction> list = bumperProductionService.selectBumperProductionList(bumperProduction);
        return getDataTable(list);
    }


    /**
     * 导出缓冲器排产列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperProduction:export')")
    @Log(title = "缓冲器排产", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BumperProduction bumperProduction)
    {
        List<BumperProduction> list = bumperProductionService.selectBumperProductionList(bumperProduction);
        ExcelUtil<BumperProduction> util = new ExcelUtil<>(BumperProduction.class);
        util.exportExcel(response, list, "缓冲器排产数据");
    }

    /**
     * 获取缓冲器排产详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperProduction:query')")
    @GetMapping(value = "/{productionId}")
    public AjaxResult getInfo(@PathVariable("productionId") Long productionId)
    {
        return AjaxResult.success(bumperProductionService.selectBumperProductionByProductionId(productionId));
    }

    /**
     * 获取缓冲器排产详细信息
     */
    @Anonymous
    @GetMapping(value = "getInfoByFactoryNumber/{factoryNumber}")
    public AjaxResult getInfoByFactoryNumber(@PathVariable("factoryNumber") String factoryNumber)
    {
        return AjaxResult.success(bumperProductionService.selectBumperProductionByFactoryNumber(factoryNumber));
    }

    /**
     * 新增缓冲器排产
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperProduction:add')")
    @Log(title = "缓冲器排产", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BumperProduction bumperProduction)
    {
        return toAjax(bumperProductionService.insertBumperProduction(bumperProduction));
    }

    /**
     * 修改缓冲器排产
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperProduction:edit')")
    @Log(title = "缓冲器排产", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BumperProduction bumperProduction)
    {
        return toAjax(bumperProductionService.updateBumperProduction(bumperProduction));
    }

    /**
     * 修改缓冲器排产扫描标志
     */
    @Anonymous
    @PutMapping("/updateScanFlag/{productionId}")
    public AjaxResult updateScanFlag(@PathVariable("productionId") Long productionId)
    {
        return toAjax(bumperProductionService.updateScanFlag(productionId));
    }

    /**
     * 删除缓冲器排产
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperProduction:remove')")
    @Log(title = "缓冲器排产", businessType = BusinessType.DELETE)
	@DeleteMapping("/{productionIds}")
    public AjaxResult remove(@PathVariable List<Long> productionIds)
    {
        return toAjax(bumperProductionService.deleteBumperProductionByProductionIds(productionIds));
    }

    /**
     * 下载缓冲器排产导入模板
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperProduction:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<BumperProduction> util = new ExcelUtil<>(BumperProduction.class);
        util.importTemplateExcel(response, "缓冲器排产信息");
    }

    /**
     * 导入缓冲器排产
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:bumperProduction:import')")
    @Log(title = "缓冲器排产", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<BumperProduction> util = new ExcelUtil<>(BumperProduction.class);
        List<BumperProduction> bumperProductionList = util.importExcel(file.getInputStream());
        String message = bumperProductionService.importData(bumperProductionList, updateSupport);
        return AjaxResult.success(message);
    }

}
