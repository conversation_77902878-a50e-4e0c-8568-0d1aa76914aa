package com.hzforward.web.controller.wechat;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.wechat.service.IWechatPublicService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 队列信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-26
 */
@RestController
@RequestMapping("/wechat/public")
public class WechatPublicController extends BaseController
{
    @Resource
    private IWechatPublicService wechatPublicService;


    @Anonymous
    @GetMapping(value = "/getAddress")
    public AjaxResult getAddress(String url) {
        return AjaxResult.success(wechatPublicService.getAddress(url));
    }

    @Anonymous
    @GetMapping(value = "/getUserOpenId/{code}")
    public AjaxResult getUserOpenId(@PathVariable String code) {
        return AjaxResult.success(wechatPublicService.getUserOpenId(code));
    }
}
