package com.hzforward.web.controller.dataCheck;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.dataCheck.domain.ShipmarkRecord;
import com.hzforward.dataCheck.domain.req.ShipmarkRecordReq;
import com.hzforward.dataCheck.service.IShipmarkRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.core.page.TableDataInfo;

import java.util.List;

/**
 * 打唛头核对记录Controller
 *
 * <AUTHOR>
 * @date 2024-04-07
 */
@RestController
@RequestMapping("/dataCheck/shipmark")
public class ShipmarkRecordController extends BaseController
{
    @Resource
    private IShipmarkRecordService shipmarkRecordService;

    /**
     * 查询打唛头核对记录列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:shipmark:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShipmarkRecordReq shipmarkRecordReq)
    {
        startPage();
        List<ShipmarkRecord> list = shipmarkRecordService.selectShipmarkRecordList(shipmarkRecordReq);
        return getDataTable(list);
    }

    /**
     * 导出打唛头核对记录列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:shipmark:export')")
    @Log(title = "打唛头核对记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShipmarkRecordReq shipmarkRecordReq)
    {
        List<ShipmarkRecord> list = shipmarkRecordService.selectShipmarkRecordList(shipmarkRecordReq);
        ExcelUtil<ShipmarkRecord> util = new ExcelUtil<>(ShipmarkRecord.class);
        util.exportExcel(response, list, "打唛头核对记录数据");
    }

    /**
     * 获取打唛头核对记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:shipmark:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shipmarkRecordService.selectShipmarkRecordById(id));
    }

    /**
     * 新增打唛头核对记录
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:shipmark:add')")
    @Log(title = "打唛头核对记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShipmarkRecord shipmarkRecord)
    {
        return toAjax(shipmarkRecordService.insertShipmarkRecord(shipmarkRecord));
    }

    /**
     * 修改打唛头核对记录
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:shipmark:edit')")
    @Log(title = "打唛头核对记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShipmarkRecord shipmarkRecord)
    {
        return toAjax(shipmarkRecordService.updateShipmarkRecord(shipmarkRecord));
    }

    /**
     * 删除打唛头核对记录
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:shipmark:remove')")
    @Log(title = "打唛头核对记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shipmarkRecordService.deleteShipmarkRecordByIds(ids));
    }

    /**
     * 处理移动端核对结果请求
     */
    @PostMapping("/shipmarkVerifySubmit")
    public AjaxResult shipmarkVerifySubmit(@RequestBody ShipmarkRecord shipmarkRecord)
    {
        return toAjax(shipmarkRecordService.shipmarkVerifySubmit(shipmarkRecord));
    }
}
