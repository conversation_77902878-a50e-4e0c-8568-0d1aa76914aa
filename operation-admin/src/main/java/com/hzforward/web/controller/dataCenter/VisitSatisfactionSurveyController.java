package com.hzforward.web.controller.dataCenter;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.dataCenter.domain.VisitSatisfactionSurvey;
import com.hzforward.dataCenter.service.IVisitSatisfactionSurveyService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 访客满意度Controller
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@RestController
@RequestMapping("/dataCenter/visitSatisfactionSurvey")
public class VisitSatisfactionSurveyController extends BaseController
{
    @Resource
    private IVisitSatisfactionSurveyService visitSatisfactionSurveyService;

    /**
     * 查询访客满意度列表
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:visitSatisfactionSurvey:list')")
    @GetMapping("/list")
    public TableDataInfo list(VisitSatisfactionSurvey visitSatisfactionSurvey)
    {
        startPage();
        List<VisitSatisfactionSurvey> list = visitSatisfactionSurveyService.selectVisitSatisfactionSurveyList(visitSatisfactionSurvey);
        return getDataTable(list);
    }


    /**
     * 导出访客满意度列表
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:visitSatisfactionSurvey:export')")
    @Log(title = "访客满意度", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VisitSatisfactionSurvey visitSatisfactionSurvey)
    {
        List<VisitSatisfactionSurvey> list = visitSatisfactionSurveyService.selectVisitSatisfactionSurveyList(visitSatisfactionSurvey);
        ExcelUtil<VisitSatisfactionSurvey> util = new ExcelUtil<>(VisitSatisfactionSurvey.class);
        util.exportExcel(response, list, "访客满意度数据");
    }

    /**
     * 获取访客满意度详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:visitSatisfactionSurvey:query')")
    @GetMapping(value = "/{surveyId}")
    public AjaxResult getInfo(@PathVariable("surveyId") Long surveyId)
    {
        return AjaxResult.success(visitSatisfactionSurveyService.selectVisitSatisfactionSurveyBySurveyId(surveyId));
    }

    /**
     * 新增访客满意度
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:visitSatisfactionSurvey:add')")
    @Log(title = "访客满意度", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VisitSatisfactionSurvey visitSatisfactionSurvey)
    {
        return toAjax(visitSatisfactionSurveyService.insertVisitSatisfactionSurvey(visitSatisfactionSurvey));
    }

    /**
     * 修改访客满意度
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:visitSatisfactionSurvey:edit')")
    @Log(title = "访客满意度", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VisitSatisfactionSurvey visitSatisfactionSurvey)
    {
        return toAjax(visitSatisfactionSurveyService.updateVisitSatisfactionSurvey(visitSatisfactionSurvey));
    }

    /**
     * 删除访客满意度
     */
    @PreAuthorize("@ss.hasPermi('dataCenter:visitSatisfactionSurvey:remove')")
    @Log(title = "访客满意度", businessType = BusinessType.DELETE)
	@DeleteMapping("/{surveyIds}")
    public AjaxResult remove(@PathVariable List<Long> surveyIds)
    {
        return toAjax(visitSatisfactionSurveyService.deleteVisitSatisfactionSurveyBySurveyIds(surveyIds));
    }


    /**
     * 新增访客满意度
     */
    @Anonymous
    @PostMapping("/submitQuestionnaire")
    public AjaxResult submitQuestionnaire(@RequestBody VisitSatisfactionSurvey visitSatisfactionSurvey)
    {
        return toAjax(visitSatisfactionSurveyService.submitQuestionnaire(visitSatisfactionSurvey));
    }
}
