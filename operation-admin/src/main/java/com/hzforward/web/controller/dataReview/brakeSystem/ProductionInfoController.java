package com.hzforward.web.controller.dataReview.brakeSystem;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.dataReview.brakeSystem.domain.ProductionInfo;
import com.hzforward.dataReview.brakeSystem.service.IProductionInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 制动器排产信息Controller
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@RestController
@RequestMapping("/dataReview/productionInfo")
public class ProductionInfoController extends BaseController
{
    @Resource
    private IProductionInfoService productionInfoService;

    /**
     * 获取制动器排产信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataReview:productionInfo:query')")
    @GetMapping(value = "/{productionId}")
    public AjaxResult getInfo(@PathVariable("productionId") Long productionId)
    {
        return AjaxResult.success(productionInfoService.selectProductionInfoByProductionId(productionId));
    }

    /**
     * 获取制动器排产信息详细信息
     */
    @GetMapping(value = "/getInfoByFactoryNumber/{factoryNumber}")
    public AjaxResult getInfoByFactoryNumber(@PathVariable("factoryNumber") String factoryNumber)
    {
        return AjaxResult.success(productionInfoService.selectProductionInfoByFactoryNumber(factoryNumber));
    }

    /**
     * 查询制动器排产信息列表
     */
    @PreAuthorize("@ss.hasPermi('dataReview:productionInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductionInfo productionInfo)
    {
        startPage();
        List<ProductionInfo> list = productionInfoService.selectProductionInfoList(productionInfo);
        return getDataTable(list);
    }

    /**
     * 导出制动器排产信息列表
     */
    @PreAuthorize("@ss.hasPermi('dataReview:productionInfo:export')")
    @Log(title = "制动器排产信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductionInfo productionInfo)
    {
        List<ProductionInfo> list = productionInfoService.selectProductionInfoList(productionInfo);
        ExcelUtil<ProductionInfo> util = new ExcelUtil<>(ProductionInfo.class);
        util.exportExcel(response, list, "制动器排产信息数据");
    }

    /**
     * 新增制动器排产信息
     */
    @PreAuthorize("@ss.hasPermi('dataReview:productionInfo:add')")
    @Log(title = "制动器排产信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProductionInfo productionInfo)
    {
        return toAjax(productionInfoService.insertProductionInfo(productionInfo));
    }

    /**
     * 修改制动器排产信息
     */
    @PreAuthorize("@ss.hasPermi('dataReview:productionInfo:edit')")
    @Log(title = "制动器排产信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductionInfo productionInfo)
    {
        return toAjax(productionInfoService.updateProductionInfo(productionInfo));
    }

    /**
     * 删除制动器排产信息
     */
    @PreAuthorize("@ss.hasPermi('dataReview:productionInfo:remove')")
    @Log(title = "制动器排产信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{productionIds}")
    public AjaxResult remove(@PathVariable List<Long> productionIds)
    {
        return toAjax(productionInfoService.deleteProductionInfoByProductionIds(productionIds));
    }
}