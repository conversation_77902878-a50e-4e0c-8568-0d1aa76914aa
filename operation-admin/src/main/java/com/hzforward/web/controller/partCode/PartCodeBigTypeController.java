package com.hzforward.web.controller.partCode;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.partCode.domain.PartCodeBigType;
import com.hzforward.partCode.service.IPartCodeBigTypeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 大类管理Controller
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@RestController
@RequestMapping("/partCode/partCodeBigType")
public class PartCodeBigTypeController extends BaseController
{
    @Resource
    private IPartCodeBigTypeService partCodeBigTypeService;

    /**
     * 查询大类管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeBigType:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartCodeBigType partCodeBigType)
    {
        startPage();
        List<PartCodeBigType> list = partCodeBigTypeService.selectPartCodeBigTypeList(partCodeBigType);
        return getDataTable(list);
    }

    /**
     * 获取大类字典表
     */
    @GetMapping("/dict")
    public AjaxResult getPartCodeBigTypeDict(PartCodeBigType partCodeBigType)
    {
        return AjaxResult.success(partCodeBigTypeService.getPartCodeBigTypeDict(partCodeBigType));
    }

    /**
     * 导出大类管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeBigType:export')")
    @Log(title = "大类管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PartCodeBigType partCodeBigType)
    {
        List<PartCodeBigType> list = partCodeBigTypeService.selectPartCodeBigTypeList(partCodeBigType);
        ExcelUtil<PartCodeBigType> util = new ExcelUtil<>(PartCodeBigType.class);
        util.exportExcel(response, list, "大类管理数据");
    }

    /**
     * 获取大类管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeBigType:query')")
    @GetMapping(value = "/{bigTypeId}")
    public AjaxResult getInfo(@PathVariable("bigTypeId") Long bigTypeId)
    {
        return AjaxResult.success(partCodeBigTypeService.selectPartCodeBigTypeById(bigTypeId));
    }

    /**
     * 新增大类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeBigType:add')")
    @Log(title = "大类管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PartCodeBigType partCodeBigType)
    {
        return toAjax(partCodeBigTypeService.insertPartCodeBigType(partCodeBigType));
    }

    /**
     * 修改大类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeBigType:edit')")
    @Log(title = "大类管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PartCodeBigType partCodeBigType)
    {
        return toAjax(partCodeBigTypeService.updatePartCodeBigType(partCodeBigType));
    }

    /**
     * 删除大类管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeBigType:remove')")
    @Log(title = "大类管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{bigTypeIds}")
    public AjaxResult remove(@PathVariable Long[] bigTypeIds)
    {
        return toAjax(partCodeBigTypeService.deletePartCodeBigTypeByIds(bigTypeIds));
    }
}
