package com.hzforward.equipmentManage.controller;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.domain.repair.RepairWorkingHours;
import com.hzforward.equipmentManage.service.IRepairWorkingHoursService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 设备维修工工时汇总信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@RestController
@RequestMapping("/equipmentManage/workingHours")
public class RepairWorkingHoursController extends BaseController
{
    @Resource
    private IRepairWorkingHoursService repairWorkingHoursService;

    /**
     * 查询设备维修工工时汇总信息列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:workingHours:list')")
    @GetMapping("/list")
    public TableDataInfo list(RepairWorkingHours repairWorkingHours)
    {
        startPage();
        List<RepairWorkingHours> list = repairWorkingHoursService.selectRepairWorkingHoursList(repairWorkingHours);
        return getDataTable(list);
    }

    /**
     * 导出设备维修工工时汇总信息列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:workingHours:export')")
    @Log(title = "设备维修工工时汇总信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RepairWorkingHours repairWorkingHours)
    {
        List<RepairWorkingHours> list = repairWorkingHoursService.selectRepairWorkingHoursList(repairWorkingHours);
        ExcelUtil<RepairWorkingHours> util = new ExcelUtil<>(RepairWorkingHours.class);
        util.exportExcel(response, list, "设备维修工工时汇总信息数据");
    }

    /**
     * 根据日期计算维修人的工作时长
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:workingHours:calculate')")
    @Log(title = "设备维修工工时汇总信息", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/calculateAllWorkingHoursByDate/{workDate}")
    public AjaxResult calculateAllWorkingHoursByDate(@PathVariable("workDate") Date workDate)
    {
        repairWorkingHoursService.calculateAllWorkingHoursByDate(workDate);
        return AjaxResult.success();
    }

    /**
     * 刷新维修工工作用时
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:workingHours:calculate')")
    @Log(title = "设备维修工工时汇总信息", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/{summaryId}")
    public AjaxResult reloadMaintainerWorkingHours(@PathVariable("summaryId") Long summaryId)
    {
        repairWorkingHoursService.reloadMaintainerWorkingHours(summaryId);
        return AjaxResult.success();
    }

}
