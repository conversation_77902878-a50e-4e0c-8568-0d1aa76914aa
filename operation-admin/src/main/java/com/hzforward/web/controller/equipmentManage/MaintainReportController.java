package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.domain.MaintainReport;
import com.hzforward.equipmentManage.service.IMaintainReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 保养结果Controller
 *
 * <AUTHOR>
 * @date 2023-11-23
 */
@RestController
@RequestMapping("/equipmentManage/maintainReport")
public class MaintainReportController extends BaseController
{
    @Resource
    private IMaintainReportService maintainReportService;

    /**
     * 查询保养结果列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainReport:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaintainReport maintainReport)
    {
        startPage();
        List<MaintainReport> list = maintainReportService.selectEquipmentMaintainReportList(maintainReport);
        return getDataTable(list);
    }

    /**
     * 导出保养结果列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainReport:export')")
    @Log(title = "保养结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaintainReport maintainReport)
    {
        List<MaintainReport> list = maintainReportService.selectEquipmentMaintainReportList(maintainReport);
        ExcelUtil<MaintainReport> util = new ExcelUtil<>(MaintainReport.class);
        util.exportExcel(response, list, "保养结果数据");
    }

    /**
     * 获取保养结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainReport:query')")
    @GetMapping(value = "/{maintainId}")
    public AjaxResult getInfo(@PathVariable("maintainId") Long maintainId)
    {
        return AjaxResult.success(maintainReportService.selectEquipmentMaintainReportByMaintainId(maintainId));
    }

    /**
     * 修改保养结果
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainReport:edit')")
    @Log(title = "保养结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaintainReport maintainReport)
    {
        return toAjax(maintainReportService.updateById(maintainReport));
    }

    /**
     * 删除保养结果
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainReport:remove')")
    @Log(title = "保养结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{maintainIds}")
    public AjaxResult remove(@PathVariable List<Long> maintainIds)
    {
        return toAjax(maintainReportService.removeBatchByIds(maintainIds));
    }

    /**
     * 生成保养结果表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:maintainReport:manualCreateMaintainPlan')")
    @GetMapping(value = "/manualCreateMaintainPlan/{maintainPlan}")
    public AjaxResult manualCreateMaintainPlan(@PathVariable("maintainPlan") String maintainPlan)
    {
        maintainReportService.manualCreateMaintainPlan(maintainPlan);
        return AjaxResult.success();
    }
}
