package com.hzforward.web.controller.dinerManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.dinnerManage.domain.DinnerManageProcessSet;
import com.hzforward.dinnerManage.service.IDinnerManageProcessSetService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 用餐审批设置Controller
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@RestController
@RequestMapping("/dinnerManage/processSet")
public class DinnerManageProcessSetController extends BaseController
{
    @Resource
    private IDinnerManageProcessSetService dinnerManageProcessSetService;

    /**
     * 查询用餐审批设置列表
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:processSet:list')")
    @GetMapping("/list")
    public AjaxResult list(DinnerManageProcessSet dinnerManageProcessSet)
    {
        return AjaxResult.success(dinnerManageProcessSetService.selectDinnerManageProcessSetList(dinnerManageProcessSet));
    }


    /**
     * 设置部门用餐审批人
     */
    @PreAuthorize("@ss.hasPermi('dinnerManage:processSet:edit')")
    @Log(title = "用餐审批设置", businessType = BusinessType.UPDATE)
    @PostMapping
    public AjaxResult setDinnerManageProcessSet(@RequestBody DinnerManageProcessSet dinnerManageProcessSet)
    {
        return toAjax(dinnerManageProcessSetService.setDinnerManageProcessSet(dinnerManageProcessSet));
    }

}
