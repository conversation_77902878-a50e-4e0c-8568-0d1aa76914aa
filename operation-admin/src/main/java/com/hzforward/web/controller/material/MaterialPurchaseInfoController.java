package com.hzforward.web.controller.material;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.material.domain.MaterialPurchaseInfo;
import com.hzforward.material.service.IMaterialPurchaseInfoService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 物料采购数据Controller
 *
 * <AUTHOR>
 * @date 2022-03-23
 */
@RestController
@RequestMapping("/material/materialPurchaseInfo")
public class MaterialPurchaseInfoController extends BaseController
{
    @Resource
    private IMaterialPurchaseInfoService materialPurchaseInfoService;

    /**
     * 查询物料采购数据列表
     */
    @PreAuthorize("@ss.hasPermi('material:materialPurchaseInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaterialPurchaseInfo materialPurchaseInfo)
    {
        startPage();
        List<MaterialPurchaseInfo> list = materialPurchaseInfoService.selectMaterialPurchaseInfoList(materialPurchaseInfo);
        return getDataTable(list);
    }

    /**
     * 导出物料采购数据列表
     */
    @PreAuthorize("@ss.hasPermi('material:materialPurchaseInfo:export')")
    @Log(title = "物料采购", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaterialPurchaseInfo materialPurchaseInfo)
    {
        List<MaterialPurchaseInfo> list = materialPurchaseInfoService.selectMaterialPurchaseInfoList(materialPurchaseInfo);
        ExcelUtil<MaterialPurchaseInfo> util = new ExcelUtil<>(MaterialPurchaseInfo.class);
        util.exportExcel(response, list, "物料采购数据数据");
    }

    /**
     * 获取物料采购数据详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:materialPurchaseInfo:query')")
    @GetMapping(value = "/{serialNumber}")
    public AjaxResult getInfo(@PathVariable("serialNumber") String serialNumber)
    {
        return AjaxResult.success(materialPurchaseInfoService.selectMaterialPurchaseInfoBySerialNumber(serialNumber));
    }

    /**
     * 新增物料采购数据
     */
    @PreAuthorize("@ss.hasPermi('material:materialPurchaseInfo:add')")
    @Log(title = "物料采购", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialPurchaseInfo materialPurchaseInfo)
    {
        return toAjax(materialPurchaseInfoService.insertMaterialPurchaseInfo(materialPurchaseInfo));
    }

    /**
     * 修改物料采购数据
     */
    @PreAuthorize("@ss.hasPermi('material:materialPurchaseInfo:edit')")
    @Log(title = "物料采购", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialPurchaseInfo materialPurchaseInfo)
    {
        return toAjax(materialPurchaseInfoService.updateMaterialPurchaseInfo(materialPurchaseInfo));
    }

    /**
     * 删除物料采购数据
     */
    @PreAuthorize("@ss.hasPermi('material:materialPurchaseInfo:remove')")
    @Log(title = "物料采购", businessType = BusinessType.DELETE)
    @DeleteMapping("/{serialNumbers}")
    public AjaxResult remove(@PathVariable String[] serialNumbers)
    {
        return toAjax(materialPurchaseInfoService.deleteMaterialPurchaseInfoBySerialNumbers(serialNumbers));
    }
    /*
     *   导出导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<MaterialPurchaseInfo> util = new ExcelUtil<>(MaterialPurchaseInfo.class);
        util.importTemplateExcel(response, "物料采购数据");
    }

    /*
     *   导入物料采购数据
     */
    @Log(title = "物料采购", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('material:materialPurchaseInfo:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<MaterialPurchaseInfo> util = new ExcelUtil<>(MaterialPurchaseInfo.class);
        List<MaterialPurchaseInfo> importMaterialPurchaseInfoList = util.importExcel(file.getInputStream());
        String operateName = getUsername();
        String message = materialPurchaseInfoService.importMaterialPurchaseInfo(importMaterialPurchaseInfoList, updateSupport, operateName);
        return AjaxResult.success(message);
    }

    @InitBinder  //类初始化是调用的方法注解
    public void initBinder(WebDataBinder binder) {
        binder.setAutoGrowNestedPaths(true);
        //给这个controller配置接收list的长度100000，仅在这个controller有效
        binder.setAutoGrowCollectionLimit(10000);
    }

    /**
     * 导出看板数据
     */
    @PreAuthorize("@ss.hasPermi('material:materialPurchaseInfo:export')")
    @Log(title = "物料采购", businessType = BusinessType.EXPORT)
    @PostMapping("/exportBulletinBoard")
    public void exportBulletinBoard(HttpServletResponse response, MaterialPurchaseInfo materialPurchaseInfo){
        materialPurchaseInfoService.exportBulletinBoard(response,materialPurchaseInfo);
    }

}
