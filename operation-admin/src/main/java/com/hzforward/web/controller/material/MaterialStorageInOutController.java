package com.hzforward.web.controller.material;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.material.domain.MaterialStorageInOut;
import com.hzforward.material.service.IMaterialStorageInOutService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 物料仓储出入库Controller
 *
 * <AUTHOR>
 * @date 2024-01-16
 */
@RestController
@RequestMapping("/material/storageInOut")
public class MaterialStorageInOutController extends BaseController
{
    @Resource
    private IMaterialStorageInOutService materialStorageInOutService;

    /**
     * 查询物料仓储出入库列表
     */
    @PreAuthorize("@ss.hasPermi('material:storageInOut:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaterialStorageInOut materialStorageInOut)
    {
        startPage();
        List<MaterialStorageInOut> list = materialStorageInOutService.selectMaterialStorageInOutList(materialStorageInOut);
        return getDataTable(list);
    }


    /**
     * 导出物料仓储出入库列表
     */
    @PreAuthorize("@ss.hasPermi('material:storageInOut:export')")
    @Log(title = "物料仓储出入库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaterialStorageInOut materialStorageInOut)
    {
        List<MaterialStorageInOut> list = materialStorageInOutService.selectMaterialStorageInOutList(materialStorageInOut);
        ExcelUtil<MaterialStorageInOut> util = new ExcelUtil<>(MaterialStorageInOut.class);
        util.exportExcel(response, list, "物料仓储出入库数据");
    }

    /**
     * 获取物料仓储出入库详细信息
     */
    @PreAuthorize("@ss.hasPermi('material:storageInOut:query')")
    @GetMapping(value = "/{inOutId}")
    public AjaxResult getInfo(@PathVariable("inOutId") Long inOutId)
    {
        return AjaxResult.success(materialStorageInOutService.selectMaterialStorageInOutByInOutId(inOutId));
    }

    /**
     * 新增物料仓储出入库
     */
    @PreAuthorize("@ss.hasPermi('material:storageInOut:add')")
    @Log(title = "物料仓储出入库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialStorageInOut materialStorageInOut)
    {
        return toAjax(materialStorageInOutService.insertMaterialStorageInOut(materialStorageInOut));
    }

    /**
     * 修改物料仓储出入库
     */
    @PreAuthorize("@ss.hasPermi('material:storageInOut:edit')")
    @Log(title = "物料仓储出入库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialStorageInOut materialStorageInOut)
    {
        return toAjax(materialStorageInOutService.updateMaterialStorageInOut(materialStorageInOut));
    }

    /**
     * 删除物料仓储出入库
     */
    @PreAuthorize("@ss.hasPermi('material:storageInOut:remove')")
    @Log(title = "物料仓储出入库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{inOutIds}")
    public AjaxResult remove(@PathVariable List<Long> inOutIds)
    {
        return toAjax(materialStorageInOutService.deleteMaterialStorageInOutByInOutIds(inOutIds));
    }

    @PreAuthorize("@ss.hasPermi('material:storageInOut:materialInOut')")
    @Log(title = "物料仓储出入库", businessType = BusinessType.INSERT)
    @PostMapping("/materialInOut")
    public AjaxResult materialInOut(@RequestBody MaterialStorageInOut materialStorageInOut)
    {
        return toAjax(materialStorageInOutService.materialInOut(materialStorageInOut));
    }
}
