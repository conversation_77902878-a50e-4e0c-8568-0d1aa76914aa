package com.hzforward.web.controller.processCheck;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.process.domain.ProcessDailyInspectionProjectReport;
import com.hzforward.process.service.IProcessDailyInspectionProjectReportService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 工序巡检报告Controller
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@RestController
@RequestMapping("/processInspectionProjectInfo/report")
public class ProcessDailyInspectionProjectReportController extends BaseController
{
    @Resource
    private IProcessDailyInspectionProjectReportService processInspectionProjectReportService;
	/**
	 * 获取工序巡检报告详细信息
	 */
	@PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:report:query')")
	@GetMapping(value = "/{reportId}")
	public AjaxResult getInfo(@PathVariable("reportId") Long reportId)
	{
		return AjaxResult.success(processInspectionProjectReportService.selectProcessInspectionProjectReportByReportId(reportId));
	}

    /**
     * 查询工序巡检报告列表
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:report:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessDailyInspectionProjectReport processDailyInspectionProjectReport)
    {
        startPage();
        List<ProcessDailyInspectionProjectReport> list = processInspectionProjectReportService.selectProcessInspectionProjectReportList(processDailyInspectionProjectReport);
        return getDataTable(list);
    }

    /**
     * 导出工序巡检报告列表
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:report:export')")
    @Log(title = "工序巡检报告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcessDailyInspectionProjectReport processDailyInspectionProjectReport)
    {
        List<ProcessDailyInspectionProjectReport> list = processInspectionProjectReportService.selectProcessInspectionProjectReportList(processDailyInspectionProjectReport);
        ExcelUtil<ProcessDailyInspectionProjectReport> util = new ExcelUtil<>(ProcessDailyInspectionProjectReport.class);
        util.exportExcel(response, list, "工序巡检报告数据");
    }

    /**
     * 新增工序巡检报告
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:report:add')")
    @Log(title = "工序巡检报告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessDailyInspectionProjectReport processDailyInspectionProjectReport)
    {
        return toAjax(processInspectionProjectReportService.insertProcessInspectionProjectReport(processDailyInspectionProjectReport));
    }

    /**
     * 修改工序巡检报告
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:report:edit')")
    @Log(title = "工序巡检报告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessDailyInspectionProjectReport processDailyInspectionProjectReport)
    {
        return toAjax(processInspectionProjectReportService.updateProcessInspectionProjectReport(processDailyInspectionProjectReport));
    }

    /**
     * 删除工序巡检报告
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:report:remove')")
    @Log(title = "工序巡检报告", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reportIds}")
    public AjaxResult remove(@PathVariable List<Long> reportIds)
    {
        return toAjax(processInspectionProjectReportService.deleteProcessInspectionProjectReportByReportIds(reportIds));
    }

    /**
     * 查询设备当天点检列表以及结果
     */
    @PostMapping("/getProcessDailyInspectionReportByDate")
    public AjaxResult selectProcessDailyInspectionInfoAndReportList(@RequestBody ProcessDailyInspectionProjectReport processDailyInspectionReport)
    {
        return  AjaxResult.success(processInspectionProjectReportService.getProcessDailyInspectionReportByDate(processDailyInspectionReport));
    }

    /**
     * 处理移动端点检请求
     */
    @PostMapping("/inspectionReportSubmit")
    public AjaxResult inspectionReportSubmit(@RequestBody ProcessDailyInspectionProjectReport processDailyInspectionProjectReport)
    {
        return toAjax(processInspectionProjectReportService.inspectionReportSubmit(processDailyInspectionProjectReport));
    }
}
