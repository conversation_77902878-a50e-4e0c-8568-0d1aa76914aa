package com.hzforward.web.controller.partCode;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.partCode.domain.PartCodeCompany;
import com.hzforward.partCode.service.IPartCodeCompanyService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 公司管理Controller
 *
 * <AUTHOR>
 * @date 2022-04-01
 */
@RestController
@RequestMapping("/partCode/partCodeCompany")
public class PartCodeCompanyController extends BaseController
{
    @Resource
    private IPartCodeCompanyService partCodeCompanyService;

    /**
     * 查询公司管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCompany:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartCodeCompany partCodeCompany)
    {
        startPage();
        List<PartCodeCompany> list = partCodeCompanyService.selectPartCodeCompanyList(partCodeCompany);
        return getDataTable(list);
    }

    /**
     * 查询公司字典表
     */
    @GetMapping("/dict")
    public AjaxResult getPartCodeCompanyDict()
    {
        return AjaxResult.success(partCodeCompanyService.getPartCodeCompanyDict());
    }

    /**
     * 导出公司管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCompany:export')")
    @Log(title = "公司管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PartCodeCompany partCodeCompany)
    {
        List<PartCodeCompany> list = partCodeCompanyService.selectPartCodeCompanyList(partCodeCompany);
        ExcelUtil<PartCodeCompany> util = new ExcelUtil<>(PartCodeCompany.class);
        util.exportExcel(response, list, "公司管理数据");
    }

    /**
     * 获取公司管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCompany:query')")
    @GetMapping(value = "/{companyId}")
    public AjaxResult getInfo(@PathVariable("companyId") Long companyId)
    {
        return AjaxResult.success(partCodeCompanyService.selectPartCodeCompanyById(companyId));
    }

    /**
     * 新增公司管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCompany:add')")
    @Log(title = "公司管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PartCodeCompany partCodeCompany)
    {
        return toAjax(partCodeCompanyService.insertPartCodeCompany(partCodeCompany));
    }

    /**
     * 修改公司管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCompany:edit')")
    @Log(title = "公司管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PartCodeCompany partCodeCompany)
    {
        return toAjax(partCodeCompanyService.updatePartCodeCompany(partCodeCompany));
    }

    /**
     * 删除公司管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeCompany:remove')")
    @Log(title = "公司管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{companyIds}")
    public AjaxResult remove(@PathVariable Long[] companyIds)
    {
        return toAjax(partCodeCompanyService.deletePartCodeCompanyByIds(companyIds));
    }

}
