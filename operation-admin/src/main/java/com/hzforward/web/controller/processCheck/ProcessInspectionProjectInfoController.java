package com.hzforward.web.controller.processCheck;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.process.domain.ProcessInspectionProjectInfo;
import com.hzforward.process.service.IProcessInspectionProjectInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 巡检内容Controller
 *
 * <AUTHOR>
 * @date 2024-07-17
 */
@RestController
@RequestMapping("/processInspectionProjectInfo/info")
public class ProcessInspectionProjectInfoController extends BaseController
{
    @Resource
    private IProcessInspectionProjectInfoService processInspectionProjectInfoService;

	/**
	 * 获取巡检内容详细信息
	 */
	@PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:info:query')")
	@GetMapping(value = "/{inspectionId}")
	public AjaxResult getInfo(@PathVariable("inspectionId") Long inspectionId)
	{
		return AjaxResult.success(processInspectionProjectInfoService.selectProcessInspectionProjectInfoByInspectionId(inspectionId));
	}

    /**
     * 查询巡检内容列表
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessInspectionProjectInfo processInspectionProjectInfo)
    {
        startPage();
        List<ProcessInspectionProjectInfo> list = processInspectionProjectInfoService.selectProcessInspectionProjectInfoList(processInspectionProjectInfo);
        return getDataTable(list);
    }

    /**
     * 导出巡检内容列表
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:info:export')")
    @Log(title = "巡检内容", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcessInspectionProjectInfo processInspectionProjectInfo)
    {
        List<ProcessInspectionProjectInfo> list = processInspectionProjectInfoService.selectProcessInspectionProjectInfoList(processInspectionProjectInfo);
        ExcelUtil<ProcessInspectionProjectInfo> util = new ExcelUtil<>(ProcessInspectionProjectInfo.class);
        util.exportExcel(response, list, "巡检内容数据");
    }

    /**
     * 新增巡检内容
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:info:add')")
    @Log(title = "巡检内容", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessInspectionProjectInfo processInspectionProjectInfo)
    {
        return toAjax(processInspectionProjectInfoService.insertProcessInspectionProjectInfo(processInspectionProjectInfo));
    }

    /**
     * 修改巡检内容
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:info:edit')")
    @Log(title = "巡检内容", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessInspectionProjectInfo processInspectionProjectInfo)
    {
        return toAjax(processInspectionProjectInfoService.updateProcessInspectionProjectInfo(processInspectionProjectInfo));
    }

    /**
     * 删除巡检内容
     */
    @PreAuthorize("@ss.hasPermi('processInspectionProjectInfo:info:remove')")
    @Log(title = "巡检内容", businessType = BusinessType.DELETE)
	@DeleteMapping("/{inspectionIds}")
    public AjaxResult remove(@PathVariable List<Long> inspectionIds)
    {
        return toAjax(processInspectionProjectInfoService.deleteProcessInspectionProjectInfoByInspectionIds(inspectionIds));
    }
}
