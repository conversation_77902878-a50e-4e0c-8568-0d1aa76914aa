package com.hzforward.web.controller.sync;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.sync.domain.SyncCableQualityProcessInspect;
import com.hzforward.sync.service.ISyncCableQualityProcessInspectService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电缆车间排产完成Controller
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@RestController
@RequestMapping("/sync/cableQualityProcessInspect")
public class SyncCableQualityProcessInspectController extends BaseController
{
    @Resource
    private ISyncCableQualityProcessInspectService syncCableQualityProcessInspectService;

    /**
     * 查询电缆车间排产完成列表
     */
    @PreAuthorize("@ss.hasPermi('sync:cableQualityProcessInspect:list')")
    @GetMapping("/list")
    public TableDataInfo list(SyncCableQualityProcessInspect syncCableQualityProcessInspect)
    {
        startPage();
        List<SyncCableQualityProcessInspect> list = syncCableQualityProcessInspectService.selectSyncCableQualityProcessInspectList(syncCableQualityProcessInspect);
        return getDataTable(list);
    }

    /**
     * 导出电缆车间排产完成列表
     */
    @PreAuthorize("@ss.hasPermi('sync:cableQualityProcessInspect:export')")
    @Log(title = "电缆车间排产完成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SyncCableQualityProcessInspect syncCableQualityProcessInspect)
    {
        List<SyncCableQualityProcessInspect> list = syncCableQualityProcessInspectService.selectSyncCableQualityProcessInspectList(syncCableQualityProcessInspect);
        ExcelUtil<SyncCableQualityProcessInspect> util = new ExcelUtil<>(SyncCableQualityProcessInspect.class);
        util.exportExcel(response, list, "电缆车间排产完成数据");
    }

    /**
     * 导入
     */
    @PreAuthorize("@ss.hasPermi('sync:cableQualityProcessInspect:export')")
    @Log(title = "电缆车间排产完成", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<SyncCableQualityProcessInspect> util = new ExcelUtil<>(SyncCableQualityProcessInspect.class);
        List<SyncCableQualityProcessInspect> qualityProcessInspects = util.importExcel(file.getInputStream());
        String message = syncCableQualityProcessInspectService.importData(qualityProcessInspects);
        return AjaxResult.success(message);
    }

    /**
     * 下载导入模板
     */
    @PreAuthorize("@ss.hasPermi('sync:cableQualityProcessInspect:export')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SyncCableQualityProcessInspect> util = new ExcelUtil<>(SyncCableQualityProcessInspect.class);
        util.importTemplateExcel(response, "电缆车间排产完成");
    }

	/**
	 * 获取电缆车间排产完成详细信息
	 */
	@PreAuthorize("@ss.hasPermi('sync:cableQualityProcessInspect:query')")
	@GetMapping(value = "/{id}")
	public AjaxResult getInfo(@PathVariable("id") Long id)
	{
		return AjaxResult.success(syncCableQualityProcessInspectService.getById(id));
	}

    /**
     * 新增电缆车间排产完成
     */
    @PreAuthorize("@ss.hasPermi('sync:cableQualityProcessInspect:add')")
    @Log(title = "电缆车间排产完成", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SyncCableQualityProcessInspect syncCableQualityProcessInspect)
    {
        return toAjax(syncCableQualityProcessInspectService.insert(syncCableQualityProcessInspect));
    }

    /**
     * 修改电缆车间排产完成
     */
    @PreAuthorize("@ss.hasPermi('sync:cableQualityProcessInspect:edit')")
    @Log(title = "电缆车间排产完成", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SyncCableQualityProcessInspect syncCableQualityProcessInspect)
    {
        return toAjax(syncCableQualityProcessInspectService.updateById(syncCableQualityProcessInspect));
    }

    /**
     * 删除电缆车间排产完成
     */
    @PreAuthorize("@ss.hasPermi('sync:cableQualityProcessInspect:remove')")
    @Log(title = "电缆车间排产完成", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<Long> ids)
    {
        return toAjax(syncCableQualityProcessInspectService.removeBatchByIds(ids));
    }
}
