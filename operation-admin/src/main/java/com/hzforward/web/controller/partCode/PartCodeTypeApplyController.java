package com.hzforward.web.controller.partCode;

import java.util.List;

import com.hzforward.common.annotation.RepeatSubmit;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.partCode.domain.PartCodeTypeApply;
import com.hzforward.partCode.service.IPartCodeTypeApplyService;
import com.hzforward.common.core.page.TableDataInfo;

import javax.annotation.Resource;

/**
 * 小类申请Controller
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@RestController
@RequestMapping("/partCode/partCodeTypeApply")
public class PartCodeTypeApplyController extends BaseController
{
    @Resource
    private IPartCodeTypeApplyService partCodeTypeApplyService;

    /**
     * 查询小类申请列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeTypeApply:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartCodeTypeApply partCodeTypeApply)
    {
        startPage();
        List<PartCodeTypeApply> list = partCodeTypeApplyService.selectPartCodeTypeApplyList(partCodeTypeApply);
        return getDataTable(list);
    }


    /**
     * 获取小类申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeTypeApply:query')")
    @GetMapping(value = "/{applyId}")
    public AjaxResult getInfo(@PathVariable("applyId") Long applyId)
    {
        return AjaxResult.success(partCodeTypeApplyService.selectPartCodeTypeApplyByApplyId(applyId));
    }

    /**
     * 新增小类申请
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeTypeApply:add')")
    @Log(title = "小类申请", businessType = BusinessType.INSERT)
    @PostMapping
    @RepeatSubmit
    public AjaxResult add(@RequestBody PartCodeTypeApply partCodeTypeApply)
    {
        return toAjax(partCodeTypeApplyService.insertPartCodeTypeApply(partCodeTypeApply));
    }

    /**
     * 修改小类申请
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeTypeApply:edit')")
    @Log(title = "小类申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PartCodeTypeApply partCodeTypeApply)
    {
        return toAjax(partCodeTypeApplyService.updatePartCodeTypeApply(partCodeTypeApply));
    }

    /**
     * 删除小类申请
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeTypeApply:remove')")
    @Log(title = "小类申请", businessType = BusinessType.DELETE)
	@DeleteMapping("/{applyIds}")
    public AjaxResult remove(@PathVariable Long[] applyIds)
    {
        return toAjax(partCodeTypeApplyService.deletePartCodeTypeApplyByApplyIds(applyIds));
    }

    /**
     * 驳回申请
     */
    @Log(title = "小类申请", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('partCode:partCodeTypeApply:audit')")
    @PostMapping("/rejectPartCodeTypeApply")
    public AjaxResult rejectPartCodeTypeApply(@RequestBody PartCodeTypeApply partCodeTypeApply)
    {
        return toAjax(partCodeTypeApplyService.rejectPartCodeTypeApply(partCodeTypeApply));
    }

    /**
     * 通过申请
     */
    @Log(title = "小类申请", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('partCode:partCodeTypeApply:audit')")
    @GetMapping(value = "/approvedPartCodeTypeApply/{applyId}")
    public AjaxResult approvedPartCodeTypeApply(@PathVariable("applyId") Long applyId)
    {
        return toAjax(partCodeTypeApplyService.approvedPartCodeTypeApply(applyId));
    }


    /**
     * 查询未处理消息数
     */
    @GetMapping(value = "/getMessageCounts")
    @PreAuthorize("@ss.hasPermi('partCode:partCodeTypeApply:audit')")
    public AjaxResult getMessageCounts()
    {
        return AjaxResult.success(partCodeTypeApplyService.getMessageCounts());
    }
}
