package com.hzforward.web.controller.system;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.constant.Constants;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.domain.entity.SysMenu;
import com.hzforward.common.core.domain.entity.SysUser;
import com.hzforward.common.core.domain.model.LoginBody;
import com.hzforward.common.utils.SecurityUtils;
import com.hzforward.framework.web.service.SysLoginService;
import com.hzforward.framework.web.service.SysPermissionService;
import com.hzforward.system.service.ISysMenuService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Resource
    private SysLoginService loginService;

    @Resource
    private ISysMenuService menuService;

    @Resource
    private SysPermissionService permissionService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 钉钉用户免登陆
     */
    @Anonymous
    @GetMapping(value = "/dingTalkUserLogin/{authCode}&{corpId}")
    public AjaxResult dingTalkUserLogin(@PathVariable String authCode, @PathVariable("corpId") String corpId) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.dingTalkUserLogin(authCode);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 钉钉用户扫码免登陆
     */
    @Anonymous
    @GetMapping(value = "/dingTalkUserScanLogin/{authCode}")
    public AjaxResult dingTalkUserScanLogin(@PathVariable String authCode) {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.dingTalkUserScanLogin(authCode);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);

        // TODO 增加临时权限
        // 增加临时角色

        // 临时权限


        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
