package com.hzforward.web.controller.equipmentManage;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.domain.Section;
import com.hzforward.equipmentManage.service.ISectionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备工段Controller
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@RestController
@RequestMapping("/equipmentManage/section")
public class SectionController extends BaseController
{
    @Resource
    private ISectionService sectionService;

    /**
     * 查询设备工段列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:section:list')")
    @GetMapping("/list")
    public TableDataInfo list(Section section)
    {
        startPage();
        List<Section> list = sectionService.selectSectionList(section);
        return getDataTable(list);
    }

    /**
     * 导出设备工段列表
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:section:export')")
    @Log(title = "设备工段", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Section section)
    {
        List<Section> list = sectionService.selectSectionList(section);
        ExcelUtil<Section> util = new ExcelUtil<>(Section.class);
        util.exportExcel(response, list, "设备工段数据");
    }

    /**
     * 获取工段字典表
     */
    @GetMapping("/getDepartmentDict/{factoryArea}")
    public AjaxResult getDepartmentDict(@PathVariable String factoryArea)
    {
        return AjaxResult.success(sectionService.getDepartmentDict(factoryArea));
    }

    /**
     * 获取工段字典表
     */
    @GetMapping("/getSectionDict")
    public AjaxResult getSectionDict(Section section)
    {
        return AjaxResult.success(sectionService.getSectionDict(section));
    }

    /**
     * 根据用户Name获取用户负责的工段
     */
    @GetMapping("/getSectionDict/{userName}")
    public AjaxResult getUserSections(@PathVariable String userName)
    {
        return AjaxResult.success(sectionService.getUserSections(userName));
    }

	/**
	 * 获取设备工段详细信息
	 */
	@PreAuthorize("@ss.hasPermi('equipmentManage:section:query')")
	@GetMapping(value = "/{sectionId}")
	public AjaxResult getInfo(@PathVariable("sectionId") Long sectionId)
	{
		return AjaxResult.success(sectionService.getSectionById(sectionId));
	}

    /**
     * 新增设备工段
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:section:add')")
    @Log(title = "设备工段", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Section section)
    {
        return toAjax(sectionService.saveSection(section));
    }

    /**
     * 修改设备工段
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:section:edit')")
    @Log(title = "设备工段", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Section section)
    {
        return toAjax(sectionService.updateSection(section));
    }

    /**
     * 删除设备工段
     */
    @PreAuthorize("@ss.hasPermi('equipmentManage:section:remove')")
    @Log(title = "设备工段", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sectionIds}")
    public AjaxResult remove(@PathVariable List<Long> sectionIds)
    {
        return toAjax(sectionService.removeBatchByIds(sectionIds));
    }
}
