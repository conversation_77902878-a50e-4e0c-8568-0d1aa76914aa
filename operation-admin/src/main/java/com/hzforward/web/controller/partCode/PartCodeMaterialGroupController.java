package com.hzforward.web.controller.partCode;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.partCode.domain.PartCodeMaterialGroup;
import com.hzforward.partCode.service.IPartCodeMaterialGroupService;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 物料组管理Controller
 *
 * <AUTHOR>
 * @date 2022-04-18
 */
@RestController
@RequestMapping("/partCode/partCodeMaterialGroup")
public class PartCodeMaterialGroupController extends BaseController
{
    @Resource
    private IPartCodeMaterialGroupService partCodeMaterialGroupService;

    /**
     * 查询物料组管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeMaterialGroup:list')")
    @GetMapping("/list")
    public TableDataInfo list(PartCodeMaterialGroup partCodeMaterialGroup)
    {
        startPage();
        List<PartCodeMaterialGroup> list = partCodeMaterialGroupService.selectPartCodeMaterialGroupList(partCodeMaterialGroup);
        return getDataTable(list);
    }

    /**
     * 获取物料组管理字典表
     */
    @GetMapping("/dict")
    public AjaxResult getPartCodeMaterialGroupDict(PartCodeMaterialGroup partCodeMaterialGroup)
    {
        return AjaxResult.success(partCodeMaterialGroupService.getPartCodeMaterialGroupDict(partCodeMaterialGroup));
    }

    /**
     * 导出物料组管理列表
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeMaterialGroup:export')")
    @Log(title = "物料组管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PartCodeMaterialGroup partCodeMaterialGroup)
    {
        List<PartCodeMaterialGroup> list = partCodeMaterialGroupService.selectPartCodeMaterialGroupList(partCodeMaterialGroup);
        ExcelUtil<PartCodeMaterialGroup> util = new ExcelUtil<>(PartCodeMaterialGroup.class);
        util.exportExcel(response, list, "物料组管理数据");
    }

    /**
     * 获取物料组管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeMaterialGroup:query')")
    @GetMapping(value = "/{materialId}")
    public AjaxResult getInfo(@PathVariable("materialId") Long materialId)
    {
        return AjaxResult.success(partCodeMaterialGroupService.selectPartCodeMaterialGroupByMaterialId(materialId));
    }

    /**
     * 新增物料组管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeMaterialGroup:add')")
    @Log(title = "物料组管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PartCodeMaterialGroup partCodeMaterialGroup)
    {
        return toAjax(partCodeMaterialGroupService.insertPartCodeMaterialGroup(partCodeMaterialGroup));
    }

    /**
     * 修改物料组管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeMaterialGroup:edit')")
    @Log(title = "物料组管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PartCodeMaterialGroup partCodeMaterialGroup)
    {
        return toAjax(partCodeMaterialGroupService.updatePartCodeMaterialGroup(partCodeMaterialGroup));
    }

    /**
     * 删除物料组管理
     */
    @PreAuthorize("@ss.hasPermi('partCode:partCodeMaterialGroup:remove')")
    @Log(title = "物料组管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{materialIds}")
    public AjaxResult remove(@PathVariable Long[] materialIds)
    {
        return toAjax(partCodeMaterialGroupService.deletePartCodeMaterialGroupByMaterialIds(materialIds));
    }
}
