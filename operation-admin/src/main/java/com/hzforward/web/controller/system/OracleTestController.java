package com.hzforward.web.controller.system;

import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.system.service.IOracleTestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Oracle数据库测试控制器
 * 提供Oracle数据库连接和操作的测试接口
 *
 * <AUTHOR>
 */
@Api(tags = "Oracle数据库测试")
@RestController
@RequestMapping("/system/oracle")
public class OracleTestController extends BaseController {

    @Autowired
    private IOracleTestService oracleTestService;

    /**
     * 测试Oracle数据库连接
     */
    @ApiOperation("测试Oracle数据库连接")
    @GetMapping("/testConnection")
    public AjaxResult testConnection() {
        try {
            String result = oracleTestService.testOracleConnectionWithAnnotation();
            return AjaxResult.success("连接测试成功", result);
        } catch (Exception e) {
            return AjaxResult.error("连接测试失败: " + e.getMessage());
        }
    }

    /**
     * 获取Oracle数据库时间
     */
    @ApiOperation("获取Oracle数据库时间")
    @GetMapping("/getDatabaseTime")
    public AjaxResult getDatabaseTime() {
        try {
            String time = oracleTestService.getOracleDatabaseTime();
            return AjaxResult.success("获取数据库时间成功", time);
        } catch (Exception e) {
            return AjaxResult.error("获取数据库时间失败: " + e.getMessage());
        }
    }

    /**
     * 获取Oracle数据库版本
     */
    @ApiOperation("获取Oracle数据库版本")
    @GetMapping("/getVersion")
    public AjaxResult getVersion() {
        try {
            String version = oracleTestService.getOracleVersion();
            return AjaxResult.success("获取数据库版本成功", version);
        } catch (Exception e) {
            return AjaxResult.error("获取数据库版本失败: " + e.getMessage());
        }
    }

    /**
     * 获取表空间信息
     */
    @ApiOperation("获取表空间信息")
    @GetMapping("/getTablespaces")
    public AjaxResult getTablespaces() {
        try {
            List<Map<String, Object>> tablespaces = oracleTestService.getTablespaces();
            return AjaxResult.success("获取表空间信息成功", tablespaces);
        } catch (Exception e) {
            return AjaxResult.error("获取表空间信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户表列表
     */
    @ApiOperation("获取用户表列表")
    @GetMapping("/getUserTables")
    public AjaxResult getUserTables() {
        try {
            List<Map<String, Object>> tables = oracleTestService.getUserTables();
            return AjaxResult.success("获取用户表列表成功", tables);
        } catch (Exception e) {
            return AjaxResult.error("获取用户表列表失败: " + e.getMessage());
        }
    }

    /**
     * 执行自定义SQL查询
     */
    @ApiOperation("执行自定义SQL查询")
    @PostMapping("/executeQuery")
    public AjaxResult executeQuery(@RequestParam String sql) {
        try {
            // 简单的SQL注入防护
            if (sql.toLowerCase().contains("drop") || 
                sql.toLowerCase().contains("delete") || 
                sql.toLowerCase().contains("update") ||
                sql.toLowerCase().contains("insert") ||
                sql.toLowerCase().contains("create") ||
                sql.toLowerCase().contains("alter")) {
                return AjaxResult.error("为了安全考虑，只允许执行SELECT查询");
            }
            
            List<Map<String, Object>> result = oracleTestService.executeCustomQuery(sql);
            return AjaxResult.success("查询执行成功", result);
        } catch (Exception e) {
            return AjaxResult.error("查询执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取连接池状态
     */
    @ApiOperation("获取连接池状态")
    @GetMapping("/getConnectionPoolStatus")
    public AjaxResult getConnectionPoolStatus() {
        try {
            Map<String, Object> status = oracleTestService.getConnectionPoolStatus();
            return AjaxResult.success("获取连接池状态成功", status);
        } catch (Exception e) {
            return AjaxResult.error("获取连接池状态失败: " + e.getMessage());
        }
    }
}
