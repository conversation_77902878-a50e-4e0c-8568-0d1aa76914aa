package com.hzforward.web.controller.processCheck;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.hzforward.process.domain.ProcessInfo;
import com.hzforward.process.service.IProcessInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.common.core.page.TableDataInfo;

/**
 * 巡检工序基础数据Controller
 *
 * <AUTHOR>
 * @date 2024-07-12
 */
@RestController
@RequestMapping("/process/info")
public class ProcessInfoController extends BaseController
{
    @Resource
    private IProcessInfoService processInfoService;

	/**
	 * 获取巡检工序基础数据详细信息
	 */
	@PreAuthorize("@ss.hasPermi('process:info:query')")
	@GetMapping(value = "/{processId}")
	public AjaxResult getInfo(@PathVariable("processId") Long processId)
	{
		return AjaxResult.success(processInfoService.selectProcessInfoByProcessId(processId));
	}

    /**
     * 查询设备基础数据
     */
    @GetMapping("/getProcessInfoByProcessNo/{processNo}")
    public AjaxResult getProcessInfoByEquipNo(@PathVariable("processNo") String processNo)
    {
        return AjaxResult.success(processInfoService.selectProcessInfoByEquipNo(processNo));
    }

    /**
     * 查询巡检工序基础数据列表
     */
    @PreAuthorize("@ss.hasPermi('process:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProcessInfo processInfo)
    {
        startPage();
        List<ProcessInfo> list = processInfoService.selectProcessInfoList(processInfo);
        return getDataTable(list);
    }

    /**
     * 导出巡检工序基础数据列表
     */
    @PreAuthorize("@ss.hasPermi('process:info:export')")
    @Log(title = "巡检工序基础数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProcessInfo processInfo)
    {
        List<ProcessInfo> list = processInfoService.selectProcessInfoList(processInfo);
        ExcelUtil<ProcessInfo> util = new ExcelUtil<>(ProcessInfo.class);
        util.exportExcel(response, list, "巡检工序基础数据数据");
    }

    /**
     * 新增巡检工序基础数据
     */
    @PreAuthorize("@ss.hasPermi('process:info:add')")
    @Log(title = "巡检工序基础数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProcessInfo processInfo)
    {
        return toAjax(processInfoService.insertProcessInfo(processInfo));
    }

    /**
     * 修改巡检工序基础数据
     */
    @PreAuthorize("@ss.hasPermi('process:info:edit')")
    @Log(title = "巡检工序基础数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProcessInfo processInfo)
    {
        return toAjax(processInfoService.updateProcessInfo(processInfo));
    }

    /**
     * 删除巡检工序基础数据
     */
    @PreAuthorize("@ss.hasPermi('process:info:remove')")
    @Log(title = "巡检工序基础数据", businessType = BusinessType.DELETE)
	@DeleteMapping("/{processIds}")
    public AjaxResult remove(@PathVariable List<Long> processIds)
    {
        return toAjax(processInfoService.deleteProcessInfoByProcessIds(processIds));
    }
}
