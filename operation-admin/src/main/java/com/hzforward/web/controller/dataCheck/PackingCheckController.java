package com.hzforward.web.controller.dataCheck;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.dataCheck.domain.PackingCheck;
import com.hzforward.dataCheck.service.IPackingCheckService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 装箱核对Controller
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
@RestController
@RequestMapping("/dataCheck/packingCheck")
public class PackingCheckController extends BaseController
{
    @Resource
    private IPackingCheckService packingCheckService;

    /**
     * 获取装箱核对详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:packingCheck:query')")
    @GetMapping(value = "/{packingId}")
    public AjaxResult getInfo(@PathVariable("packingId") Long packingId)
    {
        return AjaxResult.success(packingCheckService.selectPackingCheckByPackingId(packingId));
    }

    /**
     * 获取装箱核对详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:packingCheck:query')")
    @GetMapping(value = "/getInfoByOrderNumber/{workOrderNumber}")
    public AjaxResult getInfoByOrderNumber(@PathVariable("workOrderNumber") String workOrderNumber)
    {
        return AjaxResult.success(packingCheckService.selectPackingCheckByOrderNumber(workOrderNumber));
    }

    /**
     * 获取装箱核对详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:packingCheck:query')")
    @GetMapping(value = "/getInfoByListNumber/{listNumber}")
    public AjaxResult getInfoByListNumber(@PathVariable("listNumber") String listNumber)
    {
        return AjaxResult.success(packingCheckService.selectPackingCheckByListNumber(listNumber));
    }

    /**
     * 查询装箱核对列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:packingCheck:list')")
    @GetMapping("/list")
    public TableDataInfo list(PackingCheck packingCheck)
    {
        startPage();
        List<PackingCheck> list = packingCheckService.selectPackingCheckList(packingCheck);
        return getDataTable(list);
    }


    /**
     * 删除装箱核对信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:packingCheck:remove')")
    @DeleteMapping(value = "/{packingId}")
    public AjaxResult remove(@PathVariable("packingId") Long packingId)
    {
        return AjaxResult.success(packingCheckService.deletePackingCheckByPackingId(packingId));
    }

    /**
     * 同步装箱核对数据
     */
    @PostMapping(value = "/sendPackingData")
    @Anonymous
    public AjaxResult sendPackingData(@RequestBody PackingCheck packingCheck)
    {
        return toAjax(packingCheckService.sendPackingData(packingCheck));
    }

    /**
     * 上传图片信息
     */
    @PostMapping(value = "/uploadImages")
    @PreAuthorize("@ss.hasPermi('dataCheck:applet:packingCheck')")
    public AjaxResult uploadImages(@RequestBody PackingCheck packingCheck)
    {
        return toAjax(packingCheckService.uploadImages(packingCheck));
    }


    @GetMapping(value = "/barCodeScan")
    @PreAuthorize("@ss.hasPermi('dataCheck:applet:packingCheck')")
    public AjaxResult barCodeScan(Long packingId, String barCode)
    {
        return toAjax(packingCheckService.barCodeScan(packingId, barCode));
    }

    @GetMapping(value = "/electricalCheck")
    @PreAuthorize("@ss.hasPermi('dataCheck:applet:packingCheck')")
    public AjaxResult electricalCheck(Long packingId, String shippingMarkCode)
    {
        return toAjax(packingCheckService.electricalCheck(packingId, shippingMarkCode));
    }

}
