package com.hzforward.web.controller.dataCheck;

import com.hzforward.common.annotation.Anonymous;
import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.dataCheck.domain.SpeedLimiterProduction;
import com.hzforward.dataCheck.service.ISpeedLimiterProductionService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 限速器排产Controller
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@RestController
@RequestMapping("/dataCheck/speedLimiterProduction")
public class SpeedLimiterProductionController extends BaseController
{
    @Resource
    private ISpeedLimiterProductionService speedLimiterProductionService;

    /**
     * 获取限速器排产详细信息
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:speedLimiterProduction:query')")
    @GetMapping(value = "/{speedLimitId}")
    public AjaxResult getInfo(@PathVariable("speedLimitId") Long speedLimitId)
    {
        return AjaxResult.success(speedLimiterProductionService.selectSpeedLimiterProductionBySpeedLimitId(speedLimitId));
    }

    /**
     * 获取缓冲器排产详细信息
     */
    @Anonymous
    @GetMapping(value = "getInfoByFactoryNumber/{factoryNumber}")
    public AjaxResult getInfoByFactoryNumber(@PathVariable("factoryNumber") String factoryNumber)
    {
        return AjaxResult.success(speedLimiterProductionService.selectSpeedLimiterProductionByFactoryNumber(factoryNumber));
    }

    /**
     * 查询限速器排产列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:speedLimiterProduction:list')")
    @GetMapping("/list")
    public TableDataInfo list(SpeedLimiterProduction speedLimiterProduction)
    {
        startPage();
        List<SpeedLimiterProduction> list = speedLimiterProductionService.selectSpeedLimiterProductionList(speedLimiterProduction);
        return getDataTable(list);
    }

    /**
     * 导出限速器排产列表
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:speedLimiterProduction:export')")
    @Log(title = "限速器排产", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SpeedLimiterProduction speedLimiterProduction)
    {
        List<SpeedLimiterProduction> list = speedLimiterProductionService.selectSpeedLimiterProductionList(speedLimiterProduction);
        ExcelUtil<SpeedLimiterProduction> util = new ExcelUtil<>(SpeedLimiterProduction.class);
        util.exportExcel(response, list, "限速器排产数据");
    }

    /**
     * 新增限速器排产
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:speedLimiterProduction:add')")
    @Log(title = "限速器排产", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SpeedLimiterProduction speedLimiterProduction)
    {
        return toAjax(speedLimiterProductionService.insertSpeedLimiterProduction(speedLimiterProduction));
    }

    /**
     * 修改限速器排产
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:speedLimiterProduction:edit')")
    @Log(title = "限速器排产", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SpeedLimiterProduction speedLimiterProduction)
    {
        return toAjax(speedLimiterProductionService.updateSpeedLimiterProduction(speedLimiterProduction));
    }

    /**
     * 修改缓冲器排产扫描标志
     */
    @Anonymous
    @PutMapping("/updateScanFlag/{speedLimitId}")
    public AjaxResult updateScanFlag(@PathVariable("speedLimitId") Long speedLimitId)
    {
        return toAjax(speedLimiterProductionService.updateScanFlag(speedLimitId));
    }

    /**
     * 删除限速器排产
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:speedLimiterProduction:remove')")
    @Log(title = "限速器排产", businessType = BusinessType.DELETE)
	@DeleteMapping("/{speedLimitIds}")
    public AjaxResult remove(@PathVariable List<Long> speedLimitIds)
    {
        return toAjax(speedLimiterProductionService.deleteSpeedLimiterProductionBySpeedLimitIds(speedLimitIds));
    }

    /**
     * 下载限速器排产导入模板
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:speedLimiterProduction:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SpeedLimiterProduction> util = new ExcelUtil<>(SpeedLimiterProduction.class);
        util.importTemplateExcel(response, "限速器排产信息");
    }

    /**
     * 导入限速器排产
     */
    @PreAuthorize("@ss.hasPermi('dataCheck:speedLimiterProduction:import')")
    @Log(title = "限速器排产", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<SpeedLimiterProduction> util = new ExcelUtil<>(SpeedLimiterProduction.class);
        List<SpeedLimiterProduction> speedLimiterProductionList = util.importExcel(file.getInputStream());
        String message = speedLimiterProductionService.importData(speedLimiterProductionList, updateSupport);
        return AjaxResult.success(message);
    }
}
