package com.hzforward.web.controller.itProperty;

import com.hzforward.common.annotation.Log;
import com.hzforward.common.core.controller.BaseController;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.page.TableDataInfo;
import com.hzforward.common.enums.BusinessType;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.itProperty.domain.ItPropertyManage;
import com.hzforward.itProperty.service.IItPropertyManageService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 资产管理Controller
 *
 * <AUTHOR>
 * @date 2022-06-17
 */
@RestController
@RequestMapping("/itProperty/itPropertyManage")
public class ItPropertyManageController extends BaseController
{
    @Resource
    private IItPropertyManageService itPropertyManageService;

    /**
     * 查询资产管理列表
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyManage:list')")
    @GetMapping("/list")
    public TableDataInfo list(ItPropertyManage itPropertyManage)
    {
        startPage();
        List<ItPropertyManage> list = itPropertyManageService.selectItPropertyManageList(itPropertyManage);
        return getDataTable(list);
    }

    /**
     * 导出资产管理列表
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyManage:export')")
    @Log(title = "资产管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ItPropertyManage itPropertyManage)
    {
        List<ItPropertyManage> list = itPropertyManageService.selectItPropertyManageList(itPropertyManage);
        ExcelUtil<ItPropertyManage> util = new ExcelUtil<>(ItPropertyManage.class);
        util.exportExcel(response, list, "资产管理数据");
    }

    /**
     * 获取资产管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyManage:query')")
    @GetMapping(value = "/{propertyId}")
    public AjaxResult getInfo(@PathVariable("propertyId") Long propertyId)
    {
        return AjaxResult.success(itPropertyManageService.selectItPropertyManageByPropertyId(propertyId));
    }

    /**
     * 新增资产管理
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyManage:add')")
    @Log(title = "资产管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ItPropertyManage itPropertyManage)
    {
        return toAjax(itPropertyManageService.insertItPropertyManage(itPropertyManage));
    }

    /**
     * 修改资产管理
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyManage:edit')")
    @Log(title = "资产管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ItPropertyManage itPropertyManage)
    {
        return toAjax(itPropertyManageService.updateItPropertyManage(itPropertyManage));
    }

    /**
     * 删除资产管理
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyManage:remove')")
    @Log(title = "资产管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{propertyIds}")
    public AjaxResult remove(@PathVariable Long[] propertyIds)
    {
        return toAjax(itPropertyManageService.deleteItPropertyManageByPropertyIds(propertyIds));
    }

    /**
     * 获取资产管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('itProperty:itPropertyManage:addMacAddress')")
    @GetMapping(value = "/addMacAddress/{macAddress}&{telnetIp}")
    public AjaxResult addMacAddress(@PathVariable("macAddress") String macAddress, @PathVariable("telnetIp") String telnetIp)
    {
        return itPropertyManageService.addMacAddress(macAddress,telnetIp);
    }
}
