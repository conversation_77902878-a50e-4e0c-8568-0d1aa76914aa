package com.hzforward.common.utils;

import java.util.Date;
import java.util.List;

/**
 * 处理查询日期范围工具类
 *
 * <AUTHOR>
 */
public class QueryUtils
{
    public static Date getQueryStartDate(List<Date> queryDateRange)
    {
        if(StringUtils.isEmpty(queryDateRange)) {
            return null;
        }
        return queryDateRange.get(0);
    }

    public static Date getQueryEndDate(List<Date> queryDateRange)
    {
        if(StringUtils.isEmpty(queryDateRange)) {
            return null;
        }
        return queryDateRange.get(1);
    }
}
