package com.hzforward.common.utils;

import com.hzforward.common.exception.ServiceException;
import lombok.Data;

import java.sql.Time;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SplitDateUtils {

    @Data
    public static class SplitDate {
        private Date date;
        private Time beginTime;
        private Time endTime;
    }

    /**
     * 传入两个时间范围，返回这两个时间范围内的所有日期，并保存在一个集合中
     * @param dateTimeRange 日期时间范围
     * @return list
     */
    public static List<SplitDate> splitDate(List<Date> dateTimeRange){
        if(StringUtils.isEmpty(dateTimeRange) || dateTimeRange.size() != 2 || dateTimeRange.get(0).after(dateTimeRange.get(1))) {
            throw new ServiceException("请选择正确的时间范围!");
        }

        //1.创建一个放所有日期的集合
        List<SplitDate> dates = new ArrayList<>();

        //2.获取开始结束时间
        Date dateTimeBegin = dateTimeRange.get(0);
        Date dateTimeEnd = dateTimeRange.get(1);

        if(DateUtils.isSameDay(dateTimeBegin, dateTimeEnd)) {

            SplitDate splitDate = new SplitDate();
            splitDate.setDate(DateUtils.parseDate(DateUtils.YYYY_MM_DD, dateTimeBegin));
            splitDate.setBeginTime(DateUtils.getTimeFromDate(dateTimeBegin));
            splitDate.setEndTime(DateUtils.getTimeFromDate(dateTimeEnd));
            dates.add(splitDate);

        }else {

            Date beginDate = DateUtils.parseDate(DateUtils.YYYY_MM_DD, dateTimeBegin);
            Date endDate = DateUtils.parseDate(DateUtils.YYYY_MM_DD, dateTimeEnd);

            //4.将格式化后的第一天添加进集合
            SplitDate splitDate = new SplitDate();
            splitDate.setDate(beginDate);
            splitDate.setBeginTime(DateUtils.getTimeFromDate(dateTimeBegin));
            splitDate.setEndTime(Time.valueOf("23:59:59"));
            dates.add(splitDate);

            while (beginDate.before(endDate)) {
                beginDate = DateUtils.addDays(beginDate, 1);

                //10.得到的每一天就添加进集合
                SplitDate key = new SplitDate();
                key.setDate(beginDate);
                key.setBeginTime(Time.valueOf("00:00:00"));

                if (key.getDate().equals(endDate)) {
                    key.setEndTime(DateUtils.getTimeFromDate(dateTimeEnd));
                } else {
                    key.setEndTime(Time.valueOf("23:59:59"));
                }
                dates.add(key);
            }
        }


        return dates;
    }

}

