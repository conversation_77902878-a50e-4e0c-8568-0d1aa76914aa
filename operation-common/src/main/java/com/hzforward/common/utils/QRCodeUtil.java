package com.hzforward.common.utils;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.util.Hashtable;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

public class QRCodeUtil {
    private static final String CHARSET = "utf-8";
    // 二维码尺寸
    private static final int QRCODE_WIDTH = 200;
    private static final int QRCODE_HEIGHT = 250;

    public static BufferedImage createQRCode(String content){
        return createImage(content);
    }

    public static BufferedImage createQRCode(String content, String pressText){
        return pressText(pressText, createImage(content));
    }

    private static BufferedImage createImage(String content){
        try{
            Hashtable hints = new Hashtable();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);//二维码的纠错程度
            hints.put(EncodeHintType.CHARACTER_SET, CHARSET);//指定适用的字符编码
            hints.put(EncodeHintType.MARGIN, 3);//二维码的白边，如果要展示二维码内容，建议留着
            //生成二维码
            BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, QRCODE_WIDTH, QRCODE_HEIGHT, hints);

            int width = bitMatrix.getWidth();
            int height = bitMatrix.getHeight();
            //生成二维码的辅助类
            BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            //按照指定大小将二维码生成在图像image中
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
                }
            }

            return image;
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static BufferedImage pressText(String pressText, BufferedImage image){
        try{
            //如果有中文，建议使用gbk；否则容易出现在ide中运行好用，将程序打包后无法正常写入中文。
            pressText = new String(pressText.getBytes("gbk"),"gbk");

            Graphics g = image.createGraphics();
            g.drawImage(image, 0, 0, image.getWidth(), image.getHeight(), null);
            //设置画笔的颜色
            g.setColor(Color.BLACK);
            //设置字体
            Font font = new Font("宋体", Font.BOLD, 25);
            g.setFont(font);

            int startX = 60;
            int startY = 240;
            g.drawString(pressText, startX, startY);
            g.dispose();

            return image;
        }catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}