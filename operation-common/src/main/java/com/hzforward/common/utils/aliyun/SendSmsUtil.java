package com.hzforward.common.utils.aliyun;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsRequest;
import com.aliyun.dysmsapi20170525.models.QuerySendDetailsResponse;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.hzforward.common.constant.AliyunConstant;

public class SendSmsUtil {

    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId accessKeyId
     * @param accessKeySecret accessKeySecret
     * @return Client
     * @throws Exception e
     */
    public static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config().setAccessKeyId(accessKeyId).setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

    public static Client createClient() throws Exception {
        String accessKeyId = AliyunConstant.ALIBABA_CLOUD_ACCESS_KEY_ID;
        String accessKeySecret = AliyunConstant.ALIBABA_CLOUD_ACCESS_KEY_SECRET;
        return createClient(accessKeyId, accessKeySecret);
    }

    public static SendSmsResponse sendSms (String signName, String templateCode, String phoneNumber, String templateParam) {
        try{
            Client client = createClient();
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setSignName(signName)
                    .setTemplateCode(templateCode)
                    .setPhoneNumbers(phoneNumber)
                    .setTemplateParam(templateParam);
            RuntimeOptions runtime = new RuntimeOptions();
            return client.sendSmsWithOptions(sendSmsRequest, runtime);
        }catch (Exception e) {
            throw new RuntimeException("获取阿里云短信服务失败");
        }

    }

    public static QuerySendDetailsResponse querySendSmsDetail(String phoneNumber, String sendDate, String bizId) {
        return querySendSmsDetail(phoneNumber, sendDate, 10L, 1L, bizId);
    }

    public static QuerySendDetailsResponse querySendSmsDetail(String phoneNumber, String sendDate, Long pageSize, Long currentPage, String bizId) {
        try{
            Client client = createClient();
            QuerySendDetailsRequest querySendDetailsRequest = new QuerySendDetailsRequest()
                    .setPhoneNumber(phoneNumber)
                    .setSendDate(sendDate)
                    .setPageSize(pageSize)
                    .setCurrentPage(currentPage)
                    .setBizId(bizId);
            RuntimeOptions runtime = new RuntimeOptions();

            return client.querySendDetailsWithOptions(querySendDetailsRequest, runtime);
        }catch (Exception e) {
            throw new RuntimeException("查询短信发送情况失败");
        }

    }

}
