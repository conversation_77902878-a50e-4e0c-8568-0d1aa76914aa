package com.hzforward.common.utils.dingtalk;

import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiUserGetbyunionidRequest;
import com.dingtalk.api.request.OapiV2UserGetuserinfoRequest;
import com.dingtalk.api.response.OapiUserGetbyunionidResponse;
import com.dingtalk.api.response.OapiV2UserGetuserinfoResponse;
import com.hzforward.common.constant.DingTalkConstant;
import com.hzforward.common.constant.DingTalkURLConstant;
import com.hzforward.common.enums.ErrorEnum;
import com.hzforward.common.exception.ServiceException;
import com.taobao.api.ApiException;

public class DingtalkUserLoginUtil {

	// 根据钉钉免登码获取用户信息
	public static  String getDingtalkUserInfoByAuthCode(String authCode) {
		try{
			DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.URL_GET_USER_INFO_BY_AUTH_CODE);
			OapiV2UserGetuserinfoRequest request = new OapiV2UserGetuserinfoRequest();
			request.setCode(authCode);
			String accessToken = AccessTokenUtil.getAccessToken();
			OapiV2UserGetuserinfoResponse response = client.execute(request, accessToken);
			return response.getResult().getUserid();
		}catch (ApiException e){
			throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
		}

	}

	// 根据扫码免登码获取用户个人token
	public static String getAccessToken(String authCode){
		try{
			com.aliyun.dingtalkoauth2_1_0.Client client = new com.aliyun.dingtalkoauth2_1_0.Client(AccessTokenUtil.getConfig());
			GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
					//应用基础信息-应用信息的AppKey,请务必替换为开发的应用AppKey
					.setClientId(DingTalkConstant.APP_KEY)
					//应用基础信息-应用信息的AppSecret，,请务必替换为开发的应用AppSecret
					.setClientSecret(DingTalkConstant.APP_SECRET)
					.setCode(authCode)
					.setGrantType("authorization_code");
			GetUserTokenResponse getUserTokenResponse = client.getUserToken(getUserTokenRequest);
			//获取用户个人token
			return getUserTokenResponse.getBody().getAccessToken();
		}catch (Exception e){
			throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
		}
	}

	/**
	 * 获取用户unionId
	 * @param accessToken 个人token
	 */
	public static String getUserInfo(String accessToken) {
		try{
			com.aliyun.dingtalkcontact_1_0.Client client = new com.aliyun.dingtalkcontact_1_0.Client(AccessTokenUtil.getConfig());
			GetUserHeaders getUserHeaders = new GetUserHeaders();
			getUserHeaders.xAcsDingtalkAccessToken = accessToken;
			//获取用户个人信息，如需获取当前授权人的信息，unionId参数必须传me
			return client.getUserWithOptions("me", getUserHeaders, new RuntimeOptions()).getBody().getUnionId();
		}catch (Exception e){
			throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
		}
	}

	/**
	 * 根据用户唯一标识获取用户信息
	 * @param unionId 用户唯一标识
	 */
	public static OapiUserGetbyunionidResponse.UserGetByUnionIdResponse getDingtalkUserInfoByUnionId(String unionId) {
		try{
			DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.URL_GET_USER_INFO_BY_UNION_ID);
			OapiUserGetbyunionidRequest req = new OapiUserGetbyunionidRequest();
			req.setUnionid(unionId);
			String accessToken= AccessTokenUtil.getAccessToken();
			return client.execute(req, accessToken).getResult();
		}catch (Exception e){
			throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
		}
	}

}
