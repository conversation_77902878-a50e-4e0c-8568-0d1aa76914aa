package com.hzforward.common.utils.dingtalk;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiAttendanceListRequest;
import com.dingtalk.api.response.OapiAttendanceListResponse;
import com.hzforward.common.constant.DingTalkURLConstant;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.*;

// 获取钉钉考勤相关数据
public class AttendanceInfoUtil {

    /**
     * 分页获取考勤数据
     */
    public static OapiAttendanceListResponse getAttendanceInfo(OapiAttendanceListRequest request) {

        try {
            String token = AccessTokenUtil.getAccessToken();
            DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.URL_GET_ATTENDANCE_INFO);
            OapiAttendanceListRequest req = new OapiAttendanceListRequest();
            req.setWorkDateFrom(request.getWorkDateFrom());
            req.setWorkDateTo(request.getWorkDateTo());
            req.setUserIdList(request.getUserIdList());
            req.setOffset(request.getOffset());
            req.setLimit(request.getLimit());
            return client.execute(req, token);
        } catch (Exception _err) {
            throw new ServiceException("获取考勤数据错误");
        }
    }

    /**
     * 获取所有考勤数据
     */
    public static void getAllAttendanceInfo(List<OapiAttendanceListResponse.Recordresult> recordresultList, OapiAttendanceListRequest request) {
        if(StringUtils.isNull(request.getOffset())) {
            request.setOffset(0L);
        }
        if(StringUtils.isNull(request.getLimit())) {
            request.setLimit(20L);
        }

        OapiAttendanceListResponse response = getAttendanceInfo(request);
        recordresultList.addAll(response.getRecordresult());
        if(response.getHasMore()) {
            request.setOffset(request.getLimit() + request.getOffset());
            getAllAttendanceInfo(recordresultList, request);
        }
    }

    /**
     * 获取所有员工特定天打卡数据
     */
    public static List<OapiAttendanceListResponse.Recordresult> getUserTodayAttendanceInfo(List<String> userNameList, Date startDate, Date endDate)  {
        List<OapiAttendanceListResponse.Recordresult> recordresultList = new ArrayList<>();

        if(StringUtils.isEmpty(userNameList)) {
            return recordresultList;
        }

        OapiAttendanceListRequest request = new OapiAttendanceListRequest();
        request.setWorkDateFrom(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", startDate));
        request.setWorkDateTo(DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", endDate));
        request.setUserIdList(userNameList);
        getAllAttendanceInfo(recordresultList, request);

        return recordresultList;
    }

    /**
     * 检查员工到岗情况
     */
    public static List<String> checkUserOnDuty(List<String> userNameList){
        List<String> sysUserList = new ArrayList<>();

        if(StringUtils.isEmpty(userNameList)) {
            return sysUserList;
        }

        // 获取员工两天内的打卡信息(因为有夜班的情况，夜班可能会跨天),以最新的一条打卡记录为准,判断员工是否处于上班状态
        OapiAttendanceListRequest request = new OapiAttendanceListRequest();
        request.setUserIdList(userNameList);
        // 获取昨天的日期,这边不需要管时间,只要日期能对上就行,钉钉要求传入的参数格式需要带上时间,但回传的数据是按日期去查询的
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        request.setWorkDateFrom(DateFormatUtils.format(calendar.getTime() ,"yyyy-MM-dd HH:mm:ss"));
        request.setWorkDateTo(DateFormatUtils.format(DateUtils.getNowDate(), "yyyy-MM-dd HH:mm:ss"));
        List<OapiAttendanceListResponse.Recordresult> recordresultList = new ArrayList<>();
        // 获取到所有用户的考勤数据
        AttendanceInfoUtil.getAllAttendanceInfo(recordresultList, request);

        Map<String, OapiAttendanceListResponse.Recordresult> userOnDutyMap = new HashMap<>();

        for(OapiAttendanceListResponse.Recordresult recordresult : recordresultList) {
            if(userOnDutyMap.containsKey(recordresult.getUserId())) {
                if(recordresult.getUserCheckTime().after(userOnDutyMap.get(recordresult.getUserId()).getUserCheckTime())) {
                    userOnDutyMap.put(recordresult.getUserId(), recordresult);
                }
            }else {
                userOnDutyMap.put(recordresult.getUserId(), recordresult);
            }
        }



        userOnDutyMap.forEach((key, value) -> {
            if(value.getCheckType().equals("OnDuty")) {
                sysUserList.add(value.getUserId());
            }
        });

        return sysUserList;
    }
}
