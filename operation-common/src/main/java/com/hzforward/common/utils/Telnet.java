package com.hzforward.common.utils;

import com.hzforward.common.exception.ServiceException;
import org.apache.commons.net.telnet.TelnetClient;

import java.io.InputStream;
import java.io.PrintStream;

public class Telnet {
    private TelnetClient telnet = new TelnetClient();
    private InputStream in;
    private PrintStream out;

    public Telnet(String ip) {
        try {
            telnet.connect(ip);
            in = telnet.getInputStream();
            out = new PrintStream(telnet.getOutputStream());
        } catch (Exception e) {
            throw new ServiceException("连接"+ip+"失败");
        }
    }

    public void login(String user, String password) {
        readUntil("login:");
        write(user);
        readUntil("Password:");
        write(password);
    }

    /** * 读取分析结果 * * @param pattern * @return */
    public String readUntil(String pattern) {
        StringBuilder sb = new StringBuilder();
        try {
            char ch = (char) in.read();
            while (true) {
                sb.append(ch);
                if (sb.toString().toLowerCase().endsWith(pattern.toLowerCase())) {
                    return sb.toString();
                }
                ch = (char) in.read();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }

    /** * 写操作 * * @param value */
    public void write(String value) {
        try {
            out.println(value);
            out.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /** * 关闭连接 */
    public void disconnect() {
        try {
            telnet.disconnect();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
