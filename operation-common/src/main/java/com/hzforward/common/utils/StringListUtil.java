package com.hzforward.common.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class StringListUtil {
    public static List<String> StringToList(String str) {
        if (str == null || str.isEmpty()){
            return new ArrayList<>();
        }
        return new ArrayList<>(Arrays.asList(str.split(",")));
    }

    public static String ListToString(List<String> list) {
        if (list == null || list.isEmpty()){
            return null;
        }
        return String.join(",", list);
    }
}
