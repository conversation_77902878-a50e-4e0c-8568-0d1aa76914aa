package com.hzforward.common.utils.dingtalk;

import com.aliyun.dingtalkworkflow_1_0.Client;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest.StartProcessInstanceRequestFormComponentValues;
import com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceRequest.StartProcessInstanceRequestTargetSelectActioners;
import com.aliyun.teautil.models.RuntimeOptions;
import com.hzforward.common.core.domain.dingtalk.DingtalkProcessInstancesInfo;
import com.hzforward.common.core.domain.dingtalk.FormComponentValues;
import com.hzforward.common.exception.ServiceException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class DingtalkInstanceUtil {

    /**
     *
     * @param dingtalkProcessInstancesInfo 表单信息
     * @return 流程编号
     */
    public static String createDingtalkInstance(DingtalkProcessInstancesInfo dingtalkProcessInstancesInfo) {
        try {
            Client client = new Client(AccessTokenUtil.getConfig());

            StartProcessInstanceHeaders startProcessInstanceHeaders = new StartProcessInstanceHeaders();
            startProcessInstanceHeaders.xAcsDingtalkAccessToken = AccessTokenUtil.getAccessToken();

            List<StartProcessInstanceRequestFormComponentValues> StartProcessInstanceRequestFormComponentValuesList = new ArrayList<>();
            for(FormComponentValues formComponentValues : dingtalkProcessInstancesInfo.getFormComponentValuesList()) {
                StartProcessInstanceRequestFormComponentValuesList.add(new StartProcessInstanceRequestFormComponentValues()
                        .setName(formComponentValues.getName())
                        .setValue(formComponentValues.getValue())
                );
            }

            StartProcessInstanceRequestTargetSelectActioners targetSelectActioners = new StartProcessInstanceRequestTargetSelectActioners()
                    .setActionerKey(dingtalkProcessInstancesInfo.getTargetSelectActionKey())
                    .setActionerUserIds(dingtalkProcessInstancesInfo.getTargetSelectActionUserIds());


            StartProcessInstanceRequest startProcessInstanceRequest = new StartProcessInstanceRequest()
                    .setDeptId(dingtalkProcessInstancesInfo.getDeptId())
                    .setOriginatorUserId(dingtalkProcessInstancesInfo.getOriginatorUserId())
                    .setProcessCode(dingtalkProcessInstancesInfo.getProcessCode())
                    .setTargetSelectActioners(Collections.singletonList(targetSelectActioners))
                    .setFormComponentValues(StartProcessInstanceRequestFormComponentValuesList);

            StartProcessInstanceResponse response = client.startProcessInstanceWithOptions(startProcessInstanceRequest, startProcessInstanceHeaders, new RuntimeOptions());
            return response.getBody().getInstanceId();

        }catch (Exception _err) {
            throw new ServiceException("钉钉创建OA流程失败");
        }
    }


    /**
     *
     * @param processInstanceId 流程编号
     * @return 流程编号
     */
    public static GetProcessInstanceResponse getDingtalkInstanceDetail(String processInstanceId) {

        try {
            Client client = new Client(AccessTokenUtil.getConfig());
            GetProcessInstanceHeaders getProcessInstanceHeaders = new GetProcessInstanceHeaders();
            getProcessInstanceHeaders.xAcsDingtalkAccessToken = AccessTokenUtil.getAccessToken();
            GetProcessInstanceRequest getProcessInstanceRequest = new GetProcessInstanceRequest()
                    .setProcessInstanceId(processInstanceId);
            return client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new RuntimeOptions());
        }catch (Exception _err) {
//            System.out.println("processInstanceId = " + _err);
            throw new ServiceException("钉钉流程信息失败");
        }

    }

}
