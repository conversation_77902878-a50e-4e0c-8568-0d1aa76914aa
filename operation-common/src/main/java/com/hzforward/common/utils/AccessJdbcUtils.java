package com.hzforward.common.utils;

import java.sql.*;
import java.util.Properties;

public class AccessJdbcUtils {

    private static final String DRIVER_CLASS_NAME = "net.ucanaccess.jdbc.UcanaccessDriver";

    public static Connection getConnection(String path, String username, String password) throws ClassNotFoundException, SQLException {
        Properties properties = new Properties();
        properties.setProperty("user", username);
        properties.setProperty("password", password);
        properties.setProperty("loginTimeout", "1");
        return getConnection(path, properties);
    }

    public static Connection getConnection(String path, Properties properties) throws ClassNotFoundException, SQLException {
        Class.forName(DRIVER_CLASS_NAME);
        return DriverManager.getConnection(getUrl(path), properties);
    }

    public static ResultSet query(Connection conn, String sql) throws SQLException {
        Statement statement = conn.createStatement();
        return statement.executeQuery(sql);
    }

    public static void closeConn(Connection conn) throws SQLException {
        if (conn != null) {
            conn.close();
        }
    }

    public static void closeRsAndStatement(ResultSet rs) throws SQLException {
        if (rs != null) {
            Statement statement = rs.getStatement();
            rs.close();
            statement.close();
        }
    }

    private static String getUrl(String path) {
        return "jdbc:ucanaccess://" + path;
    }
}
