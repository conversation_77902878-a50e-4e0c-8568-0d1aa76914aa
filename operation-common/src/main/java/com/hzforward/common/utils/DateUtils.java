package com.hzforward.common.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.sql.Time;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String MM_DD = "MM-dd";

    public static String MM = "MM";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String HH_MM = "HH:mm";

    public static String MM_SS = "mm:ss";

    private static final String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM", "yyyy-MM-dd E"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    public static Date getTodayDate() {
        return parseDate(getDate());
    }


    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    // 获取当天 23:59:59
    public static Date getTodayStartTime(Date date) {
        if (StringUtils.isNull(date)) {
            return null;
        }
        return parseDate(parseDateToStr(YYYY_MM_DD + " 00:00:00", date));
    }

    // 获取当天 23:59:59
    public static Date getTodayLastTime(Date date) {
        if (StringUtils.isNull(date)) {
            return null;
        }
        return parseDate(parseDateToStr(YYYY_MM_DD + " 23:59:59", date));
    }

    // 获取服务器启动时间
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    // 日期路径 即年/月/日 如2018/08/08
    public static String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }


    /**
     *  转换时间方法
     */

    // 日期转日期型字符串
    public static String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    // 日期型字符串转化为日期 格式
    public static Date parseDate(String format, Date date) {
        return parseDate(parseDateToStr(format, date));
    }

    // 日期型字符串转化为日期 格式
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    public static Time getTimeFromDate(Date date) {
        if (date == null) {
            return null;
        }

        String timeString = parseDateToStr("HH:mm:ss", date);
        return Time.valueOf(timeString);
    }

    public static Date appendDateAndTime(Date date, Time time) {
        if (date == null || time == null) {
            return null;
        }

        return parseDate(parseDateToStr("yyyy-MM-dd", date) + " " + time.toString());
    }

    /**
     *  时间计算方法
     */

    // 计算相差天数
    public static int differentDaysByMillisecond(Date date1, Date date2) {
        return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    // 计算相差小时数
    public static int differentHoursByMillisecond(Date date1, Date date2) {
        return (int) ((date2.getTime() - date1.getTime()) / (1000 * 3600));
    }

    // 计算相差分钟数
    public static int differentMinuteByMillisecond(Date date1, Date date2) {
        return (int) ((date2.getTime() - date1.getTime()) / (1000 * 60));
    }

    // 计算相差秒数
    public static int differentSecondByMillisecond(Date date1, Date date2) {
        return (int) ((date2.getTime() - date1.getTime()) / 1000);
    }

    // 计算两个时间差
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    // 将秒转化为时分秒
    public static String convertSecondsToTimeFormat(int seconds) {
        int hours = seconds / 3600; // 计算小时数
        int remainder = seconds % 3600; // 计算剩余秒数
        int minutes = remainder / 60; // 计算分钟数
        seconds = remainder % 60; // 计算剩余秒数

        // 使用String.format进行格式化，确保单个数字前补零
        return String.format("%02d时%02d分%02d秒", hours, minutes, seconds);
    }

    // 将秒转化为保留2位小数的分钟
    public static String convertSecondsToMinute(int seconds) {
        if (seconds == 0) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat("#.00");
        return String.valueOf(df.format((double) seconds/60));
    }

    // 将秒转化为保留2位小数的小时
    public static String convertSecondsToHours(int seconds) {
        if (seconds == 0) {
            return "0";
        }
        DecimalFormat df = new DecimalFormat("#.00");
        return String.valueOf(df.format((double) seconds/60/60));
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        return toDate(localDateTime);
    }

    public static LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    /**
     * 将日期范围转换为范围内每天
     */
    public static List<Date> getDateRangeList(List<Date> dateRange) {
        List<Date> result = new ArrayList<>();
        if(dateRange.get(0).equals(dateRange.get(1))) {
            result.add(dateRange.get(0));
        } else {
            // 设置开始日期和结束日期
            LocalDate beginDate = toLocalDate(dateRange.get(0));
            LocalDate endDate = toLocalDate(dateRange.get(1));

            // 使用循环逐日生成日期并添加到列表
            while (!beginDate.isAfter(endDate)) {
                result.add(toDate(beginDate));
                beginDate = beginDate.plusDays(1);
            }
        }

        return result;
    }

    public static boolean isSameDay(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);

        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR)
                && cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR);

    }

    /**
     * 思路：将有交集的情况列出,若不符合有交集的情况,则无交集
     * 有交集的两种情况
     * 1.第二个时间段的开始时间在第一个时间段的开始时间和结束时间当中
     * 2.第二个时间段的结束时间在第一个时间段的开始时间和结束时间当中
     * 判断两个时间段是否有交集
     *
     * @param leftStartDate  第一个时间段的开始时间
     * @param leftEndDate    第一个时间段的结束时间
     * @param rightStartDate 第二个时间段的开始时间
     * @param rightEndDate   第二个时间段的结束时间
     * @return 若有交集, 返回true, 否则返回false
     */
    public static boolean hasOverlap(Date leftStartDate, Date leftEndDate, Date rightStartDate, Date rightEndDate) {
        return Math.max(leftStartDate.getTime(), rightStartDate.getTime()) < Math.min(leftEndDate.getTime(), rightEndDate.getTime());
    }
}
