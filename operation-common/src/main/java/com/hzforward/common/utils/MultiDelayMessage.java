package com.hzforward.common.utils;


import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class MultiDelayMessage<T> implements Serializable {

	public MultiDelayMessage() {

	}

	/**
	 * 消息体
	 */
	private T data;
	/**
	 * 记录延迟时间的集合
	 */
	private List<Long> delayMillis;



	public MultiDelayMessage(T data, List<Long> delayMillis) {
		this.data = data;
		this.delayMillis = delayMillis;
	}

	public static <T> MultiDelayMessage<T> of(T data, Integer ... delaySeconds){
		// 秒转毫秒
		List<Long> delayMillis = new ArrayList<>();
		CollUtils.newArrayList(delaySeconds).forEach(item -> delayMillis.add((long) (item * 1000)));

		return new MultiDelayMessage<>(data, delayMillis);
	}

	public static <T> MultiDelayMessage<T> of(T data, Long ... delayMillis){
		return new MultiDelayMessage<>(data, CollUtils.newArrayList(delayMillis));
	}

	/**
	 * 获取并移除下一个延迟时间
	 * @return 队列中的第一个延迟时间
	 */
	public Long removeNextDelay(){
		return delayMillis.remove(0);
	}

	/**
	 * 是否还有下一个延迟时间
	 */
	public boolean hasNextDelay(){
		return !delayMillis.isEmpty();
	}
}