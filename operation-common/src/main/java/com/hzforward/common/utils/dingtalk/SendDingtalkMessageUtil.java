package com.hzforward.common.utils.dingtalk;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.hzforward.common.constant.DingTalkConstant;
import com.hzforward.common.constant.DingTalkURLConstant;
import com.hzforward.common.core.domain.dingtalk.DingtalkMessageSendInfo;
import com.hzforward.common.enums.ErrorEnum;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.StringUtils;
import com.taobao.api.ApiException;

import java.net.URLEncoder;

public class SendDingtalkMessageUtil {

    //发送钉钉推送消息
    public static void sendActionCardMessage(DingtalkMessageSendInfo dingtalkMessageSendInfo){

        if(StringUtils.isEmpty(dingtalkMessageSendInfo.getUserIdList())){
            return;
        }

        DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.URL_SEND_MESSAGE);
        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setUseridList(dingtalkMessageSendInfo.getUserIdList());
        request.setAgentId(DingTalkConstant.AGENT_ID);
        request.setToAllUser(false);

        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setMsgtype("action_card");

        msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
        msg.getActionCard().setTitle(dingtalkMessageSendInfo.getTitle());
        msg.getActionCard().setMarkdown(dingtalkMessageSendInfo.getMarkdown());
        msg.getActionCard().setSingleTitle(StringUtils.isEmpty(dingtalkMessageSendInfo.getSingleTitle()) ? "查看详情" : dingtalkMessageSendInfo.getSingleTitle());

        StringBuilder singleUrl = new StringBuilder();
        singleUrl.append("dingtalk://dingtalkclient/action/openapp");
        singleUrl.append("?corpid=").append(DingTalkConstant.CORP_ID);
        singleUrl.append("&container_type=work_platform");
        singleUrl.append("&app_id=0_").append(DingTalkConstant.AGENT_ID);
        singleUrl.append("&redirect_type=jump");
        if(StringUtils.isNotEmpty(dingtalkMessageSendInfo.getSingleUrl())){
            try {
                singleUrl.append("&redirect_url=");
                singleUrl.append(URLEncoder.encode("https://se.hzforward.com/applet/#" + dingtalkMessageSendInfo.getSingleUrl() + "?corpId=$CORPID$", "UTF-8"));
            }catch (Exception e){
                throw new ServiceException("钉钉消息通知跳转地址错误");
            }
        }
        msg.getActionCard().setSingleUrl(singleUrl.toString());

        request.setMsg(msg);

        try {
            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, AccessTokenUtil.getAccessToken());
            JSONObject responseObj = JSON.parseObject(response.getBody());
            if (!"0".equals(responseObj.getString("errcode"))) {
                throw new ServiceException("钉钉通知发送失败!");
            }
        } catch (ApiException e) {
            throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
        }
    }
}
