package com.hzforward.common.utils;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

/**
 * Created by <PERSON><PERSON> on 17/3/2.
 */
public class TimeUtil {

    public static Date getOldDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, 1970);
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date parse(String s) throws Exception {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return sf.parse(s);
    }

    public static String format(Date date) {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sf.format(date);
    }

    public static Date parse(String s, String pattern) throws Exception {
        SimpleDateFormat sf = new SimpleDateFormat(pattern);
        return sf.parse(s);
    }

    public static String format(Date date, String pattern){
        SimpleDateFormat sf = new SimpleDateFormat(pattern);
        return sf.format(date);
    }

    /**
     *
     * @param start 当前时间
     * @param end 结束时间
     * @return 相差时间
     */

    public static int gapHour(Date start, Date end) {
        long result = (end.getTime() - start.getTime());
        return (int) (result / 3600000);
    }

    /**
     * 获得当天时间 00:00:00
     *
     * @return 当天时间
     */
    public static Calendar getCurrentDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    /**
     * 计算两个日期之间相差天数(只计算日期部分，不考虑时间)
     *
     * @param date 时间
     * @param anotherDate 时间
     * @return 相差天数
     */
    public static Integer getDaysBetweenDate(Date date, Date anotherDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Calendar anotherCalendar = Calendar.getInstance();
        anotherCalendar.setTime(anotherDate);
        anotherCalendar.set(Calendar.HOUR_OF_DAY, 0);
        anotherCalendar.set(Calendar.MINUTE, 0);
        anotherCalendar.set(Calendar.SECOND, 0);
        anotherCalendar.set(Calendar.MILLISECOND, 0);
        long time = calendar.getTime().getTime() - anotherCalendar.getTime().getTime();
        time = Math.abs(time);
        String days = MathUtil.divide(String.valueOf(time), String.valueOf(1000 * 60 * 60 * 24), 0);
        return Integer.parseInt(days);
    }

    public static Integer getAgeBetweenDate(Date birthDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(birthDay);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int age = Calendar.getInstance().get(Calendar.YEAR) - year - 1;
        if (month < Calendar.getInstance().get(Calendar.MONTH) || (month == Calendar.getInstance().get(Calendar.MONTH) && day <= Calendar.getInstance().get(Calendar.DAY_OF_MONTH))) {
            //若出生月份小于当前月份 或 （出生月份等于当前月份，且出生日期小于等于当前日期）
            return age + 1;
        }
        return age;
    }

    /**
     * 根据年 月 获取对应的月份 天数
     * */
    public static int getDaysByYearMonth(int year, int month) {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, year);
        a.set(Calendar.MONTH, month - 1);
        a.set(Calendar.DATE, 1);
        a.roll(Calendar.DATE, -1);
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }

    /**
     * date 转 localDateTime
     * @param date
     * @return localDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * localDateTime 转 date
     * @param localDateTime
     * @return date
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return Date.from(instant);
    }

}
