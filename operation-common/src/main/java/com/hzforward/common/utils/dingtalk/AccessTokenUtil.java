package com.hzforward.common.utils.dingtalk;

import cn.hutool.extra.spring.SpringUtil;
import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenResponse;
import com.aliyun.teaopenapi.models.Config;
import com.hzforward.common.constant.CacheConstants;
import com.hzforward.common.constant.DingTalkConstant;
import com.hzforward.common.core.redis.RedisCache;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.spring.SpringUtils;

import java.util.concurrent.TimeUnit;


/**
 * 获取access_token工具类
 */
public class AccessTokenUtil {

    /**
     * 生成config
     * @return Config
     */
    public static Config getConfig(){
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return config;
    }

    public static String getAccessToken() {
        // 优先获取缓存中的token
        Object accessTokenObject = SpringUtils.getBean(RedisCache.class).getCacheObject(CacheConstants.ACCESS_TOKEN);
        if(StringUtils.isNotNull(accessTokenObject)) {
            return accessTokenObject.toString();
        }

        return getAndSetAccessToken();
    }

    public static String getAndSetAccessToken() {
        try {
            Client client = new Client(getConfig());
            GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest()
                    .setAppKey(DingTalkConstant.APP_KEY)
                    .setAppSecret(DingTalkConstant.APP_SECRET);
            GetAccessTokenResponse getAccessTokenResponse = client.getAccessToken(getAccessTokenRequest);
            String accessToken = getAccessTokenResponse.getBody().getAccessToken();
            int timeOut = (int)getAccessTokenResponse.getBody().getExpireIn().longValue();
            SpringUtils.getBean(RedisCache.class).setCacheObject(CacheConstants.ACCESS_TOKEN, accessToken, timeOut, TimeUnit.SECONDS);
            return accessToken;
        } catch (Exception _err) {
            throw new ServiceException("获取accessToken错误");
        }
    }

    public static String getTokenByCompany(String companyCode, String appKey, String appSecret) {
        try {
            Object accessTokenObject = SpringUtil.getBean(RedisCache.class).getCacheObject(CacheConstants.ACCESS_TOKEN + companyCode);
            if (accessTokenObject != null) {
                return accessTokenObject.toString();
            }
            return getAndSetAccessToken(companyCode, appKey, appSecret);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    public static String getAndSetAccessToken(String companyCode, String appKey, String appSecret) {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";

//        String appKey = getAppKey(companyCode);
//        String appSecret = getAppSecret(companyCode);

        try {
            Client client = new Client(config);
            GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest()
                    .setAppKey(appKey)
                    .setAppSecret(appSecret);
            GetAccessTokenResponse getAccessTokenResponse = client.getAccessToken(getAccessTokenRequest);
            String accessToken = getAccessTokenResponse.getBody().getAccessToken();
            int timeOut = (int) getAccessTokenResponse.getBody().getExpireIn().longValue();
            SpringUtil.getBean(RedisCache.class).setCacheObject(CacheConstants.ACCESS_TOKEN + companyCode, accessToken, timeOut, TimeUnit.SECONDS);
            return accessToken;
        } catch (Exception _err) {
            throw new ServiceException();
        }

    }
}
