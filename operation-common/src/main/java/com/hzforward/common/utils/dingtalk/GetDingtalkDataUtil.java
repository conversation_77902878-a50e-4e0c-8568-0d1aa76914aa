package com.hzforward.common.utils.dingtalk;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiDepartmentListRequest;
import com.dingtalk.api.request.OapiV2DepartmentGetRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserListRequest;
import com.dingtalk.api.response.OapiDepartmentListResponse;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.hzforward.common.constant.DingTalkURLConstant;
import com.hzforward.common.enums.ErrorEnum;
import com.hzforward.common.exception.ServiceException;
import com.taobao.api.ApiException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetDingtalkDataUtil {

    /**
     * 获取钉钉部门列表
     * @return 部门列表
     */
    public static List<OapiDepartmentListResponse.Department> getDingTalkDepartment(){
        try{
            String accessToken = AccessTokenUtil.getAccessToken();
            DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.URL_GET_DINGTALK_DEPARTMENT);
            OapiDepartmentListRequest request = new OapiDepartmentListRequest();
            request.setHttpMethod("GET");
            return client.execute(request, accessToken).getDepartment();
        }catch (ApiException e){
            throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
        }
    }

    /**
     * 根据部门id获取部门详情
     *
     * @param dingtalkDeptId 钉钉部门id
     * @return 部门列表
     */
    public static OapiV2DepartmentGetResponse.DeptGetResponse getDingTalkDepartmentByDeptId(Long dingtalkDeptId){
        try{
            String accessToken = AccessTokenUtil.getAccessToken();
            DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.URL_GET_DINGTALK_DEPARTMENT_BY_DEPT_ID);
            OapiV2DepartmentGetRequest request = new OapiV2DepartmentGetRequest();
            request.setDeptId(dingtalkDeptId);
            return client.execute(request, accessToken).getResult();
        }catch (ApiException e){
            throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
        }
    }

    /**
     * 获取钉钉用户列表
     * @return 用户列表
     */
    public static Map<Long, List<OapiV2UserListResponse.ListUserResponse>> getDingTalkUserList(){
        Map<Long, List<OapiV2UserListResponse.ListUserResponse>> usersJsonList = new HashMap<>();

        String accessToken = AccessTokenUtil.getAccessToken();
        List<OapiDepartmentListResponse.Department> departmentList = getDingTalkDepartment();

        for(OapiDepartmentListResponse.Department department : departmentList){
            Long deptId = department.getId();
            List<OapiV2UserListResponse.ListUserResponse> userList = new ArrayList<>();
            getDingTalkUserListRequest(deptId,0L, accessToken, userList);
            usersJsonList.put(deptId, userList);
        }
        return usersJsonList;
    }

    /**
     * 根据部门获取钉钉用户列表
     * @param deptId 部门id
     * @param offset 开始取数据的行数
     * @param accessToken accessToken
     * @param userList 用户列表
     */
    private static void getDingTalkUserListRequest(Long deptId, Long offset, String accessToken, List<OapiV2UserListResponse.ListUserResponse> userList){
        try{
            DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.URL_GET_DINGTALK_USER_LIST);
            OapiV2UserListRequest request = new OapiV2UserListRequest();
            request.setDeptId(deptId);
            request.setCursor(offset);
            request.setSize(99L);
            OapiV2UserListResponse response = client.execute(request, accessToken);
            userList.addAll(response.getResult().getList());

            if(response.getResult().getHasMore()){
                getDingTalkUserListRequest(deptId, response.getResult().getNextCursor(), accessToken, userList);
            }
        }catch (ApiException e){
            throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
        }
    }

    /**
     * 根据用户id获取钉钉用户详情
     *
     * @param dingtalkUserId 钉钉用户id
     */
    public static OapiV2UserGetResponse.UserGetResponse getDingTalkUserByUserId(String dingtalkUserId) {
        try {
            String accessToken = AccessTokenUtil.getAccessToken();
            DingTalkClient client = new DefaultDingTalkClient(DingTalkURLConstant.URL_GET_USER_INFO_BY_USER_ID);
            OapiV2UserGetRequest request = new OapiV2UserGetRequest();
            request.setUserid(dingtalkUserId);
            request.setLanguage("zh_CN");
            return client.execute(request, accessToken).getResult();
        } catch (ApiException e) {
            throw new ServiceException(ErrorEnum.DINGTALK_INTERFACE_ERROR.getName());
        }
    }
}
