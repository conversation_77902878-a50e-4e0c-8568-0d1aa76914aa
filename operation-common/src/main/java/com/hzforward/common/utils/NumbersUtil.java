package com.hzforward.common.utils;

import java.util.ArrayList;
import java.util.List;

public class NumbersUtil {

    public static String getNumbers(int position,int convertNumber) {
        StringBuilder result = new StringBuilder(String.valueOf(convertNumber));

        for (int j = 1; j < position - String.valueOf(convertNumber).length(); j ++) {
            result.insert(0, '0');
        }
        return result.toString();
    }
}
