package com.hzforward.common.utils;

import com.hzforward.common.enums.ErrorEnum;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.exception.user.UserPasswordNotMatchException;

import javax.naming.Context;

import javax.naming.ldap.InitialLdapContext;
import javax.naming.ldap.LdapContext;
import java.util.Hashtable;

public class LdapUtil {

    public static LdapContext connectForWardLdap(String username, String password){
        username = username + "@elevator.xizi.local";
        // LDAP访问地址
        String ldapUrl = "ldap://elevator.xizi.local:389";
        Hashtable<String, String> environment = initEnvironment(ldapUrl,username,password);
        return connectLdapContext(environment);
    }


    public static LdapContext connectLdapContext(Hashtable<String, String> environment){
        try {
            return new InitialLdapContext(environment, null);
        }catch (javax.naming.AuthenticationException e) {
            throw new UserPasswordNotMatchException();
        } catch (Exception e) {
            throw new ServiceException(ErrorEnum.CONNECT_LDAP_ERROR.getName());
        }
    }

    private static Hashtable<String, String> initEnvironment(String ldapUrl, String username, String password){
        Hashtable<String, String> environment = new Hashtable<String, String>();

        environment.put(Context.PROVIDER_URL, ldapUrl);
        environment.put(Context.SECURITY_PRINCIPAL, username);
        environment.put(Context.SECURITY_CREDENTIALS, password);

        environment.put(Context.INITIAL_CONTEXT_FACTORY,"com.sun.jndi.ldap.LdapCtxFactory");
        // LDAP访问安全级别(none,simple,strong)
        environment.put(Context.SECURITY_AUTHENTICATION, "simple");
        //连接超时设置为3秒
        environment.put("com.sun.jndi.ldap.connect.timeout", "3000");
        return environment;
    }
}
