package com.hzforward.common.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class LockUtils {
    private static final Map<String, Lock> lockMap = new HashMap<>();


    public static Lock getLock(String lockName) {
        return lockMap.computeIfAbsent(lockName, k -> new ReentrantLock());
    }
}
