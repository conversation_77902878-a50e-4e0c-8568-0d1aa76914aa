package com.hzforward.common.constant;

public class DingTalkURLConstant {

    /**
     * 获取钉钉部门列表接口url
     */
    public static final String URL_GET_DINGTALK_DEPARTMENT = "https://oapi.dingtalk.com/department/list";

    /**
     * 根据部门id获取部门详情
     */
    public static final String URL_GET_DINGTALK_DEPARTMENT_BY_DEPT_ID = "https://oapi.dingtalk.com/topapi/v2/department/get";

    /**
     * 获取钉钉用户列表接口url
     */
    public static final String URL_GET_DINGTALK_USER_LIST = "https://oapi.dingtalk.com/topapi/v2/user/list";

    /**
     * 通过免登授权码获取用户信息 url
     */
    public static final String URL_GET_USER_INFO_BY_AUTH_CODE = "https://oapi.dingtalk.com/topapi/v2/user/getuserinfo";

    /**
     * 通过用户id获取用户详情
     */
    public static final String URL_GET_USER_INFO_BY_USER_ID = "https://oapi.dingtalk.com/topapi/v2/user/get";

    /**
     * 获取用户考勤打卡情况
     */
    public static final String URL_GET_ATTENDANCE_INFO = "https://oapi.dingtalk.com/attendance/list";

    /**
     * 获取用户信息
     */
    public static final String URL_GET_USER_INFO_BY_UNION_ID = "https://oapi.dingtalk.com/topapi/user/getbyunionid";

    /**
     * 推送钉钉消息
     */
    public static final String URL_SEND_MESSAGE = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2";

    /**
     * 获取单个审批实例详情
     */
    public static final String PROCESS_INSTANCE_GET = "https://oapi.dingtalk.com/topapi/processinstance/get";
}
