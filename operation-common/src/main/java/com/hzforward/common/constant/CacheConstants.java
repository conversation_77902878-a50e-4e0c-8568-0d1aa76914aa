package com.hzforward.common.constant;

/**
 * 缓存的key 常量
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 钉钉accessToken redis key
     */
    public static final String ACCESS_TOKEN = "dingtalk_access_token:";

    /**
     * 销售统计大屏 redis key
     */
    public static final String SALE_STAT_SCREEN = "sale_stat_screen:";

    /**
     * 取餐二维码
     */
    public static final String PICK_QR_CODE = "pick_qr_code:";

    /**
     * 微信accessToken redis key
     */
    public static final String WX_ACCESS_TOKEN = "wx_access_token:";

    /**
     * WMS accessToken redis key
     */
    public static final String WMS_ACCESS_TOKEN = "wms_access_token:";

    /**
     * 微信accessToken redis key
     */
    public static final String WX_TICKET = "wx_jsapi_ticket:";
}
