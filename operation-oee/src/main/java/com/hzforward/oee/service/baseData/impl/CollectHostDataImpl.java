package com.hzforward.oee.service.baseData.impl;

import com.hzforward.common.utils.StringUtils;
import com.hzforward.oee.constant.baseData.host.CleanLineConstant;
import com.hzforward.oee.domain.baseData.host.*;
import com.hzforward.oee.mapper.baseData.host.*;
import com.hzforward.oee.service.baseData.ICollectHostData;
import com.hzforward.oee.util.Modbus4jUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采集主机MES基础数据
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Service
@RequiredArgsConstructor
public class CollectHostDataImpl implements ICollectHostData {

    private final CleanBaseLineMapper cleanBaseLineMapper;
    private final CleanFlexibleLineMapper cleanFlexibleLineMapper;
    private final CleanLargeLineMapper cleanLargeLineMapper;
    private final CleanRollerLineMapper cleanRollerLineMapper;
    private final CleanRotorLineMapper cleanRotorLineMapper;

    /**
     * 采集清洗线数据
     */
    @Async
    @Override
    public void collectCleanData() {

        List<CleanBaseLine> cleanBaseLineList = Modbus4jUtils.readModbusData(CleanLineConstant.BASE_LINE, CleanBaseLine.class);
        if(StringUtils.isNotEmpty(cleanBaseLineList)) cleanBaseLineMapper.insertBatchSomeColumn(cleanBaseLineList);

        List<CleanFlexibleLine> cleanFlexibleLineList = Modbus4jUtils.readModbusData(CleanLineConstant.FLEXIBLE_LINE, CleanFlexibleLine.class);
        if(StringUtils.isNotEmpty(cleanFlexibleLineList)) cleanFlexibleLineMapper.insertBatchSomeColumn(cleanFlexibleLineList);

        List<CleanLargeLine> cleanLargeLineList = Modbus4jUtils.readModbusData(CleanLineConstant.LARGE_LINE, CleanLargeLine.class);
        if(StringUtils.isNotEmpty(cleanLargeLineList)) cleanLargeLineMapper.insertBatchSomeColumn(cleanLargeLineList);

        List<CleanRollerLine> cleanRollerLineList = Modbus4jUtils.readModbusData(CleanLineConstant.ROLLER_LINE, CleanRollerLine.class);
        if(StringUtils.isNotEmpty(cleanRollerLineList)) cleanRollerLineMapper.insertBatchSomeColumn(cleanRollerLineList);

        List<CleanRotorLine> cleanRotorLineList = Modbus4jUtils.readModbusData(CleanLineConstant.ROTOR_LINE, CleanRotorLine.class);
        if(StringUtils.isNotEmpty(cleanRotorLineList)) cleanRotorLineMapper.insertBatchSomeColumn(cleanRotorLineList);
    }



}
