package com.hzforward.oee.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.SecurityUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.equipmentManage.domain.Section;
import com.hzforward.equipmentManage.service.ISectionService;
import com.hzforward.oee.domain.ShutdownDefaultReason;
import com.hzforward.oee.mapper.ShutdownDefaultReasonMapper;
import com.hzforward.oee.service.IShutdownDefaultReasonService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * oee默认原因Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Service
@RequiredArgsConstructor
public class ShutdownDefaultReasonServiceImpl extends ServiceImpl<ShutdownDefaultReasonMapper, ShutdownDefaultReason> implements IShutdownDefaultReasonService
{

    private final ShutdownDefaultReasonMapper shutdownDefaultReasonMapper;
    private final ISectionService sectionService;

    /**
     * 查询oee默认原因列表
     *
     * @param shutdownDefaultReason oee默认原因
     * @return oee默认原因
     */
    @Override
    public List<ShutdownDefaultReason> selectShutdownDefaultReasonList(ShutdownDefaultReason shutdownDefaultReason)
    {
        List<ShutdownDefaultReason> shutdownDefaultReasonList = shutdownDefaultReasonMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<ShutdownDefaultReason>()
                .eq(ShutdownDefaultReason::getFactoryArea, shutdownDefaultReason.getFactoryArea())
                .eq(ShutdownDefaultReason::getDepartment, shutdownDefaultReason.getDepartment())
                .eq(ShutdownDefaultReason::getSectionId, shutdownDefaultReason.getSectionId())
        );

        shutdownDefaultReasonList.forEach(item -> item.setSectionName(sectionService.getById(item.getSectionId()).getSectionName()));

        return shutdownDefaultReasonList;
    }


    /**
     * 新增设备停机默认原因列表
     *
     * @param shutdownDefaultReason oee默认原因
     * @return oee默认原因
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveShutdownDefaultReason(ShutdownDefaultReason shutdownDefaultReason) {
        shutdownDefaultReason.setReasonBy(SecurityUtils.getLoginUser().getUsername());

        List<ShutdownDefaultReason> shutdownDefaultReasonList = new ArrayList<>();
        // 获取所有工段
        List<Section> sectionList = sectionService.list(new ExcludeEmptyLambdaQueryWrapper<Section>()
                .eq(Section::getFactoryArea, shutdownDefaultReason.getFactoryArea())
                .eq(Section::getDepartment, shutdownDefaultReason.getDepartment())
                .eq(Section::getSectionId, shutdownDefaultReason.getSectionId())
        );

        for (Section section : sectionList) {
            ShutdownDefaultReason addDefaultReason = new ShutdownDefaultReason();
            BeanUtils.copyProperties(shutdownDefaultReason, addDefaultReason);
            addDefaultReason.setDepartment(section.getDepartment());
            addDefaultReason.setSectionId(section.getSectionId());
            shutdownDefaultReasonList.add(addDefaultReason);
        }

        return saveBatch(shutdownDefaultReasonList);
    }

}
