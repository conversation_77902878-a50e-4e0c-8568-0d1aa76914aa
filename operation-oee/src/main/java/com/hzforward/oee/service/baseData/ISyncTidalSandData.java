package com.hzforward.oee.service.baseData;

import com.hzforward.oee.domain.baseData.casting.TidalSand;

import java.util.Date;
import java.util.List;

/**
 * 同步潮模砂数据
 *
 * <AUTHOR>
 * @date 2025-03-7
 */
public interface ISyncTidalSandData {

    /**
     * 查询潮膜砂生产数据列表
     *
     * @param date 日期
     * @return 潮膜砂生产数据集合
     */
    List<TidalSand> selectTidalSandList(Date date);

    /**
     * 同步潮模砂造型线汇总数据
     */
    void syncTidalSandData(List<Date> dateRange);

    /**
     * 同步潮模砂造型线汇总数据
     */
    void syncYesTodayTidalSandData();

    void saveTidalSandDetailData();
}
