package com.hzforward.oee.service.impl;

import com.hzforward.common.utils.reflect.ReflectUtils;
import com.hzforward.oee.domain.baseData.MesBaseEntity;

public class GetEquipmentRunningStateImpl {
    public static String checkBeamLine(MesBaseEntity baseData, MesBaseEntity nextBaseData) {
        // 获取当前线速
        Double currentLineSpeed = ReflectUtils.getFieldValue(nextBaseData, "currentLineSpeed");
        if (currentLineSpeed != null && currentLineSpeed > 0) {
            return "2";
        } else {
            return "1";
        }
    }

    public static String checkHighSpeedExtruder(MesBaseEntity baseData, MesBaseEntity nextBaseData) {
        // 获取当前线速
        Short hmiNowSpeed = ReflectUtils.getFieldValue(nextBaseData, "hmiNowSpeed");
        if (hmiNowSpeed != null && hmiNowSpeed > 0) {
            return "2";
        } else {
            return "1";
        }
    }

    public static String checkHighSpeedTwisting(MesBaseEntity baseData, MesBaseEntity nextBaseData) {
        // 获取当前线速
        Float currentLineSpeed = ReflectUtils.getFieldValue(nextBaseData, "currentLineSpeed");
        if (currentLineSpeed != null && currentLineSpeed > 0) {
            return "2";
        } else {
            return "1";
        }
    }

    public static String checkLayingTwisting(MesBaseEntity baseData, MesBaseEntity nextBaseData) {
        // 获取当前线速
        Integer currentLineSpeed = ReflectUtils.getFieldValue(nextBaseData, "currentLineSpeed");
        if (currentLineSpeed != null && currentLineSpeed > 0) {
            return "2";
        } else {
            return "1";
        }
    }

    public static String checkSheathExtruder(MesBaseEntity baseData, MesBaseEntity nextBaseData) {
        // 获取当前线速
        Float hostSpeedReality = ReflectUtils.getFieldValue(nextBaseData, "hostSpeedReality");
        if (hostSpeedReality != null && hostSpeedReality > 0) {
            return "2";
        } else {
            return "1";
        }
    }
}
