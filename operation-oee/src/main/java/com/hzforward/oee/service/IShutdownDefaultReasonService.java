package com.hzforward.oee.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.ShutdownDefaultReason;

import java.util.List;

/**
 * oee默认原因Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-19
 */
public interface IShutdownDefaultReasonService extends IService<ShutdownDefaultReason>
{
    /**
     * 查询oee默认原因列表
     * 
     * @param shutdownDefaultReason oee默认原因
     * @return oee默认原因集合
     */
    List<ShutdownDefaultReason> selectShutdownDefaultReasonList(ShutdownDefaultReason shutdownDefaultReason);


    boolean saveShutdownDefaultReason(ShutdownDefaultReason shutdownDefaultReason);
}
