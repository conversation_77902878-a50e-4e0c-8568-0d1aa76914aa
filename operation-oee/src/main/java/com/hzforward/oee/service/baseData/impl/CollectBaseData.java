package com.hzforward.oee.service.baseData.impl;

import com.hzforward.oee.service.baseData.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 采集所有MES基础数据
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Service
@RequiredArgsConstructor
public class CollectBaseData implements ICollectBaseData {

    private final ISyncTidalSandData syncTidalSandData;

    private final ICollectHostData collectHostData;
    private final ICollectCableData collectCableData;
    private final ICollectMagneticData collectMagneticData;

    @Override
    public void collectAllBaseData() {

        // 铸件工厂数据采集
        // 潮模砂造型线
        syncTidalSandData.saveTidalSandDetailData();


        // 主机车间数据采集
        // 清洗线
        collectHostData.collectCleanData();

        // 磁材车间数据采集
        // 压机
        collectMagneticData.collectFormingPressData();


        // 电缆数据采集
        // 束丝机
        collectCableData.collectBeamLineData();
        // 高速挤出机
        collectCableData.collectHighSpeedExtruderData();
        // 高速对绞机
        collectCableData.collectHighSpeedTwistingData();
        // 悬臂单扭成缆机
        collectCableData.collectLayingTwistingData();
        // 扁电缆护套挤出机
        collectCableData.collectSheathExtruderData();
    }
}
