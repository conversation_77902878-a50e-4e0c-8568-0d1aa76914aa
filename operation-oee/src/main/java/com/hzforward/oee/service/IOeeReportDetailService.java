package com.hzforward.oee.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.OeeReport;
import com.hzforward.oee.domain.OeeReportDetail;

import java.util.List;

/**
 * OEE报告详情Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IOeeReportDetailService extends IService<OeeReportDetail>
{

    List<OeeReportDetail> getOeeReportDetailList(Long reportId);

    /**
     * 获取并设置设备OEE详情信息
     *
     * @param oeeReport oeeReport

     */
    void getAndSetOeeReportDetail(OeeReport oeeReport);

    /**
     * 设置OEE异常停机原因信息
     *
     * @param oeeReportDetail oeeReportDetail
     */
    void setOeeReportDetailReason(OeeReportDetail oeeReportDetail);
}
