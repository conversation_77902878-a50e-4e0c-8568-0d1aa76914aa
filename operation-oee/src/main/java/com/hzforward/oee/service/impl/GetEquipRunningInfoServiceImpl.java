package com.hzforward.oee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.equipmentManage.domain.LedgerInfo;
import com.hzforward.equipmentManage.service.ILedgerInfoService;
import com.hzforward.framework.datasource.DynamicDataSourceContextHolder;
import com.hzforward.oee.domain.EquipRunningInfo;
import com.hzforward.oee.domain.baseData.MesBaseEntity;
import com.hzforward.oee.domain.baseData.cable.*;
import com.hzforward.oee.domain.baseData.casting.TidalSandDetail;
import com.hzforward.oee.domain.baseData.host.PressureResistance;
import com.hzforward.oee.domain.baseData.machining.CncRealTimeInfoRow;
import com.hzforward.oee.mapper.EquipRunningInfoMapper;
import com.hzforward.oee.mapper.baseData.cable.*;
import com.hzforward.oee.mapper.baseData.casting.TidalSandDetailMapper;
import com.hzforward.oee.mapper.baseData.host.PressureResistanceMapper;
import com.hzforward.oee.mapper.baseData.machining.CncRealTimeInfoRowMapper;
import com.hzforward.oee.service.IGetEquipRunningStateService;
import com.hzforward.oee.service.IGetEquipRunningInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * oee报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Service
@RequiredArgsConstructor
public class GetEquipRunningInfoServiceImpl extends ServiceImpl<EquipRunningInfoMapper, EquipRunningInfo> implements IGetEquipRunningInfoService
{
    private final ILedgerInfoService ledgerInfoService;

    private final CncRealTimeInfoRowMapper cncRealTimeInfoRowMapper;
    private final PressureResistanceMapper pressureResistanceMapper;

    private final TidalSandDetailMapper tidalSandDetailMapper;

    // 电缆
    private final BeamLineMapper beamLineMapper;
    private final HighSpeedExtruderMapper highSpeedExtruderMapper;
    private final HighSpeedTwistingMapper highSpeedTwistingMapper;
    private final LayingTwistingMapper layingTwistingMapper;
    private final SheathExtruderMapper sheathExtruderMapper;

    /**
     * 根据设备运行详情获取设备汇总信息
     * @param equipNo 设备编号
     * @param infoDate 汇总日期
     */
    @Override
    public List<EquipRunningInfo> getRunningInfo(String equipNo, Date infoDate, Date beginQueryDateTime) {
        LedgerInfo ledgerInfo = ledgerInfoService.getInfoByEquipNo(equipNo);

        List<EquipRunningInfo> equipRunningInfoList;
        if (ledgerInfo.getNetworkingType() == null) {
            throw new ServiceException("设备编号" + equipNo +"暂时未统计OEE信息,请联系系统管理员确认");
        } else {
            switch (ledgerInfo.getNetworkingType()) {
                case "sync_pressurization_naiya":
                    equipRunningInfoList = getSummaryDataByTypeOne(equipNo, infoDate, beginQueryDateTime);
                    break;
                case "cnc_real_time_info_row":
                    equipRunningInfoList = getSummaryDataByTypeTwo(equipNo, infoDate, beginQueryDateTime);
                    break;
                case "beam_line":
                    equipRunningInfoList = getBeamLineSummaryData(equipNo, infoDate, beginQueryDateTime);
                    break;
                case "high_speed_extruder":
                    equipRunningInfoList = getHighSpeedExtruderSummaryData(equipNo, infoDate, beginQueryDateTime);
                    break;
                case "high_speed_extruder_twisting":
                    equipRunningInfoList = getHighSpeedTwistingSummaryData(equipNo, infoDate, beginQueryDateTime);
                    break;
                case "laying_twisting":
                    equipRunningInfoList = getLayingTwistingSummaryData(equipNo, infoDate, beginQueryDateTime);
                    break;
                case "sheath_extruder":
                    equipRunningInfoList = getSheathExtruderSummaryData(equipNo, infoDate, beginQueryDateTime);
                    break;
                case "tidal_sand_detail":
                    equipRunningInfoList = getSummaryDataByTypeFour(equipNo, infoDate, beginQueryDateTime);
                    break;
                default:
                    throw new ServiceException("设备编号" + equipNo +"暂时未统计OEE信息,请联系系统管理员确认");
            }
        }

        Date dateEndTime = DateUtils.isSameDay(infoDate, DateUtils.getNowDate()) ? DateUtils.getNowDate() : DateUtils.getTodayLastTime(infoDate);
        assert beginQueryDateTime != null;
        assert dateEndTime != null;

        if(equipRunningInfoList.isEmpty()) {
            EquipRunningInfo equipOffline = new EquipRunningInfo();
            equipOffline.setRunningState("0");
            equipOffline.setBeginTime(beginQueryDateTime);
            equipOffline.setEndTime(dateEndTime);
            equipRunningInfoList.add(equipOffline);
        } else {
            // 添加当天开始到首条数据
            if(DateUtils.differentMinuteByMillisecond(beginQueryDateTime, equipRunningInfoList.get(0).getBeginTime()) > 5) {
                EquipRunningInfo equipStop = new EquipRunningInfo();
                equipStop.setRunningState("0");
                equipStop.setBeginTime(beginQueryDateTime);
                equipStop.setEndTime(equipRunningInfoList.get(0).getBeginTime());
                equipRunningInfoList.add(0, equipStop);
            }else {
                equipRunningInfoList.get(0).setBeginTime(beginQueryDateTime);
            }

            // 添加当天最后条数据到结束
            if(DateUtils.differentMinuteByMillisecond(equipRunningInfoList.get(equipRunningInfoList.size()-1).getEndTime(), dateEndTime) > 5) {
                EquipRunningInfo equipStop = new EquipRunningInfo();
                equipStop.setRunningState("0");
                equipStop.setBeginTime(equipRunningInfoList.get(equipRunningInfoList.size()-1).getEndTime());
                equipStop.setEndTime(dateEndTime);
                equipRunningInfoList.add(equipStop);
            }else {
                equipRunningInfoList.get(equipRunningInfoList.size()-1).setEndTime(dateEndTime);
            }
        }

        // 删除开始时间和结束时间相同的数据
        for(int i = equipRunningInfoList.size() - 1; i >= 0; i--) {
            if(equipRunningInfoList.get(i).getBeginTime().equals(equipRunningInfoList.get(i).getEndTime())) {
                equipRunningInfoList.remove(i);
            }
        }

        // 相邻的运行数据,若运行状态相同则合并
        for(int i = equipRunningInfoList.size() - 1; i >= 0; i--) {
            if(i > 0 && equipRunningInfoList.get(i).getRunningState().equals(equipRunningInfoList.get(i-1).getRunningState())) {
                equipRunningInfoList.get(i-1).setEndTime(equipRunningInfoList.get(i).getEndTime());
                equipRunningInfoList.remove(i);
            }
        }

        return equipRunningInfoList;
    }

    /**
     * 获取 主机  标准线/柔性线/DT线/碟式马达线 汇总数据
     */
    private List<EquipRunningInfo> getSummaryDataByTypeOne(String equipNo, Date infoDate, Date beginQueryDateTime) {
        List<EquipRunningInfo> equipRunningInfoList = new ArrayList<>();

        // 标准线线体编号2000161 柔性线XINXIAN57 DT线XINXIAN48
        String lineName = "";
        int baseTime = 0;

        switch (equipNo) {
            case "2000161":
                lineName = "标准线";
                baseTime = 120;
                break;
            case "XINXIAN57":
                lineName = "柔性线";
                baseTime = 240;
                break;
            case "XINXIAN48":
                lineName = "DT线";
                baseTime = 240;
                break;
            case "XINXIAN05":
                lineName = "碟式马达线";
                baseTime = 540;
                break;
        }

        List<PressureResistance> pressureResistanceList = pressureResistanceMapper.selectList(new LambdaQueryWrapper<PressureResistance>()
                .eq(PressureResistance::getLine, lineName)
                .eq(PressureResistance::getCsrq, infoDate)
                .ge(PressureResistance::getCsrqsj, beginQueryDateTime)
                .orderByAsc(PressureResistance::getCsrqsj)
        );

        if(!pressureResistanceList.isEmpty())  {
            EquipRunningInfo equipRunningInfo = new EquipRunningInfo();
            equipRunningInfo.setRunningState("2");
            equipRunningInfo.setBeginTime(DateUtils.addSeconds(pressureResistanceList.get(0).getCsrqsj(), -baseTime));
            equipRunningInfo.setEndTime(pressureResistanceList.get(0).getCsrqsj());

            for(int i = 0; i < pressureResistanceList.size(); i++) {
                PressureResistance pressureResistance = pressureResistanceList.get(i);
                if(i > 0) {
                    if(DateUtils.differentSecondByMillisecond(equipRunningInfo.getEndTime(), pressureResistance.getCsrqsj()) >= baseTime) {
                        EquipRunningInfo addEquipRunningInfo = new EquipRunningInfo();
                        BeanUtils.copyProperties(equipRunningInfo, addEquipRunningInfo);
                        equipRunningInfoList.add(addEquipRunningInfo);

                        EquipRunningInfo equipStop = new EquipRunningInfo();
                        equipStop.setRunningState("1");
                        equipStop.setBeginTime(addEquipRunningInfo.getEndTime());
                        equipStop.setEndTime(DateUtils.addSeconds(pressureResistance.getCsrqsj(), -baseTime));
                        equipRunningInfoList.add(equipStop);

                        equipRunningInfo.setBeginTime(DateUtils.addSeconds(pressureResistance.getCsrqsj(), -baseTime));
                        equipRunningInfo.setEndTime(pressureResistance.getCsrqsj());
                    } else {
                        equipRunningInfo.setEndTime(pressureResistance.getCsrqsj());
                    }
                }

                if(i == pressureResistanceList.size() - 1) {
                    equipRunningInfoList.add(equipRunningInfo);
                }
            }
        }

        return equipRunningInfoList;
    }

    /**
     * 获取 cnc设备 汇总数据
     */
    public List<EquipRunningInfo> getSummaryDataByTypeTwo(String equipNo, Date infoDate, Date beginQueryDateTime) {
        List<EquipRunningInfo> equipRunningInfoList = new ArrayList<>();

        try {
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE7.name());

            // 首先根据设备编号,查询日期获取当天该设备的所有数据
            List<CncRealTimeInfoRow> cncRealTimeInfoRowList = cncRealTimeInfoRowMapper.selectList(new LambdaQueryWrapper<CncRealTimeInfoRow>()
                    .eq(CncRealTimeInfoRow::getEquipNo, equipNo)
                    .eq(CncRealTimeInfoRow::getDate, infoDate)
                    .ge(CncRealTimeInfoRow::getCreateTime, beginQueryDateTime)
                    .orderByAsc(CncRealTimeInfoRow::getCreateTime)
            );

            if(!cncRealTimeInfoRowList.isEmpty()) {
                EquipRunningInfo equipRunningInfo = new EquipRunningInfo();
                equipRunningInfo.setRunningState(cncRealTimeInfoRowList.get(0).getCncRunStatus());
                equipRunningInfo.setBeginTime(cncRealTimeInfoRowList.get(0).getCreateTime());
                equipRunningInfo.setEndTime(cncRealTimeInfoRowList.get(0).getCreateTime());

                for(int i = 0; i < cncRealTimeInfoRowList.size(); i++) {
                    CncRealTimeInfoRow cncRealTimeInfoRow = cncRealTimeInfoRowList.get(i);
                    if(i > 0) {
                        // 先判断下条数据和上条数据之间间隔时间,若大于5分钟则判断设备离线
                        if(DateUtils.differentMinuteByMillisecond(equipRunningInfo.getEndTime(), cncRealTimeInfoRow.getCreateTime()) >= 5) {
                            EquipRunningInfo addEquipRunningInfo = new EquipRunningInfo();
                            BeanUtils.copyProperties(equipRunningInfo, addEquipRunningInfo);
                            equipRunningInfoList.add(addEquipRunningInfo);

                            EquipRunningInfo equipOffline = new EquipRunningInfo();
                            equipOffline.setRunningState("0");
                            equipOffline.setBeginTime(equipRunningInfo.getEndTime());
                            equipOffline.setEndTime(cncRealTimeInfoRow.getCreateTime());
                            equipRunningInfoList.add(equipOffline);

                            equipRunningInfo.setRunningState(cncRealTimeInfoRow.getCncRunStatus());
                            equipRunningInfo.setBeginTime(cncRealTimeInfoRow.getCreateTime());
                            equipRunningInfo.setEndTime(cncRealTimeInfoRow.getCreateTime());
                        } else if(!equipRunningInfo.getRunningState().equals(cncRealTimeInfoRow.getCncRunStatus())) {


                            EquipRunningInfo addEquipRunningInfo = new EquipRunningInfo();
                            BeanUtils.copyProperties(equipRunningInfo, addEquipRunningInfo);
                            equipRunningInfoList.add(addEquipRunningInfo);

                            equipRunningInfo.setRunningState(cncRealTimeInfoRow.getCncRunStatus());
                            equipRunningInfo.setBeginTime(addEquipRunningInfo.getEndTime());
                            equipRunningInfo.setEndTime(cncRealTimeInfoRow.getCreateTime());
                        } else {
                            equipRunningInfo.setEndTime(cncRealTimeInfoRow.getCreateTime());
                        }
                    }

                    if(i == cncRealTimeInfoRowList.size() - 1) {
                        equipRunningInfoList.add(equipRunningInfo);
                    }
                }
            }
        }finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }

        return equipRunningInfoList;
    }


    /**
     * 获取 电缆 束线机 汇总数据
     */
    private List<EquipRunningInfo> getBeamLineSummaryData(String equipNo, Date infoDate, Date beginQueryDateTime) {
        // 获取束线机详情信息
        List<BeamLine> baseDataList = beamLineMapper.selectList(new LambdaQueryWrapper<BeamLine>()
                .eq(BeamLine::getEquipNo, equipNo)
                .eq(BeamLine::getDate, infoDate)
                .ge(BeamLine::getCreateTime, beginQueryDateTime)
                .orderByAsc(BeamLine::getCreateTime)
        );

        return getSummaryData(baseDataList, GetEquipmentRunningStateImpl::checkBeamLine);
    }

    /**
     * 获取 电缆 压出机 汇总数据
     */
    private List<EquipRunningInfo> getHighSpeedExtruderSummaryData(String equipNo, Date infoDate, Date beginQueryDateTime) {
        // 获取压出机详情信息
        List<HighSpeedExtruder> baseDataList = highSpeedExtruderMapper.selectList(new LambdaQueryWrapper<HighSpeedExtruder>()
                .eq(HighSpeedExtruder::getEquipNo, equipNo)
                .eq(HighSpeedExtruder::getDate, infoDate)
                .ge(HighSpeedExtruder::getCreateTime, beginQueryDateTime)
                .orderByAsc(HighSpeedExtruder::getCreateTime)
        );

        return getSummaryData(baseDataList, GetEquipmentRunningStateImpl::checkHighSpeedExtruder);
    }

    /**
     * 获取 电缆 压出机 汇总数据
     */
    private List<EquipRunningInfo> getHighSpeedTwistingSummaryData(String equipNo, Date infoDate, Date beginQueryDateTime) {
        // 获取压出机详情信息
        List<HighSpeedTwisting> baseDataList = highSpeedTwistingMapper.selectList(new LambdaQueryWrapper<HighSpeedTwisting>()
                .eq(HighSpeedTwisting::getEquipNo, equipNo)
                .eq(HighSpeedTwisting::getDate, infoDate)
                .ge(HighSpeedTwisting::getCreateTime, beginQueryDateTime)
                .orderByAsc(HighSpeedTwisting::getCreateTime)
        );

        return getSummaryData(baseDataList, GetEquipmentRunningStateImpl::checkHighSpeedTwisting);
    }

    /**
     * 获取 电缆 压出机 汇总数据
     */
    private List<EquipRunningInfo> getLayingTwistingSummaryData(String equipNo, Date infoDate, Date beginQueryDateTime) {
        // 获取压出机详情信息
        List<LayingTwisting> baseDataList = layingTwistingMapper.selectList(new LambdaQueryWrapper<LayingTwisting>()
                .eq(LayingTwisting::getEquipNo, equipNo)
                .eq(LayingTwisting::getDate, infoDate)
                .ge(LayingTwisting::getCreateTime, beginQueryDateTime)
                .orderByAsc(LayingTwisting::getCreateTime)
        );

        return getSummaryData(baseDataList, GetEquipmentRunningStateImpl::checkLayingTwisting);
    }

    /**
     * 获取 电缆 压出机 汇总数据
     */
    private List<EquipRunningInfo> getSheathExtruderSummaryData(String equipNo, Date infoDate, Date beginQueryDateTime) {
        // 获取压出机详情信息
        List<SheathExtruder> baseDataList = sheathExtruderMapper.selectList(new LambdaQueryWrapper<SheathExtruder>()
                .eq(SheathExtruder::getEquipNo, equipNo)
                .eq(SheathExtruder::getDate, infoDate)
                .ge(SheathExtruder::getCreateTime, beginQueryDateTime)
                .orderByAsc(SheathExtruder::getCreateTime)
        );

        return getSummaryData(baseDataList, GetEquipmentRunningStateImpl::checkSheathExtruder);
    }

    /**
     * 获取 精密 潮膜砂造型线 汇总数据
     */
    private List<EquipRunningInfo> getSummaryDataByTypeFour(String equipNo, Date infoDate, Date beginQueryDateTime) {
        List<EquipRunningInfo> equipRunningInfoList = new ArrayList<>();

        // 获取潮膜砂造型线详情信息
        List<TidalSandDetail> tidalSandDetailList = tidalSandDetailMapper.selectList(new LambdaQueryWrapper<TidalSandDetail>()
                .eq(TidalSandDetail::getEquipNo, equipNo)
                .eq(TidalSandDetail::getDate, infoDate)
                .ge(TidalSandDetail::getCreateTime, beginQueryDateTime)
                .orderByAsc(TidalSandDetail::getCreateTime)
        );

        if(!tidalSandDetailList.isEmpty()) {
            EquipRunningInfo equipRunningInfo = new EquipRunningInfo();
            equipRunningInfo.setRunningState("2");
            equipRunningInfo.setBeginTime(tidalSandDetailList.get(0).getCreateTime());
            equipRunningInfo.setEndTime(tidalSandDetailList.get(0).getCreateTime());

            for(int i = 0; i < tidalSandDetailList.size(); i++) {
                TidalSandDetail tidalSandDetail = tidalSandDetailList.get(i);
                if(i > 0) {


                    // 先判断下条数据和上条数据之间间隔时间,若大于5分钟则判断设备离线
                    if(DateUtils.differentMinuteByMillisecond(equipRunningInfo.getEndTime(), tidalSandDetail.getCreateTime()) >= 5) {
                        EquipRunningInfo addEquipRunningInfo = new EquipRunningInfo();
                        BeanUtils.copyProperties(equipRunningInfo, addEquipRunningInfo);
                        equipRunningInfoList.add(addEquipRunningInfo);

                        EquipRunningInfo equipOffline = new EquipRunningInfo();
                        equipOffline.setRunningState("0");
                        equipOffline.setBeginTime(equipRunningInfo.getEndTime());
                        equipOffline.setEndTime(tidalSandDetail.getCreateTime());
                        equipRunningInfoList.add(equipOffline);

                        equipRunningInfo.setRunningState("2");
                        equipRunningInfo.setBeginTime(tidalSandDetail.getCreateTime());
                        equipRunningInfo.setEndTime(tidalSandDetail.getCreateTime());
                    } else {
                        // 获取2条数据之间,造型数量是否发生变化
                        int totalChange = (tidalSandDetail.getTotalModelingNum() + tidalSandDetail.getTotalUnqualifiedModelingNum()) - (tidalSandDetailList.get(i-1).getTotalModelingNum() + tidalSandDetailList.get(i-1).getTotalUnqualifiedModelingNum());

                        if(totalChange > 0) {
                            if(equipRunningInfo.getRunningState().equals("2")) {
                                equipRunningInfo.setEndTime(tidalSandDetail.getCreateTime());
                            } else {
                                EquipRunningInfo addEquipRun = new EquipRunningInfo();
                                BeanUtils.copyProperties(equipRunningInfo, addEquipRun);
                                equipRunningInfoList.add(addEquipRun);

                                equipRunningInfo.setRunningState("2");
                                equipRunningInfo.setBeginTime(equipRunningInfo.getEndTime());
                                equipRunningInfo.setEndTime(tidalSandDetail.getCreateTime());
                            }
                        } else {
                            // 两条数据间未发生变化
                            if(equipRunningInfo.getRunningState().equals("1")) {
                                equipRunningInfo.setEndTime(tidalSandDetail.getCreateTime());
                            } else {
                                // 判断当前数据时间与上条数据之间是否超过节拍时间 48秒
                                int second = DateUtils.differentSecondByMillisecond(equipRunningInfo.getEndTime(), tidalSandDetail.getCreateTime());
                                if(second > 48) {
                                    EquipRunningInfo addEquipRun = new EquipRunningInfo();
                                    BeanUtils.copyProperties(equipRunningInfo, addEquipRun);
                                    equipRunningInfoList.add(addEquipRun);

                                    equipRunningInfo.setRunningState("1");
                                    equipRunningInfo.setBeginTime(equipRunningInfo.getEndTime());
                                    equipRunningInfo.setEndTime(tidalSandDetail.getCreateTime());
                                }
                            }

                        }

                    }
                }

                if(i == tidalSandDetailList.size() - 1) {
                    equipRunningInfoList.add(equipRunningInfo);
                }
            }
        }

        return equipRunningInfoList;
    }

    private List<EquipRunningInfo> getSummaryData(List<? extends MesBaseEntity> baseDataList, IGetEquipRunningStateService getEquipRunningState) {
        List<EquipRunningInfo> equipRunningInfoList = new ArrayList<>();

        if(!baseDataList.isEmpty()) {
            EquipRunningInfo equipRunningInfo = new EquipRunningInfo();

            for(int i = 0; i < baseDataList.size(); i++) {
                MesBaseEntity baseData = baseDataList.get(i);

                if (i < baseDataList.size() - 1) {
                    MesBaseEntity nextBaseData = baseDataList.get(i + 1);
                    // 先判断下条数据和上条数据之间间隔时间,若大于5分钟/300秒则判断设备离线
                    if(DateUtils.differentSecondByMillisecond(baseData.getCreateTime(), nextBaseData.getCreateTime()) >= 300) {
                        EquipRunningInfo equipOffline = new EquipRunningInfo();
                        BeanUtils.copyProperties(equipRunningInfo, equipOffline);
                        equipOffline.setRunningState("0");
                        equipOffline.setBeginTime(baseData.getCreateTime());
                        equipOffline.setEndTime(nextBaseData.getCreateTime());
                        equipRunningInfoList.add(equipOffline);
                    } else {
                        // 获取设备工作状态
                        String runningState = getEquipRunningState.getEquipRunningState(baseData, nextBaseData);

                        EquipRunningInfo addEquipRunningInfo = new EquipRunningInfo();
                        BeanUtils.copyProperties(equipRunningInfo, addEquipRunningInfo);
                        addEquipRunningInfo.setRunningState(runningState);
                        addEquipRunningInfo.setBeginTime(baseData.getCreateTime());
                        addEquipRunningInfo.setEndTime(nextBaseData.getCreateTime());
                        equipRunningInfoList.add(addEquipRunningInfo);
                    }
                }
            }
        }

        return equipRunningInfoList;
    }
}
