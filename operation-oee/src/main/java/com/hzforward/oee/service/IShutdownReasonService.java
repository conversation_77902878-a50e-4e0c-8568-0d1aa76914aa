package com.hzforward.oee.service;

import com.hzforward.oee.domain.OeeReport;
import com.hzforward.oee.domain.OeeReportDetail;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 异常停机原因描述Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IShutdownReasonService
{
    /**
     * 获取异常停机报告列表(PC)
     *
     * @param oeeReport oeeReport
     */
    List<OeeReport> getShutdownReasonList(OeeReport oeeReport);

    /**
     * 获取需要设置原因的时间列表 (PC段)
     *
     * @param oeeReport oeeReport
     */
    List<OeeReportDetail> getShutdownReasonDetail(OeeReport oeeReport);

    /**
     * 获取需要设置原因的时间列表 (移动端)
     *
     * @param oeeReport oeeReport
     */
    OeeReport getReasonSetList(OeeReport oeeReport);

    /**
     * 设置OEE异常停机原因信息
     *
     * @param oeeReportDetail oeeReportDetail
     */
    OeeReport setShutdownReason(OeeReportDetail oeeReportDetail);

    /**
     * 设置OEE异常停机原因信息(移动端)
     *
     * @param oeeReportDetail oeeReportDetail
     */
    void setShutdownReasonMobile(OeeReportDetail oeeReportDetail);

    /**
     * 导出异常停机原因列表
     *
     * @param oeeReport oeeReport
     */
    void exportShutdownReason(HttpServletResponse response, OeeReport oeeReport);
}
