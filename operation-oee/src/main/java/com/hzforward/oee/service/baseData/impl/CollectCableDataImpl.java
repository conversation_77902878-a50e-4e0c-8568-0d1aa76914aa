package com.hzforward.oee.service.baseData.impl;

import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.oee.constant.baseData.cable.*;
import com.hzforward.oee.domain.baseData.cable.*;
import com.hzforward.oee.mapper.baseData.cable.*;
import com.hzforward.oee.service.baseData.ICollectCableData;
import com.hzforward.oee.util.Modbus4jUtils;
import com.serotonin.modbus4j.BatchResults;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 采集电缆MES基础数据
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Service
@RequiredArgsConstructor
public class CollectCableDataImpl implements ICollectCableData {

    private final HighSpeedExtruderMapper highSpeedExtruderMapper;
    private final BeamLineMapper beamLineMapper;
    private final HighSpeedTwistingMapper highSpeedTwistingMapper;
    private final LayingTwistingMapper layingTwistingMapper;
    private final SheathExtruderMapper sheathExtruderMapper;

    /**
     * 采集束丝机数据
     */
    @Async
    @Override
    public void collectBeamLineData() {
        Map<String, BatchResults<Integer>> batchResultsMap = Modbus4jUtils.batchRead(BeanLineConstant.BEAN_LINE_CONFIG);

        List<BeamLine> beamLineList = new ArrayList<>();

        for (String equipNo : batchResultsMap.keySet()) {
            BeamLine beamLine = new BeamLine();

            List<Integer> dataList = new ArrayList<>();
            for(int i = 0; i < 10; i++) {
                dataList.add(((Short) batchResultsMap.get(equipNo).getValue(i)).intValue());
            }

            beamLine.setDate(DateUtils.getTodayDate());
            beamLine.setEquipNo(equipNo);

            beamLine.setRotationalSpeed(dataList.get(0));
            beamLine.setCurrentLineSpeed((double) (dataList.get(2) * 65536 + dataList.get(1)) / 100);
            beamLine.setSetMetersNumber((double) (dataList.get(4) * 65536 + dataList.get(3)) / 100);
            beamLine.setCurrentMetersNumber((double) (dataList.get(6) * 65536 + dataList.get(5)) / 100);
            beamLine.setPitch((double) (dataList.get(8) * 65536 + dataList.get(7)) );
            beamLine.setCurrentTension((double) dataList.get(9) / 100);

            beamLineList.add(beamLine);
        }

        if(StringUtils.isNotEmpty(beamLineList)) beamLineMapper.insertBatchSomeColumn(beamLineList);
    }

    /**
     * 采集高速挤出机数据
     */
    @Async
    @Override
    public void collectHighSpeedExtruderData() {
        List<HighSpeedExtruder> highSpeedExtruderList = Modbus4jUtils.readModbusData(HighSpeedExtruderConstant.HIGH_SPEED_EXTRUDER_CONFIG, HighSpeedExtruder.class);
        if(StringUtils.isNotEmpty(highSpeedExtruderList)) highSpeedExtruderMapper.insertBatchSomeColumn(highSpeedExtruderList);
    }

    /**
     * 采集高速对绞机数据
     */
    @Async
    @Override
    public void collectHighSpeedTwistingData() {
        List<HighSpeedTwisting> highSpeedTwistingList = Modbus4jUtils.readModbusData(HighSpeedTwistingConstant.HIGH_SPEED_TWISTING_CONFIG, HighSpeedTwisting.class);
        if(StringUtils.isNotEmpty(highSpeedTwistingList)) highSpeedTwistingMapper.insertBatchSomeColumn(highSpeedTwistingList);
    }

    /**
     * 采集悬臂悬臂单扭成榄机
     */
    @Async
    @Override
    public void collectLayingTwistingData() {
        List<LayingTwisting> layingTwistingList = Modbus4jUtils.readModbusData(LayingTwistingConstant.LAYING_TWISTING_CONFIG, LayingTwisting.class);
        if(StringUtils.isNotEmpty(layingTwistingList)) layingTwistingMapper.insertBatchSomeColumn(layingTwistingList);
    }

    /**
     * 采集悬臂悬臂单扭成榄机
     */
    @Async
    @Override
    public void collectSheathExtruderData() {
        List<SheathExtruder> sheathExtruderList = Modbus4jUtils.readModbusData(SheathExtruderConstant.SHEATH_EXTRUDER_CONFIG, SheathExtruder.class);
        if(StringUtils.isNotEmpty(sheathExtruderList)) sheathExtruderMapper.insertBatchSomeColumn(sheathExtruderList);
    }


}
