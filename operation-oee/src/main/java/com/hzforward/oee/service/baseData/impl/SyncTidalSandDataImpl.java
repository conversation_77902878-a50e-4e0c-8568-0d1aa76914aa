package com.hzforward.oee.service.baseData.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.AccessJdbcUtils;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.oee.domain.baseData.casting.TidalSand;
import com.hzforward.oee.domain.baseData.casting.TidalSandDetail;
import com.hzforward.oee.mapper.baseData.casting.TidalSandDetailMapper;
import com.hzforward.oee.mapper.baseData.casting.TidalSandMapper;
import com.hzforward.oee.service.baseData.ISyncTidalSandData;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


@Service
@RequiredArgsConstructor
public class SyncTidalSandDataImpl implements ISyncTidalSandData {

    private static final Logger logger = LoggerFactory.getLogger(SyncTidalSandDataImpl.class);

    private final TidalSandDetailMapper tidalSandDetailMapper;
    private final TidalSandMapper tidalSandMapper;

    private static final String accessDatabaseIp = "***********";
    private static final String userName = "";
    private static final String password = "";

    private static final String equipNo = "JQ0116";

    /**
     * 查询潮膜砂生产数据列表
     *
     * @param date 日期
     * @return 潮膜砂生产数据
     */
    @Override
    public List<TidalSand> selectTidalSandList(Date date){
        List<TidalSand> tidalSandList = tidalSandMapper.selectList(new LambdaQueryWrapper<TidalSand>()
                .eq(TidalSand::getDate, date)
        );

        if(StringUtils.isEmpty(tidalSandList)) {
            throw new ServiceException("当前日期无数据");
        }

        for(TidalSand tidalSand : tidalSandList) {
            tidalSand.setTotal(tidalSand.getModelingNum() + tidalSand.getUnqualifiedModelingNum());

            int second = DateUtils.differentSecondByMillisecond(tidalSand.getClassesStartTime(), tidalSand.getClassesEndTime());
            tidalSand.setTheoryNumber(second/60);
        }
        return tidalSandList;
    }

    /**
     * 同步潮模砂造型线汇总数据
     *
     * @param dateRange 日期范围
     */
    @Override
    public void syncTidalSandData(List<Date> dateRange) {
        List<Date> dateList = DateUtils.getDateRangeList(dateRange);
        for(Date date : dateList) {
            saveTidalSandData(date);
        }
    }

    /**
     * 同步潮模砂造型线汇总数据
     *
     */
    @Override
    public void syncYesTodayTidalSandData() {
        Date syncDate = DateUtils.addDays(DateUtils.getTodayDate(), -1);
        saveTidalSandData(syncDate);
    }

    /**
     * 同步潮模砂造型线汇总数据
     */
    private void saveTidalSandData(Date syncDate) {
        Connection connection = null;
        ResultSet result = null;

        try{
            String path = getAccessDatabasePath(syncDate);
            connection = AccessJdbcUtils.getConnection(path, userName, password);

            result = AccessJdbcUtils.query(connection, "select * from PLANING_1_20 where DATA = '" + DateUtils.parseDateToStr("yyyy/MM/dd", syncDate) + "'");


            while (result.next()) {

                List<TidalSand> tidalSandList = new ArrayList<>();
                for (int i = 1; i <= 20; i++) {
                    if(result.getString("DT_START_" + i).startsWith("00/00/0000")){
                        break;
                    }

                    TidalSand tidalSand = new TidalSand();

                    tidalSand.setDate(syncDate);
                    tidalSand.setEquipNo(equipNo);
                    tidalSand.setModel(result.getString("MODELLO_" + i));
                    tidalSand.setProgramFlag(result.getString("PROGR_" + i));
                    tidalSand.setModelingNum(result.getInt("FORM_" + i));
                    tidalSand.setUnqualifiedModelingNum(result.getInt("SCARTE_" + i));
                    tidalSand.setPouringNum(result.getInt("COLATE_" + i));
                    tidalSand.setClassesStartTime(DateUtils.parseDate(result.getString("DT_START_" + i), "dd/MM/yyyy HH:mm:ss"));
                    tidalSand.setClassesEndTime(DateUtils.parseDate(result.getString("DT_STOP_" + i), "dd/MM/yyyy HH:mm:ss"));

                    tidalSandList.add(tidalSand);
                }

                long count = tidalSandMapper.selectCount(new LambdaQueryWrapper<TidalSand>()
                        .eq(TidalSand::getDate, syncDate)
                );

                if(count > 0) {
                    tidalSandMapper.deleteById(syncDate);
                }

                tidalSandMapper.insertBatchSomeColumn(tidalSandList);
            }

        } catch (Exception e) {
            logger.error("同步潮模砂汇总数据失败");
        }finally {
            try{
                AccessJdbcUtils.closeRsAndStatement(result);
                AccessJdbcUtils.closeConn(connection);
            }catch (Exception ignored) {}
        }
    }


    /**
     * 同步潮模砂造型线详情数据
     */
    @Override
    @Async
    public void saveTidalSandDetailData() {
        Connection connection = null;
        ResultSet result = null;

        try{

            Date syncDate = DateUtils.getTodayDate();
            Date yesToday = DateUtils.addDays(DateUtils.getTodayDate(), -1);
            String path = getAccessDatabasePath(syncDate);


            connection = AccessJdbcUtils.getConnection(path, userName, password);
            String sql = "select * from PLANING_1_20 where DATA = '" + DateUtils.parseDateToStr("yyyy/MM/dd", syncDate) + "' or DATA = '" + DateUtils.parseDateToStr("yyyy/MM/dd", yesToday) + "'";

            result = AccessJdbcUtils.query(connection, sql);

            TidalSandDetail tidalSandDetail = new TidalSandDetail();
            tidalSandDetail.setDate(syncDate);
            tidalSandDetail.setEquipNo(equipNo);

            while (result.next()) {
                for (int i = 1; i <= 20; i++) {
                    if(result.getString("DT_START_" + i).startsWith("00/00/0000")){
                        break;
                    }

                    tidalSandDetail.setTotalModelingNum(tidalSandDetail.getTotalModelingNum() + result.getInt("FORM_" + i));
                    tidalSandDetail.setTotalUnqualifiedModelingNum(tidalSandDetail.getTotalUnqualifiedModelingNum() + result.getInt("SCARTE_" + i));
                    tidalSandDetail.setTotalPouringNum(tidalSandDetail.getTotalPouringNum() + result.getInt("COLATE_" + i));
                }
            }

            tidalSandDetailMapper.insert(tidalSandDetail);

        } catch (Exception ignored) {

        } finally {
            try{
                AccessJdbcUtils.closeRsAndStatement(result);
                AccessJdbcUtils.closeConn(connection);
            }catch (Exception ignored) {}
        }
    }

    private String getAccessDatabasePath(Date syncDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(syncDate);

        String year = String.valueOf(calendar.get(Calendar.YEAR));
        return  "//" + accessDatabaseIp + "/history/" + year + "/PLANING_" + year + ".MDB";
    }




}
