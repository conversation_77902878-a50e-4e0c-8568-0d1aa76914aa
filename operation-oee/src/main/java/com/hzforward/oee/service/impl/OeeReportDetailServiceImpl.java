package com.hzforward.oee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.SecurityUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.oee.constant.ReasonTypeConstant;
import com.hzforward.oee.domain.EquipRunningInfo;
import com.hzforward.oee.domain.OeeReport;
import com.hzforward.oee.domain.OeeReportDetail;
import com.hzforward.oee.domain.ShutdownDefaultReason;
import com.hzforward.oee.mapper.OeeReportDetailMapper;
import com.hzforward.oee.service.IGetEquipRunningInfoService;
import com.hzforward.oee.service.IOeeReportDetailService;
import com.hzforward.oee.service.IShutdownDefaultReasonService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * OEE报告详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
@RequiredArgsConstructor
public class OeeReportDetailServiceImpl extends ServiceImpl<OeeReportDetailMapper, OeeReportDetail> implements IOeeReportDetailService
{
    private final OeeReportDetailMapper oeeReportDetailMapper;

    private final IGetEquipRunningInfoService getEquipRunningInfService;
    private final IShutdownDefaultReasonService mesOeeDefaultReasonService;

    /**
     * 获取OEE详情信息
     *
     * @param reportId id
     */
    @Override
    public List<OeeReportDetail> getOeeReportDetailList(Long reportId) {
        return oeeReportDetailMapper.selectList(new LambdaQueryWrapper<OeeReportDetail>()
                .eq(OeeReportDetail::getReportId, reportId)
                .orderByAsc(OeeReportDetail::getBeginTime)
        );
    }

    /**
     * 获取并设置设备OEE详情信息
     *
     * @param oeeReport oeeReport
     */
    @Override
    public void getAndSetOeeReportDetail(OeeReport oeeReport) {

        // 确认查询的设备工作状态是否已经有部分汇总数据
        List<OeeReportDetail> existOeeReportDetailList = getOeeReportDetailList(oeeReport.getId());

        // 若当天的数据已全汇总,则直接返回
        if (StringUtils.isNotEmpty(existOeeReportDetailList) && existOeeReportDetailList.get(existOeeReportDetailList.size() - 1).getEndTime().equals(DateUtils.getTodayLastTime(oeeReport.getReportDate()))) {
            oeeReport.setOeeReportDetailList(existOeeReportDetailList);
            return;
        }

        Date beginQueryDateTime = oeeReport.getReportDate();

        if(StringUtils.isNotEmpty(existOeeReportDetailList)) {
            OeeReportDetail lastOeeReportDetail = existOeeReportDetailList.get(existOeeReportDetailList.size() - 1);
            beginQueryDateTime = lastOeeReportDetail.getEndTime();
        } else {
            existOeeReportDetailList = new ArrayList<>();
        }

        // 从设备基础运行数据中获取设备的运行数据
        List<EquipRunningInfo> equipRunningInfoList = getEquipRunningInfService.getRunningInfo(oeeReport.getEquipNo(), oeeReport.getReportDate(), beginQueryDateTime);

        if(StringUtils.isEmpty(equipRunningInfoList)) {
            oeeReport.setOeeReportDetailList(existOeeReportDetailList);
            return;
        }

        setOeeReportDetail(oeeReport, existOeeReportDetailList, equipRunningInfoList);
    }

    private void setOeeReportDetail(OeeReport oeeReport, List<OeeReportDetail> existOeeReportDetailList, List<EquipRunningInfo> equipRunningInfoList) {

        // 有新增数据, 设置时长重新计算标志位
        oeeReport.setRecalculateFlag(true);

        List<OeeReportDetail> addOeeReportDetailList = new ArrayList<>();
        // 将设备运行数据汇总修正为OEE数据
        for (EquipRunningInfo equipRunningInfo : equipRunningInfoList) {

            OeeReportDetail oeeReportDetail = new OeeReportDetail();

            oeeReportDetail.setReportId(oeeReport.getId());
            oeeReportDetail.setRunningState(equipRunningInfo.getRunningState());
            oeeReportDetail.setWorkingState(equipRunningInfo.getRunningState());

            // 设备工作状态, 若设备报警则设置为异常停机,停机原因为设备故障
            if(equipRunningInfo.getRunningState().equals("3")) {
                oeeReportDetail.setRunningState("1");
                oeeReportDetail.setWorkingState("1");
                oeeReportDetail.setReasonType(ReasonTypeConstant.EQUIP_FAILURE);
                oeeReportDetail.setReasonDescribe("设备报警");
            }

            if(equipRunningInfo.getRunningState().equals("1") || equipRunningInfo.getRunningState().equals("0")) {
                oeeReportDetail.setReasonType(ReasonTypeConstant.UN_SET_REASON);
            }

            oeeReportDetail.setBeginTime(equipRunningInfo.getBeginTime());
            oeeReportDetail.setEndTime(equipRunningInfo.getEndTime());

            addOeeReportDetailList.add(oeeReportDetail);
        }

        // 获取当前设备默认的停机原因
        List<ShutdownDefaultReason> shutdownDefaultReasonList = mesOeeDefaultReasonService.list(new LambdaQueryWrapper<ShutdownDefaultReason>()
                .eq(ShutdownDefaultReason::getSectionId, oeeReport.getLedgerInfo().getSectionId())
        );

        // 根据默认停机原因修正OEE数据
        for (ShutdownDefaultReason shutdownDefaultReason : shutdownDefaultReasonList) {
            Date reasonBeginTime = DateUtils.appendDateAndTime(oeeReport.getReportDate(), shutdownDefaultReason.getBeginTime());
            Date reasonEndTime = DateUtils.appendDateAndTime(oeeReport.getReportDate(), shutdownDefaultReason.getEndTime());

            List<OeeReportDetail> temporaryOeeReportDetailList = new ArrayList<>();

            for (OeeReportDetail oeeReportDetail : addOeeReportDetailList) {
                // 仅对设备状态不为运行的数据进行校验  校验默认原因 和 OEE 时间段是否有重合
                if(!oeeReportDetail.getRunningState().equals("2") && DateUtils.hasOverlap(oeeReportDetail.getBeginTime(), oeeReportDetail.getEndTime(), reasonBeginTime, reasonEndTime)) {
                    OeeReportDetail addOeeReportDetailOne = new OeeReportDetail();
                    OeeReportDetail addOeeReportDetailTwo = new OeeReportDetail();
                    OeeReportDetail addOeeReportDetailThree = new OeeReportDetail();

                    BeanUtils.copyProperties(oeeReportDetail, addOeeReportDetailOne);
                    BeanUtils.copyProperties(oeeReportDetail, addOeeReportDetailTwo);
                    BeanUtils.copyProperties(oeeReportDetail, addOeeReportDetailThree);

                    // 分解该时间段
                    if(reasonBeginTime.after(oeeReportDetail.getBeginTime())) {
                        // 原因开始时间在设备运行开始时间后面
                        if(reasonEndTime.before(oeeReportDetail.getEndTime())) {
                            // 原因时间段在该设备运行时间段中间,故设备运行分解为3段
                            addOeeReportDetailOne.setEndTime(reasonBeginTime);

                            addOeeReportDetailTwo.setBeginTime(reasonBeginTime);
                            addOeeReportDetailTwo.setEndTime(reasonEndTime);
                            setReasonByShutdownDefaultReason(addOeeReportDetailTwo, shutdownDefaultReason);

                            addOeeReportDetailThree.setBeginTime(reasonEndTime);

                            temporaryOeeReportDetailList.add(addOeeReportDetailOne);
                            temporaryOeeReportDetailList.add(addOeeReportDetailTwo);
                            temporaryOeeReportDetailList.add(addOeeReportDetailThree);
                        } else {
                            addOeeReportDetailOne.setEndTime(reasonBeginTime);

                            addOeeReportDetailTwo.setBeginTime(reasonBeginTime);
                            setReasonByShutdownDefaultReason(addOeeReportDetailTwo, shutdownDefaultReason);

                            temporaryOeeReportDetailList.add(addOeeReportDetailOne);
                            temporaryOeeReportDetailList.add(addOeeReportDetailTwo);
                        }
                    } else {
                        if(reasonEndTime.before(oeeReportDetail.getEndTime())) {
                            addOeeReportDetailOne.setEndTime(reasonEndTime);
                            setReasonByShutdownDefaultReason(addOeeReportDetailOne, shutdownDefaultReason);

                            addOeeReportDetailTwo.setBeginTime(reasonEndTime);

                            temporaryOeeReportDetailList.add(addOeeReportDetailOne);
                            temporaryOeeReportDetailList.add(addOeeReportDetailTwo);
                        } else {
                            // 原因时间段将该条设备运行时间段全包含,故直接设置原因即可
                            setReasonByShutdownDefaultReason(addOeeReportDetailOne, shutdownDefaultReason);
                            temporaryOeeReportDetailList.add(addOeeReportDetailOne);
                        }
                    }
                } else {
                    temporaryOeeReportDetailList.add(oeeReportDetail);
                }
            }

            addOeeReportDetailList = temporaryOeeReportDetailList;
        }

        // 计算时长
        addOeeReportDetailList.forEach(oeeReportDetail -> {
            oeeReportDetail.setDuration(DateUtils.differentSecondByMillisecond(oeeReportDetail.getBeginTime(), oeeReportDetail.getEndTime()));
        });

        // 排序
        addOeeReportDetailList.sort(Comparator.comparing(OeeReportDetail::getBeginTime));

        // 设置返回前端的数据
        existOeeReportDetailList.addAll(addOeeReportDetailList);
        oeeReport.setOeeReportDetailList(existOeeReportDetailList);

        // 将新增的设备OEE数据插入到详情表中
        // 若最后条数据的结束时间不是当天的23:59:59, 则不插入该条数据
        if(!addOeeReportDetailList.get(addOeeReportDetailList.size()-1).getEndTime().equals(DateUtils.getTodayLastTime(oeeReport.getReportDate()))) {
            addOeeReportDetailList.remove(addOeeReportDetailList.size()-1);
        }

        if(StringUtils.isNotEmpty(addOeeReportDetailList)) {
            oeeReportDetailMapper.insertBatchSomeColumn(addOeeReportDetailList);
        }
    }

    /**
     * 设置OEE异常停机原因信息
     *
     * @param oeeReportDetail oeeReportDetail
     */
    @Override
    public void setOeeReportDetailReason(OeeReportDetail oeeReportDetail) {
        OeeReportDetail updateOeeReportDetail = getById(oeeReportDetail.getId());

        updateOeeReportDetail.setWorkingState(getWorkingState(oeeReportDetail.getRunningState(), oeeReportDetail.getReasonType()));
        updateOeeReportDetail.setReasonType(oeeReportDetail.getReasonType());
        updateOeeReportDetail.setReasonDescribe(oeeReportDetail.getReasonDescribe());
        updateOeeReportDetail.setReasonBy(SecurityUtils.getLoginUser().getUsername());
        updateOeeReportDetail.setReasonTime(DateUtils.getNowDate());

        updateById(updateOeeReportDetail);
    }

    // 根据默认的停机原因设置OEE停机原因
    private void setReasonByShutdownDefaultReason(OeeReportDetail oeeReportDetail, ShutdownDefaultReason shutdownDefaultReason) {
        oeeReportDetail.setWorkingState(getWorkingState(oeeReportDetail.getRunningState(), shutdownDefaultReason.getReasonType()));
        oeeReportDetail.setReasonType(shutdownDefaultReason.getReasonType());
        oeeReportDetail.setReasonDescribe(shutdownDefaultReason.getReasonDescribe());
        oeeReportDetail.setReasonBy(shutdownDefaultReason.getReasonBy());
        oeeReportDetail.setReasonTime(DateUtils.getNowDate());
    }

    // 根据设备运行状态, 原因类型 获取 设备工作状态
    private String getWorkingState(String runningState, String reasonType) {
        if(runningState.equals("2")) {
            return "2";
        } else {
            if(ReasonTypeConstant.PLAN_SHUTDOWN.contains(reasonType)) {
                // 计划停机的类型
                return "3";
            } else {
                // 异常停机类型
                return "1";
            }
        }
    }

}
