package com.hzforward.oee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.domain.entity.SysDictData;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.DictUtils;
import com.hzforward.common.utils.LockUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.equipmentManage.domain.LedgerInfo;
import com.hzforward.equipmentManage.domain.repair.RepairReport;
import com.hzforward.equipmentManage.service.ILedgerInfoService;
import com.hzforward.equipmentManage.service.IRepairReportService;
import com.hzforward.oee.constant.ReasonTypeConstant;
import com.hzforward.oee.domain.OeeReport;
import com.hzforward.oee.domain.OeeReportDetail;
import com.hzforward.oee.domain.ShutdownReason;
import com.hzforward.oee.mapper.OeeReportMapper;
import com.hzforward.oee.service.IOeeReportDetailService;
import com.hzforward.oee.service.IOeeReportService;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.locks.Lock;

/**
 * OEE报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
@RequiredArgsConstructor
public class OeeReportServiceImpl extends ServiceImpl<OeeReportMapper, OeeReport> implements IOeeReportService
{
    private final OeeReportMapper oeeReportMapper;

    private final IOeeReportDetailService oeeReportDetailService;
    private final ILedgerInfoService ledgerInfoService;
    private final IRepairReportService repairReportService;

    /**
     * 获取设备OEE列表(只获取汇总数据,不查询详情数据)
     *
     * @param oeeReport oeeReport
     * @return oee集合
     */
    @Override
    public List<OeeReport> selectOeeReportList(OeeReport oeeReport) {
        List<OeeReport> oeeReportList = new ArrayList<>();

        List<Date> queryDateRange = DateUtils.getDateRangeList(oeeReport.getDateRange());
        List<LedgerInfo> networkingLegerInfoList = getNetworkingLegerInfoList(oeeReport);

        for (LedgerInfo ledgerInfo : networkingLegerInfoList) {
            for(Date date : queryDateRange) {
                OeeReport addOeeReport = oeeReportMapper.selectOne(new LambdaQueryWrapper<OeeReport>()
                        .eq(OeeReport::getReportDate, date)
                        .eq(OeeReport::getEquipNo, ledgerInfo.getEquipNo())
                        .last("limit 1")
                );

                if(addOeeReport == null) {
                    addOeeReport = getAndSetOeeReport(date, ledgerInfo);
                    addOeeReport.setOeeReportDetailList(null);
                }

                addOeeReport.setLedgerInfo(ledgerInfo);
                oeeReportList.add(addOeeReport);
            }
        }

        return oeeReportList;
    }

    /**
     * 获取设备OEE详情信息
     *
     * @param oeeReport oee
     * @return oee集合
     */
    @Override
    public List<OeeReport> selectOeeReportDetailList(OeeReport oeeReport) {
        List<OeeReport> oeeReportList = new ArrayList<>();

        // 获取需要查询详情的设备列表信息
        List<LedgerInfo> networkingLegerInfoList = getNetworkingLegerInfoList(oeeReport);

        for (LedgerInfo ledgerInfo : networkingLegerInfoList) {
            OeeReport oeeReportInfo = getAndSetOeeReport(oeeReport.getReportDate(), ledgerInfo);
            oeeReportList.add(oeeReportInfo);
        }

        return oeeReportList;
    }

    /**
     * 获取并设置当天设备OEE情况汇总信息
     *
     * @param reportDate 日期
     * @param ledgerInfo 设备信息
     * @return oee原因描述集合
     */
    private OeeReport getAndSetOeeReport(Date reportDate, LedgerInfo ledgerInfo) {

        // 不允许查询超过今天的数据
        if(reportDate.after(DateUtils.getTodayDate())) {
            throw new ServiceException("不允许查询超过当天日期的数据,请确认查询日期");
        }

        Lock lock = LockUtils.getLock(DateUtils.parseDateToStr("yyyy-MM-dd", reportDate) + "-" + ledgerInfo.getEquipNo());
        lock.lock();
        OeeReport oeeReport = new OeeReport();
        try {
            oeeReport = oeeReportMapper.selectOne(new LambdaQueryWrapper<OeeReport>()
                    .eq(OeeReport::getReportDate, reportDate)
                    .eq(OeeReport::getEquipNo, ledgerInfo.getEquipNo())
                    .last("limit 1")
            );

            if(StringUtils.isNull(oeeReport)){
                oeeReport = new OeeReport();
                oeeReport.setEquipNo(ledgerInfo.getEquipNo());
                oeeReport.setReportDate(reportDate);
                oeeReportMapper.insert(oeeReport);
            }
            oeeReport.setLedgerInfo(ledgerInfo);

            // 获取当天已汇总的OEE数据,并汇总当天还未汇总的数据
            oeeReportDetailService.getAndSetOeeReportDetail(oeeReport);

            // 计算时长
            if(oeeReport.getRecalculateFlag()) {
                calculateOeeReport(oeeReport);
            }

            return oeeReport;
        }catch (Exception ignored){

        }finally {
            lock.unlock();
        }

        return oeeReport;
    }

    /**
     * 计算设备OEE汇总信息
     *
     * @param oeeReport oeeReport
     */
    @Override
    public void calculateOeeReport(OeeReport oeeReport) {
        // 计算各类时长
        // 离线时长(秒)
        int offlineDuration = 0;
        // 停机时长(秒)
        int shutdownDuration = 0;
        // 运行时长(秒)
        int runningDuration = 0;
        // 设备工作状态未知时长(秒)
        int unknownDuration = 0;
        // 计划停机时长(秒)
        int planShutdownDuration = 0;
        // 异常停机时长(秒)
        int abnormalShutdownDuration = 0;

        // 5S时长(秒)
        int TPM5SDuration = 0;
        // 休息时长(秒)
        int restDuration = 0;

        for(OeeReportDetail oeeReportDetail : oeeReport.getOeeReportDetailList()) {
            switch (oeeReportDetail.getRunningState()) {
                case "0":
                    offlineDuration = offlineDuration + oeeReportDetail.getDuration();
                    break;
                case "1":
                    shutdownDuration = shutdownDuration + oeeReportDetail.getDuration();
                    break;
                case "2":
                    runningDuration = runningDuration + oeeReportDetail.getDuration();
                    break;
            }
            switch (oeeReportDetail.getWorkingState()) {
                case "0":
                    unknownDuration = unknownDuration + oeeReportDetail.getDuration();
                    break;
                case "1":
                    planShutdownDuration = planShutdownDuration + oeeReportDetail.getDuration();
                    break;
                case "3":
                    abnormalShutdownDuration = abnormalShutdownDuration + oeeReportDetail.getDuration();
                    break;
            }

            if(oeeReportDetail.getReasonType() != null) {
                switch (oeeReportDetail.getReasonType()) {
                    case ReasonTypeConstant.TPM5S:
                        TPM5SDuration = TPM5SDuration + oeeReportDetail.getDuration();
                        break;
                    case ReasonTypeConstant.REST:
                        restDuration = restDuration + oeeReportDetail.getDuration();
                        break;
                }
            }
        }

        oeeReport.setOfflineDuration(offlineDuration);
        oeeReport.setShutdownDuration(shutdownDuration);
        oeeReport.setRunningDuration(runningDuration);
        oeeReport.setUnknownDuration(unknownDuration);
        oeeReport.setAbnormalShutdownDuration(planShutdownDuration);
        oeeReport.setPlanShutdownDuration(abnormalShutdownDuration);

        // 计算设备OEE
        DecimalFormat df = new DecimalFormat("#.##");

        // OEE1的时间需要减去休息和5S的时间   1TPM5S   99休息
        String oeeOne = df.format((double) oeeReport.getRunningDuration() / ((24 * 60 * 60) - TPM5SDuration - restDuration) * 100) + "%";
        oeeReport.setOeeOne(oeeOne);

        // OEE1的时间需要减去计划停机时间
        String oeeTwo = df.format((double) oeeReport.getRunningDuration() / ((24 * 60 * 60) - oeeReport.getPlanShutdownDuration()) * 100) + "%";
        oeeReport.setOeeTwo(oeeTwo);

        // 记录
        updateById(oeeReport);
    }

    /**
     * 定时汇总前一天的OEE数据
     *
     */
    @Override
    public void summaryOEEData() {
        Date calculateDate = DateUtils.addDays(DateUtils.getTodayDate(), -1);
        OeeReport oeeReport = new OeeReport();
        oeeReport.setReportDate(calculateDate);
        selectOeeReportDetailList(oeeReport);
    }

    /**
     * 根据工段导出OEE信息
     *
     * @param oeeReport oee原因描述
     */
    public void exportOeeCollect(HttpServletResponse response, OeeReport oeeReport) {

    }

    /**
     * 导出设备OEE列表
     *
     * @param oeeReport oee原因描述
     */
    @Override
    public void exportOeeInfo(HttpServletResponse response, OeeReport oeeReport) {
        List<OeeReport> oeeReportList = new ArrayList<>();
        List<Date> queryDateRange = DateUtils.getDateRangeList(oeeReport.getDateRange());

        for(Date date : queryDateRange) {
            oeeReport.setReportDate(date);
            oeeReportList.addAll(selectOeeReportDetailList(oeeReport));
        }

        try(SXSSFWorkbook wb = new SXSSFWorkbook(100)) {
            Sheet sheet = wb.createSheet();

            // 设置表头
            Row titleRow = sheet.createRow(0);
            int titleCol = 0;

            // 设备编号
            sheet.setColumnWidth(titleCol, (int) (256*9+184));
            titleCol = setCellValue(titleRow, titleCol, "设备编号");

            // 设备名称
            sheet.setColumnWidth(titleCol, (int) (256*9+184));
            titleCol = setCellValue(titleRow, titleCol, "设备名称");

            // 设备工段
            sheet.setColumnWidth(titleCol, (int) (256*9+184));
            titleCol = setCellValue(titleRow, titleCol, "设备工段");

            // 日期
            sheet.setColumnWidth(titleCol, (int) (256*9+184));
            titleCol = setCellValue(titleRow, titleCol, "日期");

            // 离线时长(小时)
            sheet.setColumnWidth(titleCol, (int) (256*11+184));
            titleCol = setCellValue(titleRow, titleCol, "离线时长(小时)");

            // 运行时长(小时)
            sheet.setColumnWidth(titleCol, (int) (256*11+184));
            titleCol = setCellValue(titleRow, titleCol, "运行时长(小时)");

            // 计划停机时长(小时)
            sheet.setColumnWidth(titleCol, (int) (256*11+184));
            titleCol = setCellValue(titleRow, titleCol, "计划停机时长(小时)");

            // 异常停机时长(小时)
            sheet.setColumnWidth(titleCol, (int) (256*11+184));
            titleCol = setCellValue(titleRow, titleCol, "异常停机时长(小时)");

            sheet.setColumnWidth(titleCol, (int) (256*11+184));
            titleCol = setCellValue(titleRow, titleCol, "OEE1(运行时长/24小时-休息时长-5S)");

            sheet.setColumnWidth(titleCol, (int) (256*11+184));
            titleCol = setCellValue(titleRow, titleCol, "OEE2(运行时长/24小时-休息时长-5S-无生产任务)");

            // 维修时长(小时)
            sheet.setColumnWidth(titleCol, (int) (256*11+184));
            titleCol = setCellValue(titleRow, titleCol, "维修时长(小时)");

            List<SysDictData> dictDataList = DictUtils.getDictCache("oee_reason_type");
            // 其他原因(小时)
            assert dictDataList != null;
            for (SysDictData dictData: dictDataList) {
                sheet.setColumnWidth(titleCol, (int) (256*11+184));
                titleCol = setCellValue(titleRow, titleCol, dictData.getDictLabel() + "(小时)");
            }

            // 设置值
            for(int i = 0; i < oeeReportList.size(); i++) {

                OeeReport exportOeeReport = oeeReportList.get(i);
                Row row = sheet.createRow(i + 1);
                int col = 0;

                // 设备编号
                col = setCellValue(row, col, exportOeeReport.getEquipNo());
                // 设备名称
                col = setCellValue(row, col, exportOeeReport.getLedgerInfo().getEquipName());
                // 设备工段
                col = setCellValue(row, col, exportOeeReport.getLedgerInfo().getSection().getSectionName());
                // 日期
                col = setCellValue(row, col, DateUtils.parseDateToStr("yyyy-MM-dd", exportOeeReport.getReportDate()));
                // 离线时长(小时)
                col = setCellValue(row, col, DateUtils.convertSecondsToHours(exportOeeReport.getUnknownDuration()));
                // 运行时长(小时)
                col = setCellValue(row, col, DateUtils.convertSecondsToHours(exportOeeReport.getRunningDuration()));
                // 计划停机时长(小时)
                col = setCellValue(row, col, DateUtils.convertSecondsToHours(exportOeeReport.getPlanShutdownDuration()));
                // 异常停机时长(小时)
                col = setCellValue(row, col, DateUtils.convertSecondsToHours(exportOeeReport.getAbnormalShutdownDuration()));

                // OEE1
                col = setCellValue(row, col, exportOeeReport.getOeeOne());
                // OEE2
                col = setCellValue(row, col, exportOeeReport.getOeeTwo());
                // 维修时长(小时)
                col = setCellValue(row, col, DateUtils.convertSecondsToHours(getMaintenanceTime(exportOeeReport)));
                // 其他原因(小时)
                Map<String, ShutdownReason.ReasonTime> reasonTimeList = getShutDownReasonTime(exportOeeReport.getOeeReportDetailList());
                for (SysDictData dictData: dictDataList) {
                    int duration = reasonTimeList.get(dictData.getDictValue()).getDuration();
                    col = setCellValue(row, col, DateUtils.convertSecondsToHours(duration));
                }
            }

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            wb.write(response.getOutputStream());

        }catch (Exception e) {
            throw new RuntimeException(e);
        };
    }

    // 获取已联网设备列表
    private List<LedgerInfo> getNetworkingLegerInfoList(OeeReport mesOee) {

        LedgerInfo ledgerInfo = new LedgerInfo();
        ledgerInfo.setFactoryArea(mesOee.getFactoryArea());
        ledgerInfo.setDepartment(mesOee.getDepartment());
        ledgerInfo.setSectionId(mesOee.getSectionId());
        ledgerInfo.setEquipNo(mesOee.getEquipNo());
        ledgerInfo.setNetworkingFlag("Y");

        return ledgerInfoService.selectLedgerInfoList(ledgerInfo);
    }

    // 设置设备维修时长数据
    private int getMaintenanceTime(OeeReport oeeReport) {
        // 获取设备维修信息
        // 获取任意时间报修,但未完成维修的维修数据
        List<RepairReport> equipInMaintenance = repairReportService.getEquipInMaintenance(oeeReport.getEquipNo(), oeeReport.getReportDate());

        int maintenanceTime = 0;

        for(RepairReport repairReport : equipInMaintenance) {

            // 若设备报修时间在当天前,维修时长从当天0点开始计算
            Date startTime = repairReport.getRepairTime().before(DateUtils.getTodayStartTime(oeeReport.getReportDate())) ?
                    DateUtils.getTodayStartTime(oeeReport.getReportDate()) : repairReport.getRepairTime();

            Date endTime;

            if(repairReport.getMaintenanceStatus().equals("5")) {
                // 已经维修完成的,若维修完成时间大于当天24点,则维修时长计算到当天24点
                endTime = repairReport.getRepairedTime().after(DateUtils.getTodayLastTime(oeeReport.getReportDate())) ?
                        DateUtils.getTodayLastTime(oeeReport.getReportDate()) : repairReport.getRepairedTime();
            } else {
                // 未维修完成的, 若获取的是今天的数据,则维修时间计算到当前时间,否则为当天24点
                endTime = DateUtils.isSameDay(oeeReport.getReportDate(), DateUtils.getNowDate()) ?
                        DateUtils.getNowDate() : DateUtils.getTodayLastTime(oeeReport.getReportDate());
            }

            assert startTime != null;
            assert endTime != null;
            maintenanceTime += DateUtils.differentSecondByMillisecond(startTime, endTime);
        }
        return maintenanceTime;
    }

    // 设置设备停产报告数据
    private Map<String, ShutdownReason.ReasonTime> getShutDownReasonTime(List<OeeReportDetail> oeeReportDetailList) {

        List<SysDictData> dictData = DictUtils.getDictCache("oee_reason_type");

        Map<String, ShutdownReason.ReasonTime> reasonMap = new HashMap<>();
        assert dictData != null;
        for(SysDictData sysDictData : dictData) {
            ShutdownReason.ReasonTime reasonTime = new ShutdownReason.ReasonTime();
            reasonTime.setReasonTypeCode(sysDictData.getDictValue());
            reasonTime.setReasonTypeName(sysDictData.getDictLabel());
            reasonTime.setDuration(0);
            reasonMap.put(sysDictData.getDictValue(), reasonTime);
        }

        for(OeeReportDetail oeeReportDetail : oeeReportDetailList) {
            if(oeeReportDetail.getReasonType() != null) {
                ShutdownReason.ReasonTime reasonTime = reasonMap.get(oeeReportDetail.getReasonType());
                reasonTime.setDuration(reasonTime.getDuration() + oeeReportDetail.getDuration());
            }
        }

        return reasonMap;
    }

    private int setCellValue(Row row, int col, String value) {
        row.createCell(col).setCellValue(value);
        return col + 1;
    }
}
