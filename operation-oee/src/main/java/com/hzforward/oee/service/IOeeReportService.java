package com.hzforward.oee.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.OeeReport;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * OEE报告Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IOeeReportService extends IService<OeeReport>
{
    /**
     * 获取设备OEE列表
     *
     * @param oeeReport oeeReport
     * @return oee集合
     */
    List<OeeReport> selectOeeReportList(OeeReport oeeReport);

    /**
     * 获取设备OEE列表以及详情信息
     *
     * @param oeeReport oeeReport
     * @return oee集合
     */
    List<OeeReport> selectOeeReportDetailList(OeeReport oeeReport);

    /**
     * 计算设备OEE汇总信息
     *
     * @param oeeReport oeeReport
     */
    void calculateOeeReport(OeeReport oeeReport);

    /**
     * 定时汇总前一天的OEE数据
     *
     */
    void summaryOEEData();

    /**
     * 导出设备OEE列表
     *
     * @param oeeReport oee原因描述
     */
    void exportOeeInfo(HttpServletResponse response, OeeReport oeeReport);
}
