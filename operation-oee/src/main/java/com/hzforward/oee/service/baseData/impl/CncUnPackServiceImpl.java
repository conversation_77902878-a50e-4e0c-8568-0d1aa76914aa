package com.hzforward.oee.service.baseData.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.oee.domain.baseData.machining.CncRealTimeDetailInfo;
import com.hzforward.oee.domain.baseData.machining.CncRealTimeInfo;
import com.hzforward.oee.domain.baseData.machining.CncRealTimeInfoRow;
import com.hzforward.oee.mapper.baseData.machining.CncRealTimeInfoMapper;
import com.hzforward.oee.mapper.baseData.machining.CncRealTimeInfoRowMapper;
import com.hzforward.oee.service.baseData.ICncUnPackService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备运行状态Service接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Service
@RequiredArgsConstructor
public class CncUnPackServiceImpl implements ICncUnPackService {

    private final CncRealTimeInfoMapper cncRealTimeInfoMapper;
    private final CncRealTimeInfoRowMapper cncRealTimeInfoRowMapper;

    /**
     * cnc基础数据解包
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE7)
    public void unpack() {

        Page<CncRealTimeInfo> page = new Page<>(1, 1000);

        List<CncRealTimeInfo> CncRealTimeInfoList = cncRealTimeInfoMapper.selectPage(page, new LambdaQueryWrapper<CncRealTimeInfo>()
                .isNull(CncRealTimeInfo::getUnpackFlag)
        ).getRecords();

        List<CncRealTimeInfoRow> cncRealTimeInfoRowList = new ArrayList<>();

        for (CncRealTimeInfo cncRealTimeInfo : CncRealTimeInfoList) {
            try {
                JSONObject detailJsonObject = new JSONObject();

                JSONObject jsonObject = JSONObject.parseObject(cncRealTimeInfo.getCncJsonContent(), JSONObject.class);
                JSONArray jsonArray = jsonObject.getJSONArray("data");

                for(Object item : jsonArray){
                    JSONObject itemJSONObject = (JSONObject) item;
                    detailJsonObject.put(itemJSONObject.getString("name"), itemJSONObject.getString("value"));
                }

                CncRealTimeDetailInfo cncRealTimeDetailInfo = JSON.parseObject(JSON.toJSONString(detailJsonObject), CncRealTimeDetailInfo.class);
                CncRealTimeInfoRow cncRealTimeInfoRow = new CncRealTimeInfoRow();
                BeanUtils.copyProperties(cncRealTimeDetailInfo, cncRealTimeInfoRow);

                cncRealTimeInfoRow.setDataId(cncRealTimeInfo.getDataId());
                cncRealTimeInfoRow.setCreateTime(cncRealTimeInfo.getCreateTime());
                cncRealTimeInfoRow.setDate(cncRealTimeInfo.getCreateTime());
                cncRealTimeInfoRow.setEquipNo(cncRealTimeInfo.getDevNo());
                cncRealTimeInfoRow.setEquipType(cncRealTimeInfo.getDevType());

                if(cncRealTimeInfoRow.getCncRunStatus() != null && !cncRealTimeInfoRow.getCncRunStatus().equals("-1")){
                    // 需要进行解包的数据列表
                    cncRealTimeInfoRowList.add(cncRealTimeInfoRow);
                } else {
                    // 无工作状态的数据即为无意义数据,直接删除
                    cncRealTimeInfoMapper.deleteById(cncRealTimeInfo.getDataId());
                }

            } catch (Exception e) {
                // 若解包失败, 则设置解包状态为F
                cncRealTimeInfoMapper.update(new LambdaUpdateWrapper<CncRealTimeInfo>()
                        .set(CncRealTimeInfo::getUnpackFlag, "F")
                        .eq(CncRealTimeInfo::getDataId, cncRealTimeInfo.getDataId())
                );
            }
        }

        // 拆分List, 因为sqlserver的批量插入有长度限制
        List<List<CncRealTimeInfoRow>> subList = Lists.partition(cncRealTimeInfoRowList, 30);

        for(List<CncRealTimeInfoRow> everySubList : subList){
            List<String> dataIdList = everySubList.stream().map(CncRealTimeInfoRow::getDataId).collect(Collectors.toList());

            try {
                cncRealTimeInfoRowMapper.insertBatchSomeColumn(everySubList);
                cncRealTimeInfoMapper.deleteBatchIds(dataIdList);
            }catch (Exception e){
                cncRealTimeInfoMapper.update(new LambdaUpdateWrapper<CncRealTimeInfo>()
                        .set(CncRealTimeInfo::getUnpackFlag, "F")
                        .in(CncRealTimeInfo::getDataId, dataIdList)
                );
            }
        }

    }

}
