package com.hzforward.oee.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.poi.ExcelUtil;
import com.hzforward.equipmentManage.service.ILedgerInfoService;
import com.hzforward.oee.constant.ReasonTypeConstant;
import com.hzforward.oee.domain.OeeReport;
import com.hzforward.oee.domain.OeeReportDetail;
import com.hzforward.oee.domain.ShutdownReason;
import com.hzforward.oee.mapper.OeeReportDetailMapper;
import com.hzforward.oee.service.IOeeReportDetailService;
import com.hzforward.oee.service.IOeeReportService;
import com.hzforward.oee.service.IShutdownReasonService;
import com.hzforward.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 异常停机原因描述Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
@RequiredArgsConstructor
public class ShutdownReasonServiceImpl implements IShutdownReasonService
{
    private final OeeReportDetailMapper oeeReportDetailMapper;

    private final IOeeReportService oeeReportService;
    private final IOeeReportDetailService oeeReportDetailService;

    private final ILedgerInfoService ledgerInfoService;

    private final ISysUserService sysUserService;

    /**
     * 获取异常停机报告列表(PC)
     *
     * @param oeeReport oeeReport
     */
    @Override
    public List<OeeReport> getShutdownReasonList(OeeReport oeeReport) {
        List<OeeReport> oeeReportList = oeeReportService.selectOeeReportList(oeeReport);

        for(OeeReport setOeeReport : oeeReportList){
            List<OeeReportDetail> oeeReportDetailList = oeeReportDetailService.list(new LambdaQueryWrapper<OeeReportDetail>()
                    .eq(OeeReportDetail::getReportId, setOeeReport.getId())
                    .ne(OeeReportDetail::getWorkingState, "2")
            );

            int unSetReasonDuration = 0;
            int setReasonDuration = 0;

            for(OeeReportDetail oeeReportDetail : oeeReportDetailList){
                if(StringUtils.isEmpty(oeeReportDetail.getReasonType()) || oeeReportDetail.getReasonType().equals(ReasonTypeConstant.UN_SET_REASON)) {
                    unSetReasonDuration += oeeReportDetail.getDuration();
                }else {
                    setReasonDuration += oeeReportDetail.getDuration();
                }
            }

            setOeeReport.setUnSetReasonDuration(unSetReasonDuration);
            setOeeReport.setSetReasonDuration(setReasonDuration);
        }

        return oeeReportList;
    }

    /**
     * 获取需要设置原因的时间列表 (PC段)
     *
     * @param oeeReport oeeReport
     */
    public List<OeeReportDetail> getShutdownReasonDetail(OeeReport oeeReport) {
        List<OeeReportDetail> oeeReportDetailList = oeeReportDetailMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<OeeReportDetail>()
                .eq(OeeReportDetail::getReportId, oeeReport.getId())
                .eq(OeeReportDetail::getReasonType, oeeReport.getReasonType())
                .ne(OeeReportDetail::getWorkingState, "2")
        );

        // 根据移动端筛选停机时长
        oeeReportDetailList.removeIf(oeeReportDetail -> oeeReportDetail.getDuration() < oeeReport.getFilterMinute() * 60);

        return oeeReportDetailList;
    }

    /**
     * 获取需要设置原因的时间列表 (移动端)
     *
     * @param oeeReport oeeReport
     */
    public OeeReport getReasonSetList(OeeReport oeeReport) {

        List<OeeReport> oeeReportDetailList = oeeReportService.selectOeeReportDetailList(oeeReport);

        if(StringUtils.isEmpty(oeeReportDetailList) || oeeReportDetailList.size() != 1) {
            throw new ServiceException("当前设备无OEE信息");
        }

        OeeReport returnOeeReport = oeeReportDetailList.get(0);

        // 移除无法设置原因的数据
        returnOeeReport.getOeeReportDetailList().removeIf(oeeReportDetail -> oeeReportDetail.getId() == null);
        // 先移除所有设备正常运行的数据
        returnOeeReport.getOeeReportDetailList().removeIf(oeeReportDetail -> oeeReportDetail.getRunningState().equals("2"));

        // 根据移动端筛选原因是否已设置
        if(oeeReport.getReasonSetFlag().equals("0")) {
            returnOeeReport.getOeeReportDetailList().removeIf(oeeReportDetail -> !oeeReportDetail.getReasonType().equals("0"));
        }else if(oeeReport.getReasonSetFlag().equals("1")) {
            returnOeeReport.getOeeReportDetailList().removeIf(oeeReportDetail -> oeeReportDetail.getReasonType().equals("0"));
        }

        // 根据移动端筛选停机时长
        returnOeeReport.getOeeReportDetailList().removeIf(oeeReportDetail -> oeeReportDetail.getDuration() < oeeReport.getFilterMinute() * 60);

        return returnOeeReport;
    }

    /**
     * 设置OEE异常停机原因信息(PC端)
     *
     * @param oeeReportDetail oeeReportDetail
     */
    @Override
    public OeeReport setShutdownReason(OeeReportDetail oeeReportDetail) {

        // 修改详情信息原因
        oeeReportDetailService.setOeeReportDetailReason(oeeReportDetail);

        OeeReport oeeReport = oeeReportService.getById(oeeReportDetail.getReportId());
        oeeReport.setOeeReportDetailList(oeeReportDetailService.getOeeReportDetailList(oeeReportDetail.getReportId()));
        // 重新计算时间
        oeeReportService.calculateOeeReport(oeeReport);

        oeeReport.setLedgerInfo(ledgerInfoService.getInfoByEquipNo(oeeReport.getEquipNo()));

        // 返回修改后的设备OEE详情列表
        return oeeReport;
    }

    /**
     * 设置OEE异常停机原因信息(移动端)
     *
     * @param oeeReportDetail oeeReportDetail
     */
    @Override
    public void setShutdownReasonMobile(OeeReportDetail oeeReportDetail) {

        // 修改详情信息原因
        oeeReportDetailService.setOeeReportDetailReason(oeeReportDetail);

        OeeReport oeeReport = oeeReportService.getById(oeeReportDetail.getReportId());
        oeeReport.setOeeReportDetailList(oeeReportDetailService.getOeeReportDetailList(oeeReportDetail.getReportId()));
        // 重新计算时间
        oeeReportService.calculateOeeReport(oeeReport);
    }

    /**
     * 导出异常停机原因列表
     *
     * @param oeeReport oeeReport
     */
    @Override
    public void exportShutdownReason(HttpServletResponse response, OeeReport oeeReport) {
        List<ShutdownReason> shutdownReasonList = new ArrayList<>();

        List<OeeReport> oeeReportList = oeeReportService.selectOeeReportList(oeeReport);

        for(OeeReport setOeeReport : oeeReportList) {
            List<OeeReportDetail> oeeReportDetailList = oeeReportDetailService.list(new LambdaQueryWrapper<OeeReportDetail>()
                    .eq(OeeReportDetail::getReportId, setOeeReport.getId())
                    .ne(OeeReportDetail::getWorkingState, "2")
            );

            for(OeeReportDetail oeeReportDetail : oeeReportDetailList) {
                ShutdownReason shutdownReason = new ShutdownReason();
                shutdownReason.setEquipNo(setOeeReport.getEquipNo());
                shutdownReason.setEquipName(setOeeReport.getLedgerInfo().getEquipName());
                shutdownReason.setSectionName(setOeeReport.getLedgerInfo().getSection().getSectionName());
                shutdownReason.setReasonDate(setOeeReport.getReportDate());

                shutdownReason.setBeginTime(oeeReportDetail.getBeginTime());
                shutdownReason.setEndTime(oeeReportDetail.getEndTime());
                shutdownReason.setDuration(DateUtils.convertSecondsToMinute(oeeReportDetail.getDuration()));
                shutdownReason.setReasonName(sysUserService.getNickNameByUserName(oeeReportDetail.getReasonBy()));
                shutdownReason.setReasonTime(oeeReportDetail.getReasonTime());
                shutdownReason.setReasonType(oeeReportDetail.getReasonType());
                shutdownReason.setReasonDescribe(oeeReportDetail.getReasonDescribe());

                shutdownReasonList.add(shutdownReason);
            }
        }

        ExcelUtil<ShutdownReason> util = new ExcelUtil<>(ShutdownReason.class);
        util.exportExcel(response, shutdownReasonList, "异常停机报告");
    }
}
