package com.hzforward.oee.service.baseData;


/**
 * 采集电缆MES基础数据
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
public interface ICollectCableData
{
    /**
     * 采集束丝机数据
     */
    void collectBeamLineData();

    /**
     * 采集高速挤出机数据
     */
    void collectHighSpeedExtruderData();

    /**
     * 采集高速对绞机数据
     */
    void collectHighSpeedTwistingData();

    /**
     * 采集悬臂悬臂单扭成榄机
     */
    void collectLayingTwistingData();

    /**
     * 采集悬臂悬臂单扭成榄机
     */
    void collectSheathExtruderData();
}
