package com.hzforward.oee.service.baseData.impl;

import com.hzforward.common.utils.StringUtils;
import com.hzforward.oee.constant.baseData.magnetic.FormingPressConstant;
import com.hzforward.oee.domain.baseData.magnetic.FormingPress;
import com.hzforward.oee.mapper.baseData.magnetic.FormingPressMapper;
import com.hzforward.oee.service.baseData.ICollectMagneticData;
import com.hzforward.oee.util.Modbus4jUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 采集主机MES基础数据
 *
 * <AUTHOR>
 * @date 2025-03-14
 */
@Service
@RequiredArgsConstructor
public class CollectMagneticDataImpl implements ICollectMagneticData {

    private final FormingPressMapper formingPressMapper;

    /**
     * 采集清洗线数据
     */
    @Async
    @Override
    public void collectFormingPressData() {
        List<FormingPress> formingPressList = Modbus4jUtils.readModbusData(FormingPressConstant.FORMING_PRESS, FormingPress.class);
        if(StringUtils.isNotEmpty(formingPressList)) formingPressMapper.insertBatchSomeColumn(formingPressList);
    }

}
