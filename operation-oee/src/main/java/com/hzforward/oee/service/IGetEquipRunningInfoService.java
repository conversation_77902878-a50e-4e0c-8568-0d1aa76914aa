package com.hzforward.oee.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.EquipRunningInfo;

import java.util.Date;
import java.util.List;

/**
 * oee报告Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-07
 */
public interface IGetEquipRunningInfoService extends IService<EquipRunningInfo>
{
    /**
     * 根据设备运行详情获取设备汇总信息
     * @param equipNo 设备编号
     * @param setDate 汇总日期
     */
    List<EquipRunningInfo> getRunningInfo(String equipNo, Date setDate, Date beginQueryDateTime);
}
