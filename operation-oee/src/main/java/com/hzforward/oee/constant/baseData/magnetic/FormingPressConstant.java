package com.hzforward.oee.constant.baseData.magnetic;

import com.hzforward.oee.config.ModbusReadConfig;
import com.serotonin.modbus4j.code.DataType;

public class FormingPressConstant
{
        /**
     * 成型压机
     */
    public static final ModbusReadConfig FORMING_PRESS = new ModbusReadConfig(){
        {
            setEquipDetails("2000982", "************", 502);
            setEquipDetails("2001274", "************", 502);
            setEquipDetails("2001275", "************", 502);

            setPointDetailList(1, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 0, "magnetizingCurrent");
            setPointDetailList(2, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 2, "demagnetizationCurrent");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 4, "magnetizationProtectionTime");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 5, "demagnetizationProtectionTime");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 7, "productionModulus");
        }
    };

}
