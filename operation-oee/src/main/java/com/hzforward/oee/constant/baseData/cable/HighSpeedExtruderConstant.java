package com.hzforward.oee.constant.baseData.cable;

import com.hzforward.oee.config.ModbusReadConfig;
import com.serotonin.modbus4j.code.DataType;

public class HighSpeedExtruderConstant {
    /**
     * 高速挤出机
     */
    public static final ModbusReadConfig HIGH_SPEED_EXTRUDER_CONFIG = new ModbusReadConfig() {
        {
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);

            setPointDetailList(1, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1050, "hostOneDisplayTemperature");
            setPointDetailList(2, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1051, "hostOneSetTemperature");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1053, "hostTwoDisplayTemperature");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1054, "hostTwoSetTemperature");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1056, "hostThreeDisplayTemperature");
            setPointDetailList(6, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1057, "hostThreeSetTemperature");
            setPointDetailList(7, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1059, "hostFourDisplayTemperature");
            setPointDetailList(8, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1060, "hostFourSetTemperature");
            setPointDetailList(9, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1062, "hostFiveDisplayTemperature");
            setPointDetailList(10, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1063, "hostFiveSetTemperature");
            setPointDetailList(11, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1065, "hostSixDisplayTemperature");
            setPointDetailList(12, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1066, "hostSixSetTemperature");
            setPointDetailList(13, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1068, "secondaryHostOneDisplayTemperature");
            setPointDetailList(14, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1069, "secondaryHostOneSetTemperature");
            setPointDetailList(15, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1071, "secondaryHostTwoDisplayTemperature");
            setPointDetailList(16, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1072, "secondaryHostTwoSetTemperature");
            setPointDetailList(17, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1074, "secondaryHostThreeDisplayTemperature");
            setPointDetailList(18, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1075, "secondaryHostThreeSetTemperature");
            setPointDetailList(19, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1077, "secondaryHostFourDisplayTemperature");
            setPointDetailList(20, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1078, "secondaryHostFourSetTemperature");
            setPointDetailList(21, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 49, "hmiLineDiameter");
            setPointDetailList(22, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 153, "hmiNowSpeed");
            setPointDetailList(23, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 10, "lineDiameterOutOfTolerance");
            setPointDetailList(24, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 0, "hostRun");
            setPointDetailList(25, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 178, "hmiRealityMeters");
        }
    };
}
