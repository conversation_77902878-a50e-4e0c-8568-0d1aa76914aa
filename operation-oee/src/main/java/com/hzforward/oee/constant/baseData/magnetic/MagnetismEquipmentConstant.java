package com.hzforward.oee.constant.baseData.magnetic;

/**
 * 磁材设备
 */
public class MagnetismEquipmentConstant {


//
//    /**
//     * 气流磨
//     */
//    public static final ModbusReadConfig AirflowMillConstant = new ModbusReadConfig(){
//        {
//            setEquipDetails("************", "************", 502);
//
//            setPointDetailList("oxygenContentDetectionValue", 1, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 0, DataType.FOUR_BYTE_FLOAT);
//            setPointDetailList("sortingWheelSpeed", 2, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2, DataType.FOUR_BYTE_FLOAT);
//            setPointDetailList("grindingChamberWeight", 3, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 4, DataType.TWO_BYTE_INT_SIGNED);
//            setPointDetailList("grindingChamberPressure", 4, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 5, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("rindingPressure", 5, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 7, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("prePressureDifferenceOfCompressor", 6, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 9, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("compressorOutletWaterTemperature", 7, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 11, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("filterPressureDifference", 8, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 13, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("temperatureAtTheOutletOfTheRefrigeratedDryer", 9, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 15, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("highPressureTankPressure", 10, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 17, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("lowPressureTankPressure", 11, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 19, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("shaftCleaningPressure", 12, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 21, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("diskCleaningPressure", 13, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 23, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("highPressureAlarmInGrindingChamber", 14, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 25, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("lowPressureAlarmForNitrogenSource", 15, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 27, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("axisCleaningPressureLowAlarm", 16, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 29, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("highOxygenContentAlarm", 17, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 31, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("machineOverloadAlarm", 18, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 33, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("softStartFault", 19, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 35, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("lowPressureAlarmForDiskCleaning", 20, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 37, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("grindingRoomTemperatureAlarm", 21, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 39, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("coldDryerOutletTemperatureAlarm", 22, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 41, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("preFilterAlarm", 23, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 43, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("theFireDamperDidNotOpenAndTriggeredAnAlarm", 24, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 45, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("highPressureTankOutletValveNotOpenAlarm", 25, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 47, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("highPressureTankPressureAlarm", 26, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 49, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("feedingMotorAlarm", 27, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 51, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("theSortingMachineDidNotTurnOnTheAlarm", 28, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 53, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("lowAirPressureAlarm", 29, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 55, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("lowPressureTankOutletValveNotOpenAlarm", 30, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 57, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("filterPressureDifferentialHighAlarm", 31, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 59, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("sortingFanOverCurrent", 32, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 61, DataType.FOUR_BYTE_INT_SIGNED);
//            setPointDetailList("spiralFanOverCurrent", 33, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 63, DataType.FOUR_BYTE_INT_SIGNED);
//        }
//    };



}
