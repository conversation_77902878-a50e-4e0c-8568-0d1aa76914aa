package com.hzforward.oee.constant.baseData.cable;

import com.hzforward.oee.config.ModbusReadConfig;
import com.serotonin.modbus4j.code.DataType;

public class HighSpeedTwistingConstant {
    /**
     * 高速对绞机
     */
    public static final ModbusReadConfig HIGH_SPEED_TWISTING_CONFIG = new ModbusReadConfig() {
        {
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);

            setPointDetailList(1, 1, ModbusReadConfig.READ_COIL_STATUS, 0, "isWork");
            setPointDetailList(2, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.BINARY, 0, 0, "layDirection");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 100, "mainEngineSpeed");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 1250, "currentLineSpeed");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 1010, "setTwistDistance");
            setPointDetailList(6, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 600, "currentMeter");
            setPointDetailList(7, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 510, "setCollectionMeter");
            setPointDetailList(8, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 558, "currentCollectMeter");
            setPointDetailList(9, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 500, "fullPlateTension");
            setPointDetailList(10, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 556, "currentTension");
            setPointDetailList(11, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 502, "emptyPlateTension");
            setPointDetailList(12, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 516, "brakeTension");
            setPointDetailList(13, 1, ModbusReadConfig.READ_COIL_STATUS, 121 , "isBrokenStrip");
            setPointDetailList(14, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.BINARY, 400, 13,"inverterFault");
            setPointDetailList(15, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.BINARY, 400, 9, "externalDisconnection");
            setPointDetailList(16, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.BINARY, 400, 12, "isDoorClosed");
            setPointDetailList(17, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.BINARY, 400, 14, "isLiftingInPlace");
            setPointDetailList(18, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.BINARY, 399, 0, "meterArrived");
            setPointDetailList(19, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.BINARY, 399, 1, "isEmergencyStopReset");
            setPointDetailList(20, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.BINARY, 400, 10, "internalDisconnection");

        }
    };
}
