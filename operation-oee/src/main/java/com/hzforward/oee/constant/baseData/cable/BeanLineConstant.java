package com.hzforward.oee.constant.baseData.cable;

import com.hzforward.oee.config.ModbusReadConfig;
import com.serotonin.modbus4j.code.DataType;

public class BeanLineConstant {

    /**
     * 束丝机
     */
    public static final ModbusReadConfig BEAN_LINE_CONFIG = new ModbusReadConfig() {
        {
            setEquipDetails("*********", "************", 8000);
            setEquipDetails("*********", "************", 8000);
            setEquipDetails("*********", "************", 8000);
            setEquipDetails("*********", "************", 8000);
            setEquipDetails("*********", "************", 8000);
            setEquipDetails("*********", "************", 8000);

            setPointDetailList(0, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 0);
            setPointDetailList(1, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 1);
            setPointDetailList(2, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 2);
            setPointDetailList(3, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 3);
            setPointDetailList(4, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 4);
            setPointDetailList(5, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 5);
            setPointDetailList(6, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 6);
            setPointDetailList(7, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 7);
            setPointDetailList(8, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 8);
            setPointDetailList(9, 1, ModbusReadConfig.READ_INPUT_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 9);
        }
    };

}
