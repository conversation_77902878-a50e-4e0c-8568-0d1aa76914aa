package com.hzforward.oee.constant.baseData.cable;

import com.hzforward.oee.config.ModbusReadConfig;
import com.serotonin.modbus4j.code.DataType;

public class SheathExtruderConstant {

    /**
     * 束丝机
     */
    public static final ModbusReadConfig SHEATH_EXTRUDER_CONFIG = new ModbusReadConfig() {
        {
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);

            setPointDetailList(1,  1,  ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 12, "hostSpeedReality");
            setPointDetailList(2, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 14, "hostElectricCurrentReality");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 44, "hostSpeedSet");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 263, "lineSpeedReality");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 291, "lineSpeedSet");
//            setPointDetailList(6, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 0, "tractionElectricCurrentReality");
            setPointDetailList(7, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 512, "metersReality");

            setPointDetailList(8, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 805, "bodyTemperatureOne");
            setPointDetailList(9, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 806, "bodyTemperatureTwo");
            setPointDetailList(10, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 807, "bodyTemperatureThree");
            setPointDetailList(11, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 808, "bodyTemperatureFour");
            setPointDetailList(12, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 809, "bodyTemperatureFive");
            setPointDetailList(13, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 810, "bodyTemperatureSix");
            setPointDetailList(14, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 811, "flangeTemperature");
            setPointDetailList(15, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 812, "headTemperatureOne");
            setPointDetailList(16, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 813, "headTemperatureTwo");
            setPointDetailList(17, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 814, "thermostaticWaterTankTemperature");
            setPointDetailList(18, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 817, "setBodyTemperatureOne");
            setPointDetailList(19, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 818, "setBodyTemperatureTwo");
            setPointDetailList(20, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 819, "setBodyTemperatureThree");
            setPointDetailList(21, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 820, "setBodyTemperatureFour");
            setPointDetailList(22, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 821, "setBodyTemperatureFive");
            setPointDetailList(23, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 822, "setBodyTemperatureSix");
            setPointDetailList(24, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 823, "setFlangeTemperature");
            setPointDetailList(25, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 824, "setHeadTemperatureOne");
            setPointDetailList(26, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 825, "setHeadTemperatureTwo");
            setPointDetailList(27, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 826, "setThermostaticWaterTankTemperature");

            setPointDetailList(28, 1, ModbusReadConfig.READ_COIL_STATUS, 24, "errorSignal");
            setPointDetailList(29, 1, ModbusReadConfig.READ_COIL_STATUS, 48, "runSignal");
            setPointDetailList(30, 1, ModbusReadConfig.READ_INPUT_REGISTERS, 56, "hostDriveError");
            setPointDetailList(31, 1, ModbusReadConfig.READ_INPUT_REGISTERS, 16, "tractionDriveError");
            setPointDetailList(32, 1, ModbusReadConfig.READ_INPUT_REGISTERS,  48, "sparkError");

        }
    };

}
