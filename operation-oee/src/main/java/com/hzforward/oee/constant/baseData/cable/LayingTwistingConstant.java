package com.hzforward.oee.constant.baseData.cable;

import com.hzforward.oee.config.ModbusReadConfig;
import com.serotonin.modbus4j.code.DataType;

public class LayingTwistingConstant {
    /**
     * 悬臂单扭成榄机
     */
    public static final ModbusReadConfig LAYING_TWISTING_CONFIG = new ModbusReadConfig() {
        {
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);
            setEquipDetails("*********", "************", 502);

            setPointDetailList(1,  1,  ModbusReadConfig.READ_COIL_STATUS, 40, "isWork");
            setPointDetailList(2, 1, ModbusReadConfig.READ_COIL_STATUS, 113, "layDirection");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 107, "mainEngineSpeed");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 46, "currentLineSpeed");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 301, "setTwistDistance");
            setPointDetailList(6, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 306, "currentTwistDistance");
            setPointDetailList(7, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 273, "currentMeter");
            setPointDetailList(8, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 218, "setCollectionMeter");
            setPointDetailList(9, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 926, "setTorsionRate");
            setPointDetailList(10, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 976, "currentTorsionRate");
            setPointDetailList(11, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 351, "setSpacing");
            setPointDetailList(12, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 213, "setPreStopMeter");
            setPointDetailList(13, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 252, "meterDiameter");
            setPointDetailList(14, 1, ModbusReadConfig.READ_COIL_STATUS, 21, "wrappingStart");
            setPointDetailList(15, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 591, "wrappingTension");
            setPointDetailList(16, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 551, "wrappingMeter");
            setPointDetailList(17, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 532, "wrappingStartTension");
            setPointDetailList(18, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 537, "wrappingStopTension");
            setPointDetailList(19, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 542, "wrappingBrakeTension");
            setPointDetailList(20, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_INT_SIGNED, 516, "wrappingTotalMeter");
            setPointDetailList(21, 1, ModbusReadConfig.READ_COIL_STATUS, 1, "isBrokenStrip");
            setPointDetailList(22, 1, ModbusReadConfig.READ_COIL_STATUS, 163, "rankingOffside");
            setPointDetailList(23, 1, ModbusReadConfig.READ_COIL_STATUS, 160, "inverterFault");
            setPointDetailList(24, 1, ModbusReadConfig.READ_COIL_STATUS, 169, "externalDisconnection");
            setPointDetailList(25, 1, ModbusReadConfig.READ_COIL_STATUS, 165, "isDoorClosed");
            setPointDetailList(26, 1, ModbusReadConfig.READ_COIL_STATUS, 164, "isLiftingInPlace");
            setPointDetailList(27, 1, ModbusReadConfig.READ_COIL_STATUS, 167, "meterArrived");
            setPointDetailList(28, 1, ModbusReadConfig.READ_COIL_STATUS, 175, "meterDeceleration");
            setPointDetailList(29, 1, ModbusReadConfig.READ_COIL_STATUS, 166, "isManualBrakeReleased");
            setPointDetailList(30, 1, ModbusReadConfig.READ_COIL_STATUS, 161, "isEmergencyStopReset");
        }
    };
}
