package com.hzforward.oee.constant.baseData.host;

/**
 * 浸漆炉
 */
public class ImmersionFurnaceConstant {


//    public static final ModbusReadConfig IMMERSION_FURNACE_ONE = new ModbusReadConfig(){
//        {
//            setEquipDetails("***********", "***********", 502);
//
//            setPointDetailList("temperatureOne", 1, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2031, DataType.TWO_BYTE_INT_SIGNED);
//            setPointDetailList("temperatureTwo", 2, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2032, DataType.TWO_BYTE_INT_SIGNED);
//            setPointDetailList("temperatureThree", 3, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2033, DataType.TWO_BYTE_INT_SIGNED);
//            setPointDetailList("temperatureFour", 4, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2034, DataType.TWO_BYTE_INT_SIGNED);
//            setPointDetailList("temperatureFive", 5, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2035, DataType.TWO_BYTE_INT_SIGNED);
//        }
//    };
//
//    public static final ModbusReadConfig IMMERSION_FURNACE_TWO = new ModbusReadConfig(){
//        {
//            setEquipDetails("***********", "***********", 502);
//
//            setPointDetailList("temperatureOne", 1, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2031, DataType.TWO_BYTE_INT_UNSIGNED);
//            setPointDetailList("temperatureTwo", 2, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2032, DataType.TWO_BYTE_INT_UNSIGNED);
//            setPointDetailList("temperatureThree", 3, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2033, DataType.TWO_BYTE_INT_UNSIGNED);
//            setPointDetailList("temperatureFour", 4, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2034, DataType.TWO_BYTE_INT_UNSIGNED);
//            setPointDetailList("temperatureFive", 5, ModbusReadConfig.READ_HOLDING_REGISTERS, 1, 2035, DataType.TWO_BYTE_INT_UNSIGNED);
//        }
//    };
}
