package com.hzforward.oee.constant.baseData.host;

import com.hzforward.oee.config.ModbusReadConfig;
import com.serotonin.modbus4j.code.DataType;

/**
 * 清洗线
 */
public class CleanLineConstant {

    // 基座线
    public static final ModbusReadConfig BASE_LINE = new ModbusReadConfig(){
        {
            setEquipDetails("************", "************", 502);

            setPointDetailList(1, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 0, "vd50");
            setPointDetailList(2, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 2, "vd54");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 4, "vd58");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 6, "vd62");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 8, "vd66");
            setPointDetailList(6, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 10, "vd70");
            setPointDetailList(7, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 12, "vd74");
            setPointDetailList(8, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 14, "vd78");
            setPointDetailList(9, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 16, "vd82");
            setPointDetailList(10, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 18, "vd86");
            setPointDetailList(11, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 20, "vd90");
            setPointDetailList(12, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 22, "vd94");
            setPointDetailList(13, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 24, "vd98");
            setPointDetailList(14, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 26, "vd102");
            setPointDetailList(15, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 28, "vd106");
            setPointDetailList(16, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 30, "vd110");
            setPointDetailList(17, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 32, "vd114");
            setPointDetailList(18, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 34, "vd118");
            setPointDetailList(19, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 36, "vd122");
            setPointDetailList(20, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 38, "vd126");
            setPointDetailList(21, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 40, "vd130");
            setPointDetailList(22, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 42, "vd134");
            setPointDetailList(23, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 44, "vd138");
            setPointDetailList(24, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 46, "vd142");
            setPointDetailList(25, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 48, "vd146");
            setPointDetailList(26, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 50, "vd150");
            setPointDetailList(27, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 52, "vd154");
            setPointDetailList(28, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 54, "vd158");
            setPointDetailList(29, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 56, "vd162");
            setPointDetailList(30, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 58, "vd166");
            setPointDetailList(31, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 60, "vd170");
            setPointDetailList(32, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 62, "vd174");
            setPointDetailList(33, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 64, "vd178");
            setPointDetailList(34, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 66, "vd182");
            setPointDetailList(35, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 68, "vd186");
            setPointDetailList(36, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 70, "vd700");
            setPointDetailList(37, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 72, "vd704");
            setPointDetailList(38, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 74, "vd708");
            setPointDetailList(39, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 76, "vd712");
            setPointDetailList(40, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 78, "vd716");
            setPointDetailList(41, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 80, "vd720");
            setPointDetailList(42, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 82, "vd724");
            setPointDetailList(43, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 84, "vd728");
            setPointDetailList(44, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 86, "vd732");
            setPointDetailList(45, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 88, "vd736");
            setPointDetailList(46, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 90, "vd740");
            setPointDetailList(47, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 92, "vd744");
            setPointDetailList(48, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 94, "vd748");
            setPointDetailList(49, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 96, "vd752");
            setPointDetailList(50, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 98, "vd756");
            setPointDetailList(51, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 100, "vw0");
            setPointDetailList(52, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 101, "vw2");
            setPointDetailList(53, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 102, "vw6");
            setPointDetailList(54, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 103, "vw10");
            setPointDetailList(55, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 104, "vw32");
            setPointDetailList(56, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 105, "vw36");
            setPointDetailList(57, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 106, "vw38");
            setPointDetailList(58, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 107, "vw40");
            setPointDetailList(59, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 108, "vw1010");
            setPointDetailList(60, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 109, "vw1012");
            setPointDetailList(61, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 110, "vw1014");
        }
    };

    // 转子线
    public static final ModbusReadConfig ROTOR_LINE = new ModbusReadConfig(){
        {
            setEquipDetails("************", "************", 502);

            setPointDetailList(1, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 200, "vd50");
            setPointDetailList(2, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 202, "vd54");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 204, "vd58");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 206, "vd62");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 208, "vd66");
            setPointDetailList(6, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 210, "vd70");
            setPointDetailList(7, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 212, "vd74");
            setPointDetailList(8, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 214, "vd78");
            setPointDetailList(9, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 216, "vd82");
            setPointDetailList(10, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 218, "vd86");
            setPointDetailList(11, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 220, "vd90");
            setPointDetailList(12, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 222, "vd94");
            setPointDetailList(13, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 224, "vd98");
            setPointDetailList(14, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 226, "vd102");
            setPointDetailList(15, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 228, "vd106");
            setPointDetailList(16, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 230, "vd110");
            setPointDetailList(17, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 232, "vd114");
            setPointDetailList(18, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 234, "vd118");
            setPointDetailList(19, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 236, "vd122");
            setPointDetailList(20, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 238, "vd126");
            setPointDetailList(21, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 240, "vd130");
            setPointDetailList(22, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 242, "vd134");
            setPointDetailList(23, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 244, "vd138");
            setPointDetailList(24, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 246, "vd142");
            setPointDetailList(25, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 248, "vd146");
            setPointDetailList(26, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 250, "vd150");
            setPointDetailList(27, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 252, "vd154");
            setPointDetailList(28, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 254, "vd158");
            setPointDetailList(29, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 256, "vd162");
            setPointDetailList(30, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 258, "vd166");
            setPointDetailList(31, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 260, "vd820");
            setPointDetailList(32, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 262, "vd824");
            setPointDetailList(33, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 264, "vd828");
            setPointDetailList(34, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 266, "vd832");
            setPointDetailList(35, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 268, "vd836");
            setPointDetailList(36, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 270, "vd840");
            setPointDetailList(37, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 272, "vd844");
            setPointDetailList(38, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 274, "vd848");
            setPointDetailList(39, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 276, "vd852");
            setPointDetailList(40, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 278, "vd856");
            setPointDetailList(41, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 280, "vd860");
            setPointDetailList(42, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 282, "vd864");
            setPointDetailList(43, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 284, "vd868");
            setPointDetailList(44, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 286, "vd872");
            setPointDetailList(45, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 288, "vd876");
            setPointDetailList(46, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 290, "vd880");
            setPointDetailList(47, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 292, "vd884");
            setPointDetailList(48, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 294, "vd888");
            setPointDetailList(49, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 296, "vd892");
            setPointDetailList(50, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 298, "vd896");
        }
    };

    // 柔性线
    public static final ModbusReadConfig FLEXIBLE_LINE = new ModbusReadConfig(){
        {
            setEquipDetails("************", "************", 502);

            setPointDetailList(1, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 400, "vd50");
            setPointDetailList(2, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 402, "vd54");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 404, "vd58");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 406, "vd62");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 408, "vd66");
            setPointDetailList(6, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 410, "vd70");
            setPointDetailList(7, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 412, "vd74");
            setPointDetailList(8, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 414, "vd78");
            setPointDetailList(9, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 416, "vd82");
            setPointDetailList(10, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 418, "vd86");
            setPointDetailList(11, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 420, "vd90");
            setPointDetailList(12, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 422, "vd94");
            setPointDetailList(13, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 424, "vd98");
            setPointDetailList(14, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 426, "vd102");
            setPointDetailList(15, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 428, "vd106");
            setPointDetailList(16, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 430, "vd110");
            setPointDetailList(17, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 432, "vd114");
            setPointDetailList(18, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 434, "vd118");
            setPointDetailList(19, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 436, "vd122");
            setPointDetailList(20, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 438, "vd126");
            setPointDetailList(21, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 440, "vd130");
            setPointDetailList(22, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 442, "vd134");
            setPointDetailList(23, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 444, "vd138");
            setPointDetailList(24, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 446, "vd142");
            setPointDetailList(25, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 448, "vd146");
            setPointDetailList(26, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 450, "vd150");
            setPointDetailList(27, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 452, "vd154");
            setPointDetailList(28, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 454, "vd158");
            setPointDetailList(29, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 456, "vd162");
            setPointDetailList(30, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 458, "vd166");
            setPointDetailList(31, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 460, "vd170");
            setPointDetailList(32, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 462, "vd174");
            setPointDetailList(33, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 464, "vd178");
            setPointDetailList(34, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 466, "vd182");
            setPointDetailList(35, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 468, "vd210");
            setPointDetailList(36, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 470, "vd214");
            setPointDetailList(37, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 472, "vd218");
            setPointDetailList(38, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 474, "vd222");
            setPointDetailList(39, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 476, "vd226");
            setPointDetailList(40, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 478, "vd230");
            setPointDetailList(41, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 480, "vd234");
            setPointDetailList(42, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 482, "vd238");
            setPointDetailList(43, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 484, "vd242");
            setPointDetailList(44, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 486, "vd246");
            setPointDetailList(45, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 488, "vd250");
            setPointDetailList(46, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 490, "vd254");
            setPointDetailList(47, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 492, "vd258");
            setPointDetailList(48, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 494, "vd262");
            setPointDetailList(49, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 496, "vd266");
        }
    };

    // 辊道铁链
    public static final ModbusReadConfig ROLLER_LINE = new ModbusReadConfig(){
        {
            setEquipDetails("************", "************", 502);

            setPointDetailList(1, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 600, "vd1124");
            setPointDetailList(2, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 602, "vd1128");
            setPointDetailList(3, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 604, "vd1132");
            setPointDetailList(4, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 606, "vd1136");
            setPointDetailList(5, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 608, "vd1140");
            setPointDetailList(6, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 610, "vd1144");
            setPointDetailList(7, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 612, "vd1148");
            setPointDetailList(8, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 614, "vd1152");
            setPointDetailList(9, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 616, "vd1156");
            setPointDetailList(10, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 618, "vd1160");
            setPointDetailList(11, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 620, "vd1164");
            setPointDetailList(12, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 622, "vd1168");
            setPointDetailList(13, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 624, "vd1172");
            setPointDetailList(14, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 626, "vd1176");
            setPointDetailList(15, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 628, "vd1180");
            setPointDetailList(16, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 630, "vd1184");
            setPointDetailList(17, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 632, "vd1188");
            setPointDetailList(18, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 634, "vd1192");
            setPointDetailList(19, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 636, "vd1196");
            setPointDetailList(20, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 638, "vd1200");
            setPointDetailList(21, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 640, "vd1204");
            setPointDetailList(22, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 642, "vd1208");
            setPointDetailList(23, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 644, "vd1212");
            setPointDetailList(24, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 646, "vd1216");
            setPointDetailList(25, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 648, "vd1220");
            setPointDetailList(26, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 650, "vd1224");
            setPointDetailList(27, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 652, "vd1228");
            setPointDetailList(28, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 654, "vd1232");
            setPointDetailList(29, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 656, "vd1236");
            setPointDetailList(30, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 658, "vd1240");
            setPointDetailList(31, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 660, "vd1244");
            setPointDetailList(32, 1, ModbusReadConfig.READ_COIL_STATUS, 0, "v12480");
            setPointDetailList(33, 1, ModbusReadConfig.READ_COIL_STATUS, 1, "v12481");
            setPointDetailList(34, 1, ModbusReadConfig.READ_COIL_STATUS, 2, "v12482");
            setPointDetailList(35, 1, ModbusReadConfig.READ_COIL_STATUS, 3, "v12483");
            setPointDetailList(36, 1, ModbusReadConfig.READ_COIL_STATUS, 4, "v12484");
            setPointDetailList(37, 1, ModbusReadConfig.READ_COIL_STATUS, 5, "v12485");
            setPointDetailList(38, 1, ModbusReadConfig.READ_COIL_STATUS, 6, "v12486");
            setPointDetailList(39, 1, ModbusReadConfig.READ_COIL_STATUS, 7, "v12487");
            setPointDetailList(40, 1, ModbusReadConfig.READ_COIL_STATUS, 8, "v12490");
            setPointDetailList(41, 1, ModbusReadConfig.READ_COIL_STATUS, 9, "v12491");
            setPointDetailList(42, 1, ModbusReadConfig.READ_COIL_STATUS, 10, "v12492");
            setPointDetailList(43, 1, ModbusReadConfig.READ_COIL_STATUS, 11, "v12493");
            setPointDetailList(44, 1, ModbusReadConfig.READ_COIL_STATUS, 12, "v12494");
            setPointDetailList(45, 1, ModbusReadConfig.READ_COIL_STATUS, 13, "v12495");
            setPointDetailList(46, 1, ModbusReadConfig.READ_COIL_STATUS, 14, "v12496");
            setPointDetailList(47, 1, ModbusReadConfig.READ_COIL_STATUS, 15, "v12497");
            setPointDetailList(48, 1, ModbusReadConfig.READ_COIL_STATUS, 16, "v12500");
            setPointDetailList(49, 1, ModbusReadConfig.READ_COIL_STATUS, 17, "v12501");
            setPointDetailList(50, 1, ModbusReadConfig.READ_COIL_STATUS, 18, "v12502");
            setPointDetailList(51, 1, ModbusReadConfig.READ_COIL_STATUS, 19, "v12503");
            setPointDetailList(52, 1, ModbusReadConfig.READ_COIL_STATUS, 20, "v12510");
            setPointDetailList(53, 1, ModbusReadConfig.READ_COIL_STATUS, 21, "v12511");
            setPointDetailList(54, 1, ModbusReadConfig.READ_COIL_STATUS, 22, "v12512");
            setPointDetailList(55, 1, ModbusReadConfig.READ_COIL_STATUS, 23, "v12513");
            setPointDetailList(56, 1, ModbusReadConfig.READ_COIL_STATUS, 24, "v12514");
            setPointDetailList(57, 1, ModbusReadConfig.READ_COIL_STATUS, 25, "v12515");
            setPointDetailList(58, 1, ModbusReadConfig.READ_COIL_STATUS, 26, "v12516");
            setPointDetailList(59, 1, ModbusReadConfig.READ_COIL_STATUS, 27, "v12517");
            setPointDetailList(60, 1, ModbusReadConfig.READ_COIL_STATUS, 28, "v12520");
            setPointDetailList(61, 1, ModbusReadConfig.READ_COIL_STATUS, 29, "v12521");
            setPointDetailList(62, 1, ModbusReadConfig.READ_COIL_STATUS, 30, "v12522");
            setPointDetailList(63, 1, ModbusReadConfig.READ_COIL_STATUS, 31, "v12523");
            setPointDetailList(64, 1, ModbusReadConfig.READ_COIL_STATUS, 32, "v12524");
            setPointDetailList(65, 1, ModbusReadConfig.READ_COIL_STATUS, 33, "v12525");
            setPointDetailList(66, 1, ModbusReadConfig.READ_COIL_STATUS, 34, "v12526");
            setPointDetailList(67, 1, ModbusReadConfig.READ_COIL_STATUS, 35, "v12527");
            setPointDetailList(68, 1, ModbusReadConfig.READ_COIL_STATUS, 36, "v12530");
            setPointDetailList(69, 1, ModbusReadConfig.READ_COIL_STATUS, 37, "v12531");
            setPointDetailList(70, 1, ModbusReadConfig.READ_COIL_STATUS, 38, "v12532");
            setPointDetailList(71, 1, ModbusReadConfig.READ_COIL_STATUS, 39, "v12533");
            setPointDetailList(72, 1, ModbusReadConfig.READ_COIL_STATUS, 40, "v12540");
            setPointDetailList(73, 1, ModbusReadConfig.READ_COIL_STATUS, 41, "v12541");
            setPointDetailList(74, 1, ModbusReadConfig.READ_COIL_STATUS, 42, "v12542");
            setPointDetailList(75, 1, ModbusReadConfig.READ_COIL_STATUS, 43, "v12543");
            setPointDetailList(76, 1, ModbusReadConfig.READ_COIL_STATUS, 44, "v12544");
            setPointDetailList(77, 1, ModbusReadConfig.READ_COIL_STATUS, 45, "v12545");
            setPointDetailList(78, 1, ModbusReadConfig.READ_COIL_STATUS, 46, "v12546");
            setPointDetailList(79, 1, ModbusReadConfig.READ_COIL_STATUS, 47, "v12547");
            setPointDetailList(80, 1, ModbusReadConfig.READ_COIL_STATUS, 48, "v12550");
            setPointDetailList(81, 1, ModbusReadConfig.READ_COIL_STATUS, 49, "v12551");
            setPointDetailList(82, 1, ModbusReadConfig.READ_COIL_STATUS, 50, "v12552");
            setPointDetailList(83, 1, ModbusReadConfig.READ_COIL_STATUS, 51, "v12553");
            setPointDetailList(84, 1, ModbusReadConfig.READ_COIL_STATUS, 52, "v12554");
            setPointDetailList(85, 1, ModbusReadConfig.READ_COIL_STATUS, 53, "v12555");
            setPointDetailList(86, 1, ModbusReadConfig.READ_COIL_STATUS, 54, "v12556");
            setPointDetailList(87, 1, ModbusReadConfig.READ_COIL_STATUS, 55, "v12557");
            setPointDetailList(88, 1, ModbusReadConfig.READ_COIL_STATUS, 56, "v12560");
            setPointDetailList(89, 1, ModbusReadConfig.READ_COIL_STATUS, 57, "v12561");
            setPointDetailList(90, 1, ModbusReadConfig.READ_COIL_STATUS, 58, "v12562");
            setPointDetailList(91, 1, ModbusReadConfig.READ_COIL_STATUS, 59, "v12563");
            setPointDetailList(92, 1, ModbusReadConfig.READ_COIL_STATUS, 60, "v12564");
            setPointDetailList(93, 1, ModbusReadConfig.READ_COIL_STATUS, 61, "v12565");
        }
    };


    // 大件清洗
    public static final ModbusReadConfig LARGE_LINE = new ModbusReadConfig(){
        {
            setEquipDetails("************", "************", 502);

            setPointDetailList(1, 1, ModbusReadConfig.READ_COIL_STATUS, 100, "m07");
            setPointDetailList(2, 1, ModbusReadConfig.READ_COIL_STATUS, 101, "m10");
            setPointDetailList(3, 1, ModbusReadConfig.READ_COIL_STATUS, 102, "m11");
            setPointDetailList(4, 1, ModbusReadConfig.READ_COIL_STATUS, 103, "m12");
            setPointDetailList(5, 1, ModbusReadConfig.READ_COIL_STATUS, 104, "m13");
            setPointDetailList(6, 1, ModbusReadConfig.READ_COIL_STATUS, 105, "m14");
            setPointDetailList(7, 1, ModbusReadConfig.READ_COIL_STATUS, 106, "m15");
            setPointDetailList(8, 1, ModbusReadConfig.READ_COIL_STATUS, 107, "m16");
            setPointDetailList(9, 1, ModbusReadConfig.READ_COIL_STATUS, 108, "m17");
            setPointDetailList(10, 1, ModbusReadConfig.READ_COIL_STATUS, 109, "m20");
            setPointDetailList(11, 1, ModbusReadConfig.READ_COIL_STATUS, 110, "m21");
            setPointDetailList(12, 1, ModbusReadConfig.READ_COIL_STATUS, 111, "m22");
            setPointDetailList(13, 1, ModbusReadConfig.READ_COIL_STATUS, 112, "m23");
            setPointDetailList(14, 1, ModbusReadConfig.READ_COIL_STATUS, 113, "m24");
            setPointDetailList(15, 1, ModbusReadConfig.READ_COIL_STATUS, 114, "m25");
            setPointDetailList(16, 1, ModbusReadConfig.READ_COIL_STATUS, 115, "m26");
            setPointDetailList(17, 1, ModbusReadConfig.READ_COIL_STATUS, 116, "m27");
            setPointDetailList(18, 1, ModbusReadConfig.READ_COIL_STATUS, 117, "m30");
            setPointDetailList(19, 1, ModbusReadConfig.READ_COIL_STATUS, 118, "m31");
            setPointDetailList(20, 1, ModbusReadConfig.READ_COIL_STATUS, 119, "m32");
            setPointDetailList(21, 1, ModbusReadConfig.READ_COIL_STATUS, 120, "m45");
            setPointDetailList(22, 1, ModbusReadConfig.READ_COIL_STATUS, 121, "m46");
            setPointDetailList(23, 1, ModbusReadConfig.READ_COIL_STATUS, 122, "m47");
            setPointDetailList(24, 1, ModbusReadConfig.READ_COIL_STATUS, 123, "m50");
            setPointDetailList(25, 1, ModbusReadConfig.READ_COIL_STATUS, 124, "m51");
            setPointDetailList(26, 1, ModbusReadConfig.READ_COIL_STATUS, 125, "m53");
            setPointDetailList(27, 1, ModbusReadConfig.READ_COIL_STATUS, 126, "m54");
            setPointDetailList(28, 1, ModbusReadConfig.READ_COIL_STATUS, 127, "m55");
            setPointDetailList(29, 1, ModbusReadConfig.READ_COIL_STATUS, 128, "m62");
            setPointDetailList(30, 1, ModbusReadConfig.READ_COIL_STATUS, 129, "m63");
            setPointDetailList(31, 1, ModbusReadConfig.READ_COIL_STATUS, 130, "m64");
            setPointDetailList(32, 1, ModbusReadConfig.READ_COIL_STATUS, 131, "m65");
            setPointDetailList(33, 1, ModbusReadConfig.READ_COIL_STATUS, 132, "m66");
            setPointDetailList(34, 1, ModbusReadConfig.READ_COIL_STATUS, 133, "m67");
            setPointDetailList(35, 1, ModbusReadConfig.READ_COIL_STATUS, 134, "m97");
            setPointDetailList(36, 1, ModbusReadConfig.READ_COIL_STATUS, 135, "m104");
            setPointDetailList(37, 1, ModbusReadConfig.READ_COIL_STATUS, 136, "m105");
            setPointDetailList(38, 1, ModbusReadConfig.READ_COIL_STATUS, 137, "m140");
            setPointDetailList(39, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 800, "vd0");
            setPointDetailList(40, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 802, "vd4");
            setPointDetailList(41, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 804, "vd8");
            setPointDetailList(42, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 806, "vd12");
            setPointDetailList(43, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 808, "vd16");
            setPointDetailList(44, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 810, "vd20");
            setPointDetailList(45, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 812, "vd24");
            setPointDetailList(46, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 814, "vd28");
            setPointDetailList(47, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.FOUR_BYTE_FLOAT, 816, "vd90");
            setPointDetailList(48, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 818, "vw38");
            setPointDetailList(49, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 819, "vw40");
            setPointDetailList(50, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 820, "vw50");
            setPointDetailList(51, 1, ModbusReadConfig.READ_HOLDING_REGISTERS, DataType.TWO_BYTE_INT_SIGNED, 821, "vw52");
        }
    };

}
