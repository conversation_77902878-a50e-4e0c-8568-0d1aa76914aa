<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>operation</artifactId>
        <groupId>com.hzforward</groupId>
        <version>3.8.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>operation-oee</artifactId>

    <description>
        MES相关
    </description>

    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>com.infiniteautomation</groupId>
            <artifactId>modbus4j</artifactId>
        </dependency>

        <dependency>
            <groupId>net.sf.ucanaccess</groupId>
            <artifactId>ucanaccess</artifactId>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hzforward</groupId>
            <artifactId>operation-equipmentManage</artifactId>
        </dependency>


        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.7.2</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.7.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>


    </dependencies>
</project>