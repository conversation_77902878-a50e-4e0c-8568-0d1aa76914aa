<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.equipmentManage.mapper.MaintainReportMapper">

    <select id="getCreateMaintainPlanEquipNoList" resultType="java.lang.String">
        select distinct emi.equip_no from equipment_maintain_info emi
            left join equipment_ledger_info ei on emi.equip_no = ei.equip_no
        where ei.del_flag = '0' and emi.del_flag = '0' and ei.current_status = '0'
        and emi.maintain_month = #{maintainMonth}
    </select>
</mapper>