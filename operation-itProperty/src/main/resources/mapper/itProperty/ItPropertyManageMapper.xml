<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.itProperty.mapper.ItPropertyManageMapper">
    
    <resultMap type="ItPropertyManage" id="ItPropertyManageResult">
        <result property="propertyId"    column="property_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="ownerDept"    column="owner_dept"    />
        <result property="borrowState"    column="borrow_state"    />
        <result property="propertyState"    column="property_state"    />
        <result property="propertyType"    column="property_type"    />
        <result property="propertyNo"    column="property_no"    />
        <result property="sapSystemNo"    column="sap_system_no"    />
        <result property="propertySpc"    column="property_spc"    />
        <result property="buyTime"    column="buy_time"    />
        <result property="propertySystemNo"    column="property_system_no"    />
        <result property="quickServerNo"    column="quick_server_no"    />
        <result property="mouseName"    column="mouse_name"    />
        <result property="monitorName"    column="monitor_name"    />
        <result property="keyboardName"    column="keyboard_name"    />
        <result property="propertyRemark"    column="property_remark"    />
        <result property="oldUser"    column="old_user"    />
        <result property="macAddress"    column="mac_address"    />
        <result property="windowsActivationCode"    column="windows_activation_code"    />
    </resultMap>

    <sql id="selectItPropertyManageVo">
        select property_id, del_flag, create_time, update_time, delete_time, create_by, update_by, delete_by, owner_name, owner_dept, borrow_state, property_state, property_type, property_no, sap_system_no, property_spc, buy_time, property_system_no, quick_server_no, mouse_name, monitor_name, keyboard_name, property_remark, old_user, mac_address, windows_activation_code from it_property_manage
    </sql>

    <select id="selectItPropertyManageList" parameterType="ItPropertyManage" resultMap="ItPropertyManageResult">
        <include refid="selectItPropertyManageVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="ownerName != null  and ownerName != ''"> and owner_name like concat('%', #{ownerName}, '%')</if>
            <if test="ownerDept != null  and ownerDept != ''"> and owner_dept  like concat('%', #{ownerDept}, '%')</if>
            <if test="propertyState != null  and propertyState != ''"> and property_state = #{propertyState}</if>
            <if test="propertyType != null  and propertyType != ''"> and property_type = #{propertyType}</if>
            <if test="propertyNo != null  and propertyNo != ''"> and property_no like concat('%',  #{propertyNo}, '%')</if>
            <if test="sapSystemNo != null  and sapSystemNo != ''"> and sap_system_no like concat('%',  #{sapSystemNo}, '%')</if>
            <if test="propertySpc != null  and propertySpc != ''"> and property_spc like concat('%', #{propertySpc}, '%')</if>
            <if test="propertySystemNo != null  and propertySystemNo != ''"> and property_system_no like concat('%', #{propertySystemNo}, '%')</if>
            <if test="quickServerNo != null  and quickServerNo != ''"> and quick_server_no like concat('%', #{quickServerNo}, '%')</if>
        </where>
    </select>
    
    <select id="selectItPropertyManageByPropertyId" parameterType="Long" resultMap="ItPropertyManageResult">
        <include refid="selectItPropertyManageVo"/>
        where property_id = #{propertyId}
    </select>
        
    <insert id="insertItPropertyManage" parameterType="ItPropertyManage" useGeneratedKeys="true" keyProperty="propertyId">
        insert into it_property_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="ownerName != null">owner_name,</if>
            <if test="ownerDept != null">owner_dept,</if>
            <if test="borrowState != null and borrowState != ''">borrow_state,</if>
            <if test="propertyState != null and propertyState != ''">property_state,</if>
            <if test="propertyType != null">property_type,</if>
            <if test="propertyNo != null">property_no,</if>
            <if test="sapSystemNo != null">sap_system_no,</if>
            <if test="propertySpc != null">property_spc,</if>
            <if test="buyTime != null">buy_time,</if>
            <if test="propertySystemNo != null">property_system_no,</if>
            <if test="quickServerNo != null">quick_server_no,</if>
            <if test="mouseName != null">mouse_name,</if>
            <if test="monitorName != null">monitor_name,</if>
            <if test="keyboardName != null">keyboard_name,</if>
            <if test="propertyRemark != null">property_remark,</if>
            <if test="oldUser != null">old_user,</if>
            <if test="macAddress != null">mac_address,</if>
            <if test="windowsActivationCode != null">windows_activation_code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="ownerName != null">#{ownerName},</if>
            <if test="ownerDept != null">#{ownerDept},</if>
            <if test="borrowState != null and borrowState != ''">#{borrowState},</if>
            <if test="propertyState != null and propertyState != ''">#{propertyState},</if>
            <if test="propertyType != null">#{propertyType},</if>
            <if test="propertyNo != null">#{propertyNo},</if>
            <if test="sapSystemNo != null">#{sapSystemNo},</if>
            <if test="propertySpc != null">#{propertySpc},</if>
            <if test="buyTime != null">#{buyTime},</if>
            <if test="propertySystemNo != null">#{propertySystemNo},</if>
            <if test="quickServerNo != null">#{quickServerNo},</if>
            <if test="mouseName != null">#{mouseName},</if>
            <if test="monitorName != null">#{monitorName},</if>
            <if test="keyboardName != null">#{keyboardName},</if>
            <if test="propertyRemark != null">#{propertyRemark},</if>
            <if test="oldUser != null">#{oldUser},</if>
            <if test="macAddress != null">#{macAddress},</if>
            <if test="windowsActivationCode != null">#{windowsActivationCode},</if>
         </trim>
    </insert>

    <update id="updateItPropertyManage" parameterType="ItPropertyManage">
        update it_property_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="ownerName != null">owner_name = #{ownerName},</if>
            <if test="ownerDept != null">owner_dept = #{ownerDept},</if>
            <if test="borrowState != null and borrowState != ''">borrow_state = #{borrowState},</if>
            <if test="propertyState != null and propertyState != ''">property_state = #{propertyState},</if>
            <if test="propertyType != null">property_type = #{propertyType},</if>
            <if test="propertyNo != null">property_no = #{propertyNo},</if>
            <if test="sapSystemNo != null">sap_system_no = #{sapSystemNo},</if>
            <if test="propertySpc != null">property_spc = #{propertySpc},</if>
            <if test="buyTime != null">buy_time = #{buyTime},</if>
            <if test="propertySystemNo != null">property_system_no = #{propertySystemNo},</if>
            <if test="quickServerNo != null">quick_server_no = #{quickServerNo},</if>
            <if test="mouseName != null">mouse_name = #{mouseName},</if>
            <if test="monitorName != null">monitor_name = #{monitorName},</if>
            <if test="keyboardName != null">keyboard_name = #{keyboardName},</if>
            <if test="propertyRemark != null">property_remark = #{propertyRemark},</if>
            <if test="oldUser != null">old_user = #{oldUser},</if>
            <if test="macAddress != null">mac_address = #{macAddress},</if>
            <if test="windowsActivationCode != null">windows_activation_code = #{windowsActivationCode},</if>
        </trim>
        where property_id = #{propertyId}
    </update>

    <delete id="deleteItPropertyManageByPropertyId" parameterType="Long">
        update it_property_manage set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where property_id = #{propertyId}
    </delete>

    <delete id="deleteItPropertyManageByPropertyIds" parameterType="String">
        update it_property_manage set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where property_id in
        <foreach item="propertyId" collection="propertyIds" open="(" separator="," close=")">
            #{propertyId}
        </foreach>
    </delete>

</mapper>