<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.sync.mapper.SyncMeasuringToolTorsionMapper">
    
    <resultMap type="SyncMeasuringToolTorsion" id="SyncMeasuringToolTorsionResult">
        <result property="id"    column="id"    />
        <result property="seqId"    column="seq_id"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="value1"    column="value1"    />
        <result property="value2"    column="value2"    />
        <result property="value3"    column="value3"    />
        <result property="valueAvg"    column="value_avg"    />
        <result property="result"    column="result"    />
        <result property="opt"    column="opt"    />
        <result property="date"    column="date"    />
    </resultMap>

    <sql id="selectSyncMeasuringToolTorsionVo">
        select id, seq_id, goods_id, value1, value2, value3, value_avg, result, opt, date from sync_measuring_tool_torsion
    </sql>

    <select id="selectSyncMeasuringToolTorsionList" parameterType="SyncMeasuringToolTorsion" resultMap="SyncMeasuringToolTorsionResult">
        <include refid="selectSyncMeasuringToolTorsionVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="seqId != null  and seqId != ''"> and seq_id = #{seqId}</if>
            <if test="goodsId != null  and goodsId != ''"> and goods_id = #{goodsId}</if>
            <if test="value1 != null  and value1 != ''"> and value1 = #{value1}</if>
            <if test="value2 != null  and value2 != ''"> and value2 = #{value2}</if>
            <if test="value3 != null  and value3 != ''"> and value3 = #{value3}</if>
            <if test="valueAvg != null  and valueAvg != ''"> and value_avg = #{valueAvg}</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="opt != null  and opt != ''"> and opt = #{opt}</if>
            <if test="date != null "> and date = #{date}</if>
        </where>
    </select>
    
    <select id="selectSyncMeasuringToolTorsionById" parameterType="Long" resultMap="SyncMeasuringToolTorsionResult">
        <include refid="selectSyncMeasuringToolTorsionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSyncMeasuringToolTorsion" parameterType="SyncMeasuringToolTorsion" useGeneratedKeys="true" keyProperty="id">
        insert into sync_measuring_tool_torsion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="seqId != null">seq_id,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="value1 != null">value1,</if>
            <if test="value2 != null">value2,</if>
            <if test="value3 != null">value3,</if>
            <if test="valueAvg != null">value_avg,</if>
            <if test="result != null">result,</if>
            <if test="opt != null">opt,</if>
            <if test="date != null">date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="seqId != null">#{seqId},</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="value1 != null">#{value1},</if>
            <if test="value2 != null">#{value2},</if>
            <if test="value3 != null">#{value3},</if>
            <if test="valueAvg != null">#{valueAvg},</if>
            <if test="result != null">#{result},</if>
            <if test="opt != null">#{opt},</if>
            <if test="date != null">#{date},</if>
         </trim>
    </insert>

    <update id="updateSyncMeasuringToolTorsion" parameterType="SyncMeasuringToolTorsion">
        update sync_measuring_tool_torsion
        <trim prefix="SET" suffixOverrides=",">
            <if test="seqId != null">seq_id = #{seqId},</if>
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="value1 != null">value1 = #{value1},</if>
            <if test="value2 != null">value2 = #{value2},</if>
            <if test="value3 != null">value3 = #{value3},</if>
            <if test="valueAvg != null">value_avg = #{valueAvg},</if>
            <if test="result != null">result = #{result},</if>
            <if test="opt != null">opt = #{opt},</if>
            <if test="date != null">date = #{date},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSyncMeasuringToolTorsionById" parameterType="Long">
        update sync_measuring_tool_torsion set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteSyncMeasuringToolTorsionByIds" parameterType="String">
        update sync_measuring_tool_torsion set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>