<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.sync.mapper.MakeInspectionViewMapper">

    <select id="getMonthCount" parameterType="map" resultType="OrderMonthCount">
        SELECT
        CONVERT(VARCHAR(7), CTime, 120) as month,
        COUNT ( Id ) as saleNO
        FROM
        P_InspectionDetail
        WHERE
        ItemCode IN
        <foreach open="(" close=")" separator="," collection="itemCodes" item="item">
            #{item}
        </foreach>
        AND CTime >= #{startDate}
        AND CTime &lt;= #{endDate}
        GROUP BY
        CONVERT(VARCHAR(7), CTime, 120)
        ORDER BY
        CONVERT(VARCHAR(7), CTime, 120) ASC
    </select>

    <select id="getYearCount" parameterType="map" resultType="OrderYearCount">
        SELECT
        CONVERT(VARCHAR(4), CTime, 111) as year,
        COUNT ( Id ) as saleNO
        FROM
        P_InspectionDetail
        WHERE
        ItemCode IN
        <foreach open="(" close=")" separator="," collection="itemCodes" item="item">
        #{item}
        </foreach>
        AND CTime >= #{startDate}
          AND CTime &lt;= #{endDate}
        GROUP BY
        CONVERT(VARCHAR(4), CTime, 111)
        ORDER BY
        CONVERT(VARCHAR(4), CTime, 111) ASC
    </select>

</mapper>