<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.sync.mapper.SyncProductionMapper">
    
    <resultMap type="SyncProduction" id="SyncProductionResult">
        <result property="id"    column="id"    />
        <result property="pid"    column="pid"    />
        <result property="workLine"    column="work_line"    />
        <result property="orderNo"    column="order_no"    />
        <result property="contractNo"    column="contract_no"    />
        <result property="serialNo"    column="serial_no"    />
        <result property="deliveryCompany"    column="delivery_company"    />
        <result property="modelName"    column="model_name"    />
        <result property="fwdModel"    column="fwd_model"    />
        <result property="qty"    column="qty"    />
        <result property="importUrgeDate"    column="import_urge_date"    />
        <result property="urgeDate"    column="urge_date"    />
        <result property="planPackDate"    column="plan_pack_date"    />
        <result property="planDeliveryDate"    column="plan_delivery_date"    />
        <result property="orderFlag"    column="order_flag"    />
        <result property="nonStandard"    column="non_standard"    />
        <result property="pccl"    column="pccl"    />
        <result property="mpyq"    column="mpyq"    />
        <result property="xbsz"    column="xbsz"    />
        <result property="oversea"    column="oversea"    />
        <result property="batch"    column="batch"    />
        <result property="projectName"    column="project_name"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSyncProductionVo">
        select id, pid, work_line, order_no, contract_no, serial_no, delivery_company, model_name, fwd_model, qty, import_urge_date, urge_date, plan_pack_date, plan_delivery_date, order_flag, non_standard, pccl, mpyq, xbsz, oversea, batch, project_name, remark from sync_production
    </sql>

    <select id="selectSyncProductionList" parameterType="SyncProduction" resultMap="SyncProductionResult">
        <include refid="selectSyncProductionVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="pid != null  and pid != ''"> and pid = #{pid}</if>
            <if test="workLine != null  and workLine != ''"> and work_line = #{workLine}</if>
            <if test="orderNo != null  and orderNo != ''"> and order_no = #{orderNo}</if>
            <if test="contractNo != null  and contractNo != ''"> and contract_no = #{contractNo}</if>
            <if test="serialNo != null  and serialNo != ''"> and serial_no = #{serialNo}</if>
            <if test="deliveryCompany != null  and deliveryCompany != ''"> and delivery_company = #{deliveryCompany}</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="fwdModel != null  and fwdModel != ''"> and fwd_model = #{fwdModel}</if>
            <if test="qty != null  and qty != ''"> and qty = #{qty}</if>
            <if test="importUrgeDate != null "> and import_urge_date = #{importUrgeDate}</if>
            <if test="urgeDate != null "> and urge_date = #{urgeDate}</if>
            <if test="planPackDate != null "> and plan_pack_date = #{planPackDate}</if>
            <if test="planDeliveryDate != null "> and plan_delivery_date = #{planDeliveryDate}</if>
            <if test="orderFlag != null  and orderFlag != ''"> and order_flag = #{orderFlag}</if>
            <if test="nonStandard != null  and nonStandard != ''"> and non_standard = #{nonStandard}</if>
            <if test="pccl != null  and pccl != ''"> and pccl = #{pccl}</if>
            <if test="mpyq != null  and mpyq != ''"> and mpyq = #{mpyq}</if>
            <if test="xbsz != null  and xbsz != ''"> and xbsz = #{xbsz}</if>
            <if test="oversea != null  and oversea != ''"> and oversea = #{oversea}</if>
            <if test="batch != null  and batch != ''"> and batch = #{batch}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
        </where>
    </select>
    
    <select id="selectSyncProductionById" parameterType="Long" resultMap="SyncProductionResult">
        <include refid="selectSyncProductionVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSyncProduction" parameterType="SyncProduction" useGeneratedKeys="true" keyProperty="id">
        insert into sync_production
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="pid != null">pid,</if>
            <if test="workLine != null">work_line,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="contractNo != null">contract_no,</if>
            <if test="serialNo != null">serial_no,</if>
            <if test="deliveryCompany != null">delivery_company,</if>
            <if test="modelName != null">model_name,</if>
            <if test="fwdModel != null">fwd_model,</if>
            <if test="qty != null">qty,</if>
            <if test="importUrgeDate != null">import_urge_date,</if>
            <if test="urgeDate != null">urge_date,</if>
            <if test="planPackDate != null">plan_pack_date,</if>
            <if test="planDeliveryDate != null">plan_delivery_date,</if>
            <if test="orderFlag != null">order_flag,</if>
            <if test="nonStandard != null">non_standard,</if>
            <if test="pccl != null">pccl,</if>
            <if test="mpyq != null">mpyq,</if>
            <if test="xbsz != null">xbsz,</if>
            <if test="oversea != null">oversea,</if>
            <if test="batch != null">batch,</if>
            <if test="projectName != null">project_name,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="pid != null">#{pid},</if>
            <if test="workLine != null">#{workLine},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="contractNo != null">#{contractNo},</if>
            <if test="serialNo != null">#{serialNo},</if>
            <if test="deliveryCompany != null">#{deliveryCompany},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="fwdModel != null">#{fwdModel},</if>
            <if test="qty != null">#{qty},</if>
            <if test="importUrgeDate != null">#{importUrgeDate},</if>
            <if test="urgeDate != null">#{urgeDate},</if>
            <if test="planPackDate != null">#{planPackDate},</if>
            <if test="planDeliveryDate != null">#{planDeliveryDate},</if>
            <if test="orderFlag != null">#{orderFlag},</if>
            <if test="nonStandard != null">#{nonStandard},</if>
            <if test="pccl != null">#{pccl},</if>
            <if test="mpyq != null">#{mpyq},</if>
            <if test="xbsz != null">#{xbsz},</if>
            <if test="oversea != null">#{oversea},</if>
            <if test="batch != null">#{batch},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSyncProduction" parameterType="SyncProduction">
        update sync_production
        <trim prefix="SET" suffixOverrides=",">
            <if test="pid != null">pid = #{pid},</if>
            <if test="workLine != null">work_line = #{workLine},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="contractNo != null">contract_no = #{contractNo},</if>
            <if test="serialNo != null">serial_no = #{serialNo},</if>
            <if test="deliveryCompany != null">delivery_company = #{deliveryCompany},</if>
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="fwdModel != null">fwd_model = #{fwdModel},</if>
            <if test="qty != null">qty = #{qty},</if>
            <if test="importUrgeDate != null">import_urge_date = #{importUrgeDate},</if>
            <if test="urgeDate != null">urge_date = #{urgeDate},</if>
            <if test="planPackDate != null">plan_pack_date = #{planPackDate},</if>
            <if test="planDeliveryDate != null">plan_delivery_date = #{planDeliveryDate},</if>
            <if test="orderFlag != null">order_flag = #{orderFlag},</if>
            <if test="nonStandard != null">non_standard = #{nonStandard},</if>
            <if test="pccl != null">pccl = #{pccl},</if>
            <if test="mpyq != null">mpyq = #{mpyq},</if>
            <if test="xbsz != null">xbsz = #{xbsz},</if>
            <if test="oversea != null">oversea = #{oversea},</if>
            <if test="batch != null">batch = #{batch},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSyncProductionById" parameterType="Long">
        update sync_production set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteSyncProductionByIds" parameterType="String">
        update sync_production set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>