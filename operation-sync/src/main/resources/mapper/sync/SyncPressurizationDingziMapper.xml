<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.sync.mapper.SyncPressurizationDingziMapper">
    
    <resultMap type="SyncPressurizationDingzi" id="SyncPressurizationDingziResult">
        <result property="abh"    column="abh"    />
        <result property="xh"    column="xh"    />
        <result property="scbh"    column="scbh"    />
        <result property="csrq"    column="csrq"    />
        <result property="cssj"    column="cssj"    />
        <result property="csrqsj"    column="csrqsj"    />
        <result property="csry"    column="csry"    />
        <result property="txjh"    column="txjh"    />
        <result property="txbh"    column="txbh"    />
        <result property="rxgh"    column="rxgh"    />
        <result property="qxgh"    column="qxgh"    />
        <result property="bzgh"    column="bzgh"    />
        <result property="hjgh"    column="hjgh"    />
        <result property="t1"    column="t1"    />
        <result property="t2"    column="t2"    />
        <result property="ra"    column="Ra"    />
        <result property="rb"    column="rb"    />
        <result property="rc"    column="rc"    />
        <result property="rp"    column="Rp"    />
        <result property="rjg"    column="rjg"    />
        <result property="rt"    column="Rt"    />
        <result property="ny1u"    column="ny1u"    />
        <result property="ny1i"    column="ny1i"    />
        <result property="nyjg"    column="nyjg"    />
        <result property="rmu"    column="rmu"    />
        <result property="rm"    column="Rm"    />
        <result property="rmjg"    column="rmjg"    />
        <result property="rm1u"    column="rm1u"    />
        <result property="rm1i"    column="rm1i"    />
        <result property="rm1jg"    column="rm1jg"    />
        <result property="jyu"    column="jyu"    />
        <result property="jyr"    column="jyr"    />
        <result property="jyjg"    column="jyjg"    />
        <result property="rm2u"    column="rm2u"    />
        <result property="rm2i"    column="rm2i"    />
        <result property="rm2jg"    column="rm2jg"    />
        <result property="s1"    column="s1"    />
        <result property="f1"    column="f1"    />
        <result property="s2"    column="s2"    />
        <result property="f2"    column="f2"    />
        <result property="s3"    column="s3"    />
        <result property="f3"    column="f3"    />
        <result property="s4"    column="s4"    />
        <result property="f4"    column="f4"    />
        <result property="zjjg"    column="zjjg"    />
        <result property="cszjg"    column="cszjg"    />
        <result property="beizhu"    column="beizhu"    />
    </resultMap>

    <sql id="selectSyncPressurizationDingziVo">
        select abh, xh, scbh, csrq, cssj, csrqsj, csry, txjh, txbh, rxgh, qxgh, bzgh, hjgh, t1, t2, Ra, rb, rc, Rp, rjg, Rt, ny1u, ny1i, nyjg, rmu, Rm, rmjg, rm1u, rm1i, rm1jg, jyu, jyr, jyjg, rm2u, rm2i, rm2jg, s1, f1, s2, f2, s3, f3, s4, f4, zjjg, cszjg, beizhu from sync_pressurization_dingzi
    </sql>

    <select id="selectSyncPressurizationDingziList" parameterType="SyncPressurizationDingzi" resultMap="SyncPressurizationDingziResult">
        <include refid="selectSyncPressurizationDingziVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="abh != null "> and abh = #{abh}</if>
            <if test="xh != null  and xh != ''"> and xh = #{xh}</if>
            <if test="scbh != null  and scbh != ''"> and scbh = #{scbh}</if>
            <if test="csrq != null "> and csrq = #{csrq}</if>
            <if test="cssj != null  and cssj != ''"> and cssj = #{cssj}</if>
            <if test="csrqsj != null  and csrqsj != ''"> and csrqsj = #{csrqsj}</if>
            <if test="csry != null  and csry != ''"> and csry = #{csry}</if>
            <if test="txjh != null  and txjh != ''"> and txjh = #{txjh}</if>
            <if test="txbh != null  and txbh != ''"> and txbh = #{txbh}</if>
            <if test="rxgh != null "> and rxgh = #{rxgh}</if>
            <if test="qxgh != null "> and qxgh = #{qxgh}</if>
            <if test="bzgh != null "> and bzgh = #{bzgh}</if>
            <if test="hjgh != null "> and hjgh = #{hjgh}</if>
            <if test="t1 != null  and t1 != ''"> and t1 = #{t1}</if>
            <if test="t2 != null  and t2 != ''"> and t2 = #{t2}</if>
            <if test="ra != null  and ra != ''"> and Ra = #{ra}</if>
            <if test="rb != null  and rb != ''"> and rb = #{rb}</if>
            <if test="rc != null  and rc != ''"> and rc = #{rc}</if>
            <if test="rp != null  and rp != ''"> and Rp = #{rp}</if>
            <if test="rjg != null  and rjg != ''"> and rjg = #{rjg}</if>
            <if test="rt != null  and rt != ''"> and Rt = #{rt}</if>
            <if test="ny1u != null  and ny1u != ''"> and ny1u = #{ny1u}</if>
            <if test="ny1i != null  and ny1i != ''"> and ny1i = #{ny1i}</if>
            <if test="nyjg != null  and nyjg != ''"> and nyjg = #{nyjg}</if>
            <if test="rmu != null  and rmu != ''"> and rmu = #{rmu}</if>
            <if test="rm != null  and rm != ''"> and Rm = #{rm}</if>
            <if test="rmjg != null  and rmjg != ''"> and rmjg = #{rmjg}</if>
            <if test="rm1u != null  and rm1u != ''"> and rm1u = #{rm1u}</if>
            <if test="rm1i != null  and rm1i != ''"> and rm1i = #{rm1i}</if>
            <if test="rm1jg != null  and rm1jg != ''"> and rm1jg = #{rm1jg}</if>
            <if test="jyu != null  and jyu != ''"> and jyu = #{jyu}</if>
            <if test="jyr != null  and jyr != ''"> and jyr = #{jyr}</if>
            <if test="jyjg != null  and jyjg != ''"> and jyjg = #{jyjg}</if>
            <if test="rm2u != null  and rm2u != ''"> and rm2u = #{rm2u}</if>
            <if test="rm2i != null  and rm2i != ''"> and rm2i = #{rm2i}</if>
            <if test="rm2jg != null  and rm2jg != ''"> and rm2jg = #{rm2jg}</if>
            <if test="s1 != null  and s1 != ''"> and s1 = #{s1}</if>
            <if test="f1 != null  and f1 != ''"> and f1 = #{f1}</if>
            <if test="s2 != null  and s2 != ''"> and s2 = #{s2}</if>
            <if test="f2 != null  and f2 != ''"> and f2 = #{f2}</if>
            <if test="s3 != null  and s3 != ''"> and s3 = #{s3}</if>
            <if test="f3 != null  and f3 != ''"> and f3 = #{f3}</if>
            <if test="s4 != null  and s4 != ''"> and s4 = #{s4}</if>
            <if test="f4 != null  and f4 != ''"> and f4 = #{f4}</if>
            <if test="zjjg != null  and zjjg != ''"> and zjjg = #{zjjg}</if>
            <if test="cszjg != null  and cszjg != ''"> and cszjg = #{cszjg}</if>
            <if test="beizhu != null  and beizhu != ''"> and beizhu = #{beizhu}</if>
        </where>
    </select>
    
    <select id="selectSyncPressurizationDingziByAbh" parameterType="Long" resultMap="SyncPressurizationDingziResult">
        <include refid="selectSyncPressurizationDingziVo"/>
        where abh = #{abh}
    </select>
        
    <insert id="insertSyncPressurizationDingzi" parameterType="SyncPressurizationDingzi" useGeneratedKeys="true" keyProperty="abh">
        insert into sync_pressurization_dingzi
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="xh != null">xh,</if>
            <if test="scbh != null and scbh != ''">scbh,</if>
            <if test="csrq != null">csrq,</if>
            <if test="cssj != null">cssj,</if>
            <if test="csrqsj != null">csrqsj,</if>
            <if test="csry != null">csry,</if>
            <if test="txjh != null">txjh,</if>
            <if test="txbh != null">txbh,</if>
            <if test="rxgh != null">rxgh,</if>
            <if test="qxgh != null">qxgh,</if>
            <if test="bzgh != null">bzgh,</if>
            <if test="hjgh != null">hjgh,</if>
            <if test="t1 != null">t1,</if>
            <if test="t2 != null">t2,</if>
            <if test="ra != null">Ra,</if>
            <if test="rb != null">rb,</if>
            <if test="rc != null">rc,</if>
            <if test="rp != null">Rp,</if>
            <if test="rjg != null">rjg,</if>
            <if test="rt != null">Rt,</if>
            <if test="ny1u != null">ny1u,</if>
            <if test="ny1i != null">ny1i,</if>
            <if test="nyjg != null">nyjg,</if>
            <if test="rmu != null">rmu,</if>
            <if test="rm != null">Rm,</if>
            <if test="rmjg != null">rmjg,</if>
            <if test="rm1u != null">rm1u,</if>
            <if test="rm1i != null">rm1i,</if>
            <if test="rm1jg != null">rm1jg,</if>
            <if test="jyu != null">jyu,</if>
            <if test="jyr != null">jyr,</if>
            <if test="jyjg != null">jyjg,</if>
            <if test="rm2u != null">rm2u,</if>
            <if test="rm2i != null">rm2i,</if>
            <if test="rm2jg != null">rm2jg,</if>
            <if test="s1 != null">s1,</if>
            <if test="f1 != null">f1,</if>
            <if test="s2 != null">s2,</if>
            <if test="f2 != null">f2,</if>
            <if test="s3 != null">s3,</if>
            <if test="f3 != null">f3,</if>
            <if test="s4 != null">s4,</if>
            <if test="f4 != null">f4,</if>
            <if test="zjjg != null">zjjg,</if>
            <if test="cszjg != null">cszjg,</if>
            <if test="beizhu != null">beizhu,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="xh != null">#{xh},</if>
            <if test="scbh != null and scbh != ''">#{scbh},</if>
            <if test="csrq != null">#{csrq},</if>
            <if test="cssj != null">#{cssj},</if>
            <if test="csrqsj != null">#{csrqsj},</if>
            <if test="csry != null">#{csry},</if>
            <if test="txjh != null">#{txjh},</if>
            <if test="txbh != null">#{txbh},</if>
            <if test="rxgh != null">#{rxgh},</if>
            <if test="qxgh != null">#{qxgh},</if>
            <if test="bzgh != null">#{bzgh},</if>
            <if test="hjgh != null">#{hjgh},</if>
            <if test="t1 != null">#{t1},</if>
            <if test="t2 != null">#{t2},</if>
            <if test="ra != null">#{ra},</if>
            <if test="rb != null">#{rb},</if>
            <if test="rc != null">#{rc},</if>
            <if test="rp != null">#{rp},</if>
            <if test="rjg != null">#{rjg},</if>
            <if test="rt != null">#{rt},</if>
            <if test="ny1u != null">#{ny1u},</if>
            <if test="ny1i != null">#{ny1i},</if>
            <if test="nyjg != null">#{nyjg},</if>
            <if test="rmu != null">#{rmu},</if>
            <if test="rm != null">#{rm},</if>
            <if test="rmjg != null">#{rmjg},</if>
            <if test="rm1u != null">#{rm1u},</if>
            <if test="rm1i != null">#{rm1i},</if>
            <if test="rm1jg != null">#{rm1jg},</if>
            <if test="jyu != null">#{jyu},</if>
            <if test="jyr != null">#{jyr},</if>
            <if test="jyjg != null">#{jyjg},</if>
            <if test="rm2u != null">#{rm2u},</if>
            <if test="rm2i != null">#{rm2i},</if>
            <if test="rm2jg != null">#{rm2jg},</if>
            <if test="s1 != null">#{s1},</if>
            <if test="f1 != null">#{f1},</if>
            <if test="s2 != null">#{s2},</if>
            <if test="f2 != null">#{f2},</if>
            <if test="s3 != null">#{s3},</if>
            <if test="f3 != null">#{f3},</if>
            <if test="s4 != null">#{s4},</if>
            <if test="f4 != null">#{f4},</if>
            <if test="zjjg != null">#{zjjg},</if>
            <if test="cszjg != null">#{cszjg},</if>
            <if test="beizhu != null">#{beizhu},</if>
         </trim>
    </insert>

    <update id="updateSyncPressurizationDingzi" parameterType="SyncPressurizationDingzi">
        update sync_pressurization_dingzi
        <trim prefix="SET" suffixOverrides=",">
            <if test="xh != null">xh = #{xh},</if>
            <if test="scbh != null and scbh != ''">scbh = #{scbh},</if>
            <if test="csrq != null">csrq = #{csrq},</if>
            <if test="cssj != null">cssj = #{cssj},</if>
            <if test="csrqsj != null">csrqsj = #{csrqsj},</if>
            <if test="csry != null">csry = #{csry},</if>
            <if test="txjh != null">txjh = #{txjh},</if>
            <if test="txbh != null">txbh = #{txbh},</if>
            <if test="rxgh != null">rxgh = #{rxgh},</if>
            <if test="qxgh != null">qxgh = #{qxgh},</if>
            <if test="bzgh != null">bzgh = #{bzgh},</if>
            <if test="hjgh != null">hjgh = #{hjgh},</if>
            <if test="t1 != null">t1 = #{t1},</if>
            <if test="t2 != null">t2 = #{t2},</if>
            <if test="ra != null">Ra = #{ra},</if>
            <if test="rb != null">rb = #{rb},</if>
            <if test="rc != null">rc = #{rc},</if>
            <if test="rp != null">Rp = #{rp},</if>
            <if test="rjg != null">rjg = #{rjg},</if>
            <if test="rt != null">Rt = #{rt},</if>
            <if test="ny1u != null">ny1u = #{ny1u},</if>
            <if test="ny1i != null">ny1i = #{ny1i},</if>
            <if test="nyjg != null">nyjg = #{nyjg},</if>
            <if test="rmu != null">rmu = #{rmu},</if>
            <if test="rm != null">Rm = #{rm},</if>
            <if test="rmjg != null">rmjg = #{rmjg},</if>
            <if test="rm1u != null">rm1u = #{rm1u},</if>
            <if test="rm1i != null">rm1i = #{rm1i},</if>
            <if test="rm1jg != null">rm1jg = #{rm1jg},</if>
            <if test="jyu != null">jyu = #{jyu},</if>
            <if test="jyr != null">jyr = #{jyr},</if>
            <if test="jyjg != null">jyjg = #{jyjg},</if>
            <if test="rm2u != null">rm2u = #{rm2u},</if>
            <if test="rm2i != null">rm2i = #{rm2i},</if>
            <if test="rm2jg != null">rm2jg = #{rm2jg},</if>
            <if test="s1 != null">s1 = #{s1},</if>
            <if test="f1 != null">f1 = #{f1},</if>
            <if test="s2 != null">s2 = #{s2},</if>
            <if test="f2 != null">f2 = #{f2},</if>
            <if test="s3 != null">s3 = #{s3},</if>
            <if test="f3 != null">f3 = #{f3},</if>
            <if test="s4 != null">s4 = #{s4},</if>
            <if test="f4 != null">f4 = #{f4},</if>
            <if test="zjjg != null">zjjg = #{zjjg},</if>
            <if test="cszjg != null">cszjg = #{cszjg},</if>
            <if test="beizhu != null">beizhu = #{beizhu},</if>
        </trim>
        where abh = #{abh}
    </update>

    <delete id="deleteSyncPressurizationDingziByAbh" parameterType="Long">
        update sync_pressurization_dingzi set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where abh = #{abh}
    </delete>

    <delete id="deleteSyncPressurizationDingziByAbhs" parameterType="String">
        update sync_pressurization_dingzi set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where abh in
        <foreach item="abh" collection="abhs" open="(" separator="," close=")">
            #{abh}
        </foreach>
    </delete>

</mapper>