<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.sync.mapper.OrderInfoMapper">

    <resultMap type="OrderInfo" id="OrderInfoResult">
        <result property="customerOrderNum" column="CustomerOrderNum"/>
        <result property="contractNum" column="ContractNum"/>
        <result property="productModel" column="ProductModel"/>
        <result property="deliveryDate" column="DeliveryDate"/>
        <result property="customerName" column="CustomerName"/>
        <result property="cTime" column="CTime"/>
    </resultMap>

    <select id="listOrderInfo" resultType="OrderInfo" resultMap="OrderInfoResult">
        select top(10) t1.CustomerOrder<PERSON>um,t1.ContractNum, t1.ProductModel, t1.DeliveryDate, t2.CustomerName, t1.CTime
        from SD_Sale_OrderDetails t1
                 left join SD_Sale_OrderInfo t2 on t1.OrderNum = t2.OrderNum
        order by t1.CTime desc
    </select>

    <select id="getMonthCount" parameterType="map" resultType="OrderMonthCount">
        SELECT
        FORMAT(CTime, 'yyyy-MM') AS month,
        COUNT(*) AS saleNO
        FROM
        SD_Sale_OrderDetails
        WHERE
        CTime >= #{startDate} AND CTime &lt;= #{endDate}
        GROUP BY
        FORMAT(CTime, 'yyyy-MM')
        ORDER BY
        FORMAT(CTime, 'yyyy-MM');
    </select>

    <select id="getYearCount" parameterType="map" resultType="OrderYearCount">
        SELECT
        FORMAT(CTime, 'yyyy') AS year,
        COUNT(*) AS saleNO
        FROM
        SD_Sale_OrderDetails
        WHERE
        CTime >= #{startDate} AND CTime &lt;= #{endDate}
        GROUP BY
        FORMAT(CTime, 'yyyy')
        ORDER BY
        FORMAT(CTime, 'yyyy');
    </select>


</mapper>