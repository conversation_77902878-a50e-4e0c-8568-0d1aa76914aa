<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.sync.mapper.SyncPressurizationNaiyaMapper">
    
    <resultMap type="SyncPressurizationNaiya" id="SyncPressurizationNaiyaResult">
        <result property="abh"    column="abh"    />
        <result property="xh"    column="xh"    />
        <result property="scbh"    column="scbh"    />
        <result property="csrq"    column="csrq"    />
        <result property="cssj"    column="cssj"    />
        <result property="csrqsj"    column="csrqsj"    />
        <result property="csry"    column="csry"    />
        <result property="t1"    column="t1"    />
        <result property="t2"    column="t2"    />
        <result property="ra"    column="Ra"    />
        <result property="rb"    column="rb"    />
        <result property="rc"    column="rc"    />
        <result property="rp"    column="Rp"    />
        <result property="rjg"    column="rjg"    />
        <result property="rt"    column="Rt"    />
        <result property="ny1u"    column="ny1u"    />
        <result property="ny1i"    column="ny1i"    />
        <result property="nyjg"    column="nyjg"    />
        <result property="rmu"    column="rmu"    />
        <result property="rm"    column="Rm"    />
        <result property="rmjg"    column="rmjg"    />
        <result property="rm1u"    column="rm1u"    />
        <result property="rm1i"    column="rm1i"    />
        <result property="rm1jg"    column="rm1jg"    />
        <result property="jyu"    column="jyu"    />
        <result property="jyr"    column="jyr"    />
        <result property="jyjg"    column="jyjg"    />
        <result property="rm2u"    column="rm2u"    />
        <result property="rm2i"    column="rm2i"    />
        <result property="rm2jg"    column="rm2jg"    />
        <result property="cszjg"    column="cszjg"    />
        <result property="beizhu"    column="beizhu"    />
    </resultMap>

    <sql id="selectSyncPressurizationNaiyaVo">
        select abh, xh, scbh, csrq, cssj, csrqsj, csry, t1, t2, Ra, rb, rc, Rp, rjg, Rt, ny1u, ny1i, nyjg, rmu, Rm, rmjg, rm1u, rm1i, rm1jg, jyu, jyr, jyjg, rm2u, rm2i, rm2jg, cszjg, beizhu from sync_pressurization_naiya
    </sql>

    <select id="selectSyncPressurizationNaiyaList" parameterType="SyncPressurizationNaiya" resultMap="SyncPressurizationNaiyaResult">
        <include refid="selectSyncPressurizationNaiyaVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="abh != null "> and abh = #{abh}</if>
            <if test="xh != null  and xh != ''"> and xh = #{xh}</if>
            <if test="scbh != null  and scbh != ''"> and scbh = #{scbh}</if>
            <if test="csrq != null "> and csrq = #{csrq}</if>
            <if test="cssj != null  and cssj != ''"> and cssj = #{cssj}</if>
            <if test="csrqsj != null  and csrqsj != ''"> and csrqsj = #{csrqsj}</if>
            <if test="csry != null  and csry != ''"> and csry = #{csry}</if>
            <if test="t1 != null  and t1 != ''"> and t1 = #{t1}</if>
            <if test="t2 != null  and t2 != ''"> and t2 = #{t2}</if>
            <if test="ra != null  and ra != ''"> and Ra = #{ra}</if>
            <if test="rb != null  and rb != ''"> and rb = #{rb}</if>
            <if test="rc != null  and rc != ''"> and rc = #{rc}</if>
            <if test="rp != null  and rp != ''"> and Rp = #{rp}</if>
            <if test="rjg != null  and rjg != ''"> and rjg = #{rjg}</if>
            <if test="rt != null  and rt != ''"> and Rt = #{rt}</if>
            <if test="ny1u != null  and ny1u != ''"> and ny1u = #{ny1u}</if>
            <if test="ny1i != null  and ny1i != ''"> and ny1i = #{ny1i}</if>
            <if test="nyjg != null  and nyjg != ''"> and nyjg = #{nyjg}</if>
            <if test="rmu != null  and rmu != ''"> and rmu = #{rmu}</if>
            <if test="rm != null  and rm != ''"> and Rm = #{rm}</if>
            <if test="rmjg != null  and rmjg != ''"> and rmjg = #{rmjg}</if>
            <if test="rm1u != null  and rm1u != ''"> and rm1u = #{rm1u}</if>
            <if test="rm1i != null  and rm1i != ''"> and rm1i = #{rm1i}</if>
            <if test="rm1jg != null  and rm1jg != ''"> and rm1jg = #{rm1jg}</if>
            <if test="jyu != null  and jyu != ''"> and jyu = #{jyu}</if>
            <if test="jyr != null  and jyr != ''"> and jyr = #{jyr}</if>
            <if test="jyjg != null  and jyjg != ''"> and jyjg = #{jyjg}</if>
            <if test="rm2u != null  and rm2u != ''"> and rm2u = #{rm2u}</if>
            <if test="rm2i != null  and rm2i != ''"> and rm2i = #{rm2i}</if>
            <if test="rm2jg != null  and rm2jg != ''"> and rm2jg = #{rm2jg}</if>
            <if test="cszjg != null  and cszjg != ''"> and cszjg = #{cszjg}</if>
            <if test="beizhu != null  and beizhu != ''"> and beizhu = #{beizhu}</if>
        </where>
    </select>
    
    <select id="selectSyncPressurizationNaiyaByAbh" parameterType="Long" resultMap="SyncPressurizationNaiyaResult">
        <include refid="selectSyncPressurizationNaiyaVo"/>
        where abh = #{abh}
    </select>
        
    <insert id="insertSyncPressurizationNaiya" parameterType="SyncPressurizationNaiya" useGeneratedKeys="true" keyProperty="abh">
        insert into sync_pressurization_naiya
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="xh != null">xh,</if>
            <if test="scbh != null and scbh != ''">scbh,</if>
            <if test="csrq != null">csrq,</if>
            <if test="cssj != null">cssj,</if>
            <if test="csrqsj != null">csrqsj,</if>
            <if test="csry != null">csry,</if>
            <if test="t1 != null">t1,</if>
            <if test="t2 != null">t2,</if>
            <if test="ra != null">Ra,</if>
            <if test="rb != null">rb,</if>
            <if test="rc != null">rc,</if>
            <if test="rp != null">Rp,</if>
            <if test="rjg != null">rjg,</if>
            <if test="rt != null">Rt,</if>
            <if test="ny1u != null">ny1u,</if>
            <if test="ny1i != null">ny1i,</if>
            <if test="nyjg != null">nyjg,</if>
            <if test="rmu != null">rmu,</if>
            <if test="rm != null">Rm,</if>
            <if test="rmjg != null">rmjg,</if>
            <if test="rm1u != null">rm1u,</if>
            <if test="rm1i != null">rm1i,</if>
            <if test="rm1jg != null">rm1jg,</if>
            <if test="jyu != null">jyu,</if>
            <if test="jyr != null">jyr,</if>
            <if test="jyjg != null">jyjg,</if>
            <if test="rm2u != null">rm2u,</if>
            <if test="rm2i != null">rm2i,</if>
            <if test="rm2jg != null">rm2jg,</if>
            <if test="cszjg != null">cszjg,</if>
            <if test="beizhu != null">beizhu,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="xh != null">#{xh},</if>
            <if test="scbh != null and scbh != ''">#{scbh},</if>
            <if test="csrq != null">#{csrq},</if>
            <if test="cssj != null">#{cssj},</if>
            <if test="csrqsj != null">#{csrqsj},</if>
            <if test="csry != null">#{csry},</if>
            <if test="t1 != null">#{t1},</if>
            <if test="t2 != null">#{t2},</if>
            <if test="ra != null">#{ra},</if>
            <if test="rb != null">#{rb},</if>
            <if test="rc != null">#{rc},</if>
            <if test="rp != null">#{rp},</if>
            <if test="rjg != null">#{rjg},</if>
            <if test="rt != null">#{rt},</if>
            <if test="ny1u != null">#{ny1u},</if>
            <if test="ny1i != null">#{ny1i},</if>
            <if test="nyjg != null">#{nyjg},</if>
            <if test="rmu != null">#{rmu},</if>
            <if test="rm != null">#{rm},</if>
            <if test="rmjg != null">#{rmjg},</if>
            <if test="rm1u != null">#{rm1u},</if>
            <if test="rm1i != null">#{rm1i},</if>
            <if test="rm1jg != null">#{rm1jg},</if>
            <if test="jyu != null">#{jyu},</if>
            <if test="jyr != null">#{jyr},</if>
            <if test="jyjg != null">#{jyjg},</if>
            <if test="rm2u != null">#{rm2u},</if>
            <if test="rm2i != null">#{rm2i},</if>
            <if test="rm2jg != null">#{rm2jg},</if>
            <if test="cszjg != null">#{cszjg},</if>
            <if test="beizhu != null">#{beizhu},</if>
         </trim>
    </insert>

    <update id="updateSyncPressurizationNaiya" parameterType="SyncPressurizationNaiya">
        update sync_pressurization_naiya
        <trim prefix="SET" suffixOverrides=",">
            <if test="xh != null">xh = #{xh},</if>
            <if test="scbh != null and scbh != ''">scbh = #{scbh},</if>
            <if test="csrq != null">csrq = #{csrq},</if>
            <if test="cssj != null">cssj = #{cssj},</if>
            <if test="csrqsj != null">csrqsj = #{csrqsj},</if>
            <if test="csry != null">csry = #{csry},</if>
            <if test="t1 != null">t1 = #{t1},</if>
            <if test="t2 != null">t2 = #{t2},</if>
            <if test="ra != null">Ra = #{ra},</if>
            <if test="rb != null">rb = #{rb},</if>
            <if test="rc != null">rc = #{rc},</if>
            <if test="rp != null">Rp = #{rp},</if>
            <if test="rjg != null">rjg = #{rjg},</if>
            <if test="rt != null">Rt = #{rt},</if>
            <if test="ny1u != null">ny1u = #{ny1u},</if>
            <if test="ny1i != null">ny1i = #{ny1i},</if>
            <if test="nyjg != null">nyjg = #{nyjg},</if>
            <if test="rmu != null">rmu = #{rmu},</if>
            <if test="rm != null">Rm = #{rm},</if>
            <if test="rmjg != null">rmjg = #{rmjg},</if>
            <if test="rm1u != null">rm1u = #{rm1u},</if>
            <if test="rm1i != null">rm1i = #{rm1i},</if>
            <if test="rm1jg != null">rm1jg = #{rm1jg},</if>
            <if test="jyu != null">jyu = #{jyu},</if>
            <if test="jyr != null">jyr = #{jyr},</if>
            <if test="jyjg != null">jyjg = #{jyjg},</if>
            <if test="rm2u != null">rm2u = #{rm2u},</if>
            <if test="rm2i != null">rm2i = #{rm2i},</if>
            <if test="rm2jg != null">rm2jg = #{rm2jg},</if>
            <if test="cszjg != null">cszjg = #{cszjg},</if>
            <if test="beizhu != null">beizhu = #{beizhu},</if>
        </trim>
        where abh = #{abh}
    </update>

    <delete id="deleteSyncPressurizationNaiyaByAbh" parameterType="Long">
        update sync_pressurization_naiya set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where abh = #{abh}
    </delete>

    <delete id="deleteSyncPressurizationNaiyaByAbhs" parameterType="String">
        update sync_pressurization_naiya set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where abh in
        <foreach item="abh" collection="abhs" open="(" separator="," close=")">
            #{abh}
        </foreach>
    </delete>

</mapper>