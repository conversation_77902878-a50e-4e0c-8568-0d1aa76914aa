<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.sync.mapper.SyncMeasuringToolMapper">
    
    <resultMap type="SyncMeasuringTool" id="SyncMeasuringToolResult">
        <result property="id"    column="id"    />
        <result property="goodsId"    column="goods_id"    />
        <result property="goodsName"    column="goods_name"    />
        <result property="teamName"    column="team_name"    />
    </resultMap>

    <sql id="selectSyncMeasuringToolVo">
        select id, goods_id, goods_name, team_name from sync_measuring_tool
    </sql>

    <select id="selectSyncMeasuringToolList" parameterType="SyncMeasuringTool" resultMap="SyncMeasuringToolResult">
        <include refid="selectSyncMeasuringToolVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="goodsId != null  and goodsId != ''"> and goods_id = #{goodsId}</if>
            <if test="goodsName != null  and goodsName != ''"> and goods_name like concat('%', #{goodsName}, '%')</if>
            <if test="teamName != null  and teamName != ''"> and team_name like concat('%', #{teamName}, '%')</if>
        </where>
    </select>
    
    <select id="selectSyncMeasuringToolById" parameterType="Long" resultMap="SyncMeasuringToolResult">
        <include refid="selectSyncMeasuringToolVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertSyncMeasuringTool" parameterType="SyncMeasuringTool" useGeneratedKeys="true" keyProperty="id">
        insert into sync_measuring_tool
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="goodsName != null">goods_name,</if>
            <if test="teamName != null">team_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="goodsName != null">#{goodsName},</if>
            <if test="teamName != null">#{teamName},</if>
         </trim>
    </insert>

    <update id="updateSyncMeasuringTool" parameterType="SyncMeasuringTool">
        update sync_measuring_tool
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="goodsName != null">goods_name = #{goodsName},</if>
            <if test="teamName != null">team_name = #{teamName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSyncMeasuringToolById" parameterType="Long">
        update sync_measuring_tool set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteSyncMeasuringToolByIds" parameterType="String">
        update sync_measuring_tool set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>