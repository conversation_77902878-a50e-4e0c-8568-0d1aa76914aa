<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.sync.mapper.CableOrderInfoMapper">

    <select id="getMonthCount" parameterType="map" resultType="OrderMonthCount">
        SELECT
            FORMAT(CTime, 'yyyy-MM') AS month,
        COUNT(*) AS saleNO
        FROM
            SD_Cable_Sale_OrderInfo
        WHERE
            CTime >= #{startDate} AND CTime &lt;= #{endDate} AND IsDelete = 0
        GROUP BY
            FORMAT(CTime, 'yyyy-MM')
        ORDER BY
            FORMAT(CTime, 'yyyy-MM');
    </select>

    <select id="getYearCount" parameterType="map" resultType="OrderYearCount">
        SELECT
            FORMAT(CTime, 'yyyy') AS year,
        COUNT(*) AS saleNO
        FROM
            SD_Cable_Sale_OrderInfo
        WHERE
            CTime >= #{startDate} AND CTime &lt;= #{endDate} AND IsDelete = 0
        GROUP BY
            FORMAT(CTime, 'yyyy')
        ORDER BY
            FORMAT(CTime, 'yyyy');
    </select>

</mapper>