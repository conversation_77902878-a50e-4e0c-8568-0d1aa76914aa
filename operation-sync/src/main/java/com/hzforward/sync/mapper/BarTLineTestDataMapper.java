package com.hzforward.sync.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.sync.domain.BarTLineTestData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 条形T线测试数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
@DataSource(DataSourceType.SLAVE9)
public interface BarTLineTestDataMapper extends BaseMapper<BarTLineTestData> {

    /**
     * 查询条形T线测试数据列表
     * 使用Oracle数据源查询ILA6U_ZS0001_V230922表
     *
     * @param limit 查询条数限制
     * @return 测试数据列表
     */
    @Select("SELECT * FROM (SELECT createdTime, ShortText1695362815861 FROM ILA6U_ZS0001_V230922 ORDER BY createdTime DESC) WHERE ROWNUM <= #{limit}")
    List<Map<String, Object>> selectBarTLineTestDataList(@Param("limit") int limit);

    /**
     * 根据线体查询条形T线测试数据
     * 这里可以根据实际业务需求添加线体过滤条件
     *
     * @param line 线体名称
     * @param limit 查询条数限制
     * @return 测试数据列表
     */
    @Select("SELECT * FROM (SELECT createdTime, ShortText1695362815861 FROM ILA6U_ZS0001_V230922 ORDER BY createdTime DESC) WHERE ROWNUM <= #{limit}")
    List<Map<String, Object>> selectBarTLineTestDataByLine(@Param("line") String line, @Param("limit") int limit);
}
