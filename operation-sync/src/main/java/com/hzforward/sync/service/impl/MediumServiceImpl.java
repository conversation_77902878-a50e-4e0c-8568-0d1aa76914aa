package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.core.domain.entity.SysDictData;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.DictUtils;
import com.hzforward.oee.domain.baseData.machining.CncRealTimeInfoRow;
import com.hzforward.sync.domain.OrderInfo;
import com.hzforward.sync.domain.OrderMonthCount;
import com.hzforward.sync.domain.OrderYearCount;
import com.hzforward.oee.mapper.baseData.machining.CncRealTimeInfoRowMapper;
import com.hzforward.sync.mapper.OrderInfoMapper;
import com.hzforward.sync.service.IMediumService;
import com.hzforward.sync.util.ExcelUtils;
import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileInputStream;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 机座线PLC同步数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Service
@RequiredArgsConstructor
public class MediumServiceImpl implements IMediumService {

    private final OrderInfoMapper orderInfoMapper;
    private final CncRealTimeInfoRowMapper cncRealTimeInfoRowMapper;

    /**
     * 获取年销量列表
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE6)
    public JSONObject getYearSaleList() {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "order_total_list");
        JSONObject object = JSON.parseObject(date.getRemark());
        JSONObject resObj = new JSONObject();
        Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("2024-01-01 00:00:00", DateUtils.getNowDate()));
        Date endTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-12-31 23:59:59", DateUtils.getNowDate()));
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", startTime);
        params.put("endDate", endTime);
        List<OrderYearCount> orderMonthSaleList = orderInfoMapper.getYearCount(params);
        JSONArray years = object.getJSONArray("years");
        JSONArray saleNos = object.getJSONArray("saleNos");
        for (OrderYearCount orderYearCount : orderMonthSaleList) {
            years.add(orderYearCount.getYear());
            saleNos.add(orderYearCount.getSaleNo());
        }
        resObj.put("years", years);
        resObj.put("saleNos", saleNos);
        return resObj;
    }


    /**
     * 获取月销量数据
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE6)
    public JSONObject getMonthSaleList() {
        DateUtils.getNowDate();
        Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", DateUtils.getNowDate()));
        Date endTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-12-31 23:59:59", DateUtils.getNowDate()));
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", startTime);
        params.put("endDate", endTime);
        List<OrderMonthCount> orderMonthSaleList = orderInfoMapper.getMonthCount(params);
        // 使用 TreeMap 来按键（月份）排序
        Map<String, Integer> orderMonthMap = new TreeMap<>(Comparator.naturalOrder());
        for (OrderMonthCount order : orderMonthSaleList) {
            orderMonthMap.put(order.getMonth().substring(order.getMonth().length() - 2), order.getSaleNo());
        }
        // 转换为两个数组
        List<String> months = new ArrayList<>(orderMonthMap.keySet());
        List<Integer> counts = new ArrayList<>(orderMonthMap.values());
        // 生成结果
        JSONObject result = new JSONObject();
        result.put("months", months);
        result.put("saleNos", counts);
        return result;
    }

    /**
     * 获取区域销量统计
     *
     * @return
     */
    @Override
    public JSONObject getSectionSaleList() {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "section_sale_list");
        JSONObject object = JSON.parseObject(date.getRemark());
        JSONObject resObj = new JSONObject();
        resObj.put("sections", object.getJSONArray("sections"));
        resObj.put("saleNos", object.getJSONArray("saleNos"));
        return resObj;
    }

    /**
     * 获取订单多维度统计
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE6)
    public JSONObject getMultiDimensionalSaleList() {
        // 统计年
        Date yearStartTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", DateUtils.getNowDate()));
        Date yearEndTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-12-31 23:59:59", DateUtils.getNowDate()));
        Long yearCount = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfo>().ge(OrderInfo::getCTime, yearStartTime).le(OrderInfo::getCTime, yearEndTime));
        // 统计月
        Date monthStartTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-01 00:00:00", DateUtils.getNowDate()));
        Date monthEndTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-31 23:59:59", DateUtils.getNowDate()));
        monthEndTime = DateUtils.addDays(DateUtils.addMonths(monthEndTime, 1), -1);
        Long monthCount = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfo>().ge(OrderInfo::getCTime, monthStartTime).le(OrderInfo::getCTime, monthEndTime));
        // 统计日
        Date dayStartTime = DateUtils.getTodayStartTime(DateUtils.getNowDate());
        Date dayEndTime = DateUtils.getTodayLastTime(DateUtils.getNowDate());
        Long dayCount = orderInfoMapper.selectCount(new LambdaQueryWrapper<OrderInfo>().ge(OrderInfo::getCTime, dayStartTime).le(OrderInfo::getCTime, dayEndTime));
        // 累计
        SysDictData date = DictUtils.getDictCache("big_screen_data", "order_total_list");
        JSONObject object = JSON.parseObject(date.getRemark());
        JSONArray saleNos = object.getJSONArray("saleNos");
        Long count = 0L;
        for (int i = 0; i < saleNos.size(); i++) {
            count += saleNos.getLong(i);
        }
        count += yearCount;
        // 组合返回对象
        JSONObject result = new JSONObject();
        result.put("count", count);
        result.put("yearCount", yearCount);
        result.put("monthCount", monthCount);
        result.put("dayCount", dayCount);
        return result;
    }

    /**
     * 获取FPY
     *
     * @return
     */
    @Override
    public JSONObject getFpy() {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "fpy_data");
        return JSON.parseObject(date.getRemark());
    }

    /**
     * 获取FTB关闭率
     *
     * @return
     */
    @Override
    public JSONArray getFtbCloseRate() {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "ftb_close_rate");
        return JSON.parseArray(date.getRemark());
    }

    /**
     * 获取月度安全天数列表
     *
     * @return
     */
    @Override
    public JSONObject getMonthSecureDayList() {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "month_secure_day_list");
        return JSON.parseObject(date.getRemark());
    }

    /**
     * 获取隐患排查
     *
     * @return
     */
    @Override
    public JSONArray getHazardInvestigation() {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "hazard_investigation");
        return JSON.parseArray(date.getRemark());
    }

    /**
     * 获取质量柱状图
     *
     * @return
     */
    @Override
    public JSONObject getQualityList() {
        JSONObject result = new JSONObject();
        return result;
    }

    /**
     * 获取机加工排产完成率
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE7)
    public JSONObject getMachiningCompleteRate() {
        String networkPath = DictUtils.getDictValue("big_screen_data", "network_path");
        try {
            // jcifs 认证信息
            NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(
                    "elevator.xizi.local", "hu.hongjun", "hhjHHJ1314520."); // 请替换为你的用户名和密码
            SmbFile smbFile = new SmbFile(networkPath, auth);
            InputStream in = new SmbFileInputStream(smbFile);
            XSSFWorkbook workbook = new XSSFWorkbook(in);
            Sheet sheet = workbook.getSheet("Sheet1");
            List<List<String>> resultList = new ArrayList<>();
            if (sheet != null) {
                for (Row row : sheet) {
                    List<String> rowList = new ArrayList<>();
                    for (Cell cell : row) {
                        String cellValue = ExcelUtils.getCellValue(cell);
                        rowList.add(cellValue);
                    }
                    resultList.add(rowList);
                }
            }
            workbook.close();
            in.close();
            String[] lines = {"机座精车工段", "TD部件机座加工线", "TD部件曳引轮加工线"};
            List<BigDecimal> rates = new ArrayList<>();
            for (String line : lines) {
                int index = 0;
                for (int i = 0; i < resultList.get(0).size(); i++) {
                    if (resultList.get(0).get(i).equals(line)) index = i;
                }
                int productionNo = 0;
                int completeNo = 0;
                BigDecimal rate = new BigDecimal(0);
                for (int i = 0; i < resultList.size(); i++) {
                    if (i == 0) continue;
                    if (resultList.get(i).size() >= index + 1) {
                        if (DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.parseDate(resultList.get(i).get(0))).equals(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                            productionNo = (int) Double.parseDouble(resultList.get(i).get(index));
                        }
                    }
                }
                SysDictData date = DictUtils.getDictCache("big_screen_data", "line_device_contrast");
                JSONObject lineDeviceContrast = JSON.parseObject(date.getRemark());
                if (!lineDeviceContrast.containsKey(line)) {
                    throw new RuntimeException("线体不存在");
                }
                JSONArray baseProcessing = lineDeviceContrast.getJSONArray(line);

                //判断 当前时间是否超过7:30 如果超过就获取前一天7:30 否则获取当天7:30 作为开始时间
                Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd 07:30:00", DateUtils.getNowDate()));
                if (startTime.getTime() > System.currentTimeMillis()) {
                    startTime = DateUtils.addDays(startTime, -1);
                }

                for (int i = 0; i < baseProcessing.size(); i++) {
                    JSONObject device = baseProcessing.getJSONObject(i);
                    String deviceNo = device.getString("deviceId");
                    if (device.getBoolean("isCompute")) {
                        List<CncRealTimeInfoRow> start = cncRealTimeInfoRowMapper.selectList(Page.of(0, 1), new LambdaQueryWrapper<CncRealTimeInfoRow>()
                                .ge(CncRealTimeInfoRow::getCreateTime, startTime)
                                .le(CncRealTimeInfoRow::getCreateTime, DateUtils.getNowDate())
                                .eq(CncRealTimeInfoRow::getEquipNo, deviceNo).orderByAsc(CncRealTimeInfoRow::getCreateTime));
                        List<CncRealTimeInfoRow> end = cncRealTimeInfoRowMapper.selectList(Page.of(0, 1), new LambdaQueryWrapper<CncRealTimeInfoRow>()
                                .ge(CncRealTimeInfoRow::getCreateTime, startTime)
                                .le(CncRealTimeInfoRow::getCreateTime, DateUtils.getNowDate())
                                .eq(CncRealTimeInfoRow::getEquipNo, deviceNo).orderByDesc(CncRealTimeInfoRow::getCreateTime));
                        if (!end.isEmpty() && !start.isEmpty()) {
                            try {
                                if (deviceNo.equals("001")) {
                                    completeNo += ((end.get(0).getCncProducts() - start.get(0).getCncProducts()) / 2);
                                } else {
                                    completeNo += end.get(0).getCncProducts() - start.get(0).getCncProducts();
                                }
                            } catch (NumberFormatException e) {
                                continue;
                            }
                        }
                    }
                }
                if (productionNo != 0) {
                    rate = new BigDecimal(completeNo).divide(new BigDecimal(productionNo), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                }
                rates.add(rate);
            }
            JSONObject resObj = new JSONObject();
            resObj.put("baseRate", rates.get(0));
            resObj.put("partBaseRate", rates.get(1));
            resObj.put("partTractionSheaveRate", rates.get(2));
            return resObj;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取设备状态
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE7)
    public JSONObject getDeviceStatus() {
        return null;
    }
}
