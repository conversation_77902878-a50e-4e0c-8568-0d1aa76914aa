package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.core.domain.entity.SysDept;
import com.hzforward.common.core.domain.entity.SysDictData;
import com.hzforward.common.core.domain.entity.SysUser;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.DictUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.meter.domain.MeterTool;
import com.hzforward.meter.domain.MeterTorqueTest;
import com.hzforward.meter.mapper.MeterToolMapper;
import com.hzforward.meter.mapper.MeterTorqueTestMapper;
import com.hzforward.sign.domain.SignLine;
import com.hzforward.sign.domain.SignLineJob;
import com.hzforward.sign.domain.SignRecord;
import com.hzforward.sign.mapper.SignLineJobMapper;
import com.hzforward.sign.mapper.SignLineMapper;
import com.hzforward.sign.mapper.SignRecordMapper;
import com.hzforward.sync.domain.SyncPressurizationNaiya;
import com.hzforward.sync.domain.SyncPressurizationNaiyaBrake;
import com.hzforward.sync.domain.SyncProduction;
import com.hzforward.sync.domain.vo.*;
import com.hzforward.sync.mapper.*;
import com.hzforward.sync.service.ISyncStatService;
import com.hzforward.system.mapper.SysDeptMapper;
import com.hzforward.system.mapper.SysUserMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncStatServiceImpl implements ISyncStatService {

    @Resource
    private SyncPressurizationNaiyaMapper syncPressurizationNaiyaMapper;
    @Resource
    private SyncPressurizationNaiyaBrakeMapper syncPressurizationNaiyaBrakeMapper;
    @Resource
    private SyncProductionMapper syncProductionMapper;
    @Resource
    private SignLineMapper signLineMapper;
    @Resource
    private SignLineJobMapper signLineJobMapper;
    @Resource
    private SignRecordMapper signRecordMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private MeterToolMapper meterToolMapper;
    @Resource
    private MeterTorqueTestMapper meterTorqueTestMapper;
    @Resource
    private BarTLineTestDataMapper barTLineTestDataMapper;

    /**
     * 查询量具点检完成率
     *
     * @return
     */
    @Override
    public AjaxResult measuringToolInspectionCompletionRate(String line) {
        if ("DT线".equals(line)) {
            line = "3.2DS线外包";
        } else if ("碟式马达线".equals(line)) {
            line = "TDG主机总装线";
        }
        List<SysDept> list = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
                        .in(SysDept::getDeptName, line)
//                .eq(SysDept::getCompanyId, DictUtils.getDictValue("measuring_tool_config", "company_id"))
        );
        if (StringUtils.isEmpty(list)) {
            return AjaxResult.success(new ArrayList<>());
        }
        List<SysUser> userList = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>()
                .in(SysUser::getDeptId, list.stream().map(SysDept::getDeptId).collect(Collectors.toList()))
                .eq(SysUser::getDelFlag, 0));
        List<MeterTool> meterToolList = meterToolMapper.selectList(new LambdaQueryWrapper<MeterTool>()
                .in(MeterTool::getSafeguardPerson, userList.stream().map(SysUser::getUserName).collect(Collectors.toList()))
                .eq(MeterTool::getTorqueForceTestScope, 1)
        );
        List<MeterTorqueTest> testList = meterTorqueTestMapper.selectList(new LambdaQueryWrapper<MeterTorqueTest>()
                .ge(MeterTorqueTest::getCreateTime, DateUtils.getTodayStartTime(new Date()))
                .le(MeterTorqueTest::getCreateTime, DateUtils.getTodayLastTime(new Date()))
        );
        Map<String, MeterTorqueTest> testMap = testList.stream().collect(Collectors.toMap(MeterTorqueTest::getCode, meterTorqueTest -> meterTorqueTest, (oleO, newO) -> {
            if (newO.getResult().equals("合格")) {
                return newO;
            } else {
                return oleO;
            }
        }));
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(SysUser::getUserName, sysUser -> sysUser.getNickName(), (existing, replacement) -> replacement));
        List<MeasuringToolCheck> resList = BeanUtils.copyListPropAndReturn(meterToolList, MeasuringToolCheck.class);
        for (MeasuringToolCheck measuringToolCheck : resList) {
            measuringToolCheck.setSafeguardPersonName(userMap.get(measuringToolCheck.getSafeguardPerson()));
            if (StringUtils.isNotNull(testMap.get(measuringToolCheck.getCode()))) {
                measuringToolCheck.setDocimasyStatusName(testMap.get(measuringToolCheck.getCode()).getResult());
            } else {
                measuringToolCheck.setDocimasyStatusName("未检");
            }
        }
        MeasuringToolCheckRes measuringToolCheckRes = new MeasuringToolCheckRes();
        measuringToolCheckRes.setMeasuringToolCheckList(resList);
        measuringToolCheckRes.setTotal(resList.size());
        measuringToolCheckRes.setQualified(resList.stream().filter(measuringToolCheck -> measuringToolCheck.getDocimasyStatusName().equals("合格")).collect(Collectors.toList()).size());
        return AjaxResult.success(measuringToolCheckRes);
    }

    /**
     * 查询排产完成情况
     *
     * @return
     */
    @Override
    public ProductionComplete productionComplete(String line, Date date) {
        // 当日排产数量
        if (StringUtils.isNull(date)) {
            date = new Date();
        }
        Long productionCount = 0L;
        ProductionComplete res = new ProductionComplete();
        productionCount = syncProductionMapper.selectCount(new LambdaQueryWrapper<SyncProduction>()
                .ge(SyncProduction::getPlanPackDate, DateUtils.getTodayStartTime(date))
                .le(SyncProduction::getPlanPackDate, DateUtils.getTodayLastTime(date))
                .eq(SyncProduction::getWorkLine, line));
        // 排产扩展
        if ("柔性线".equals(line)) {
            productionCount += syncProductionMapper.selectCount(new LambdaQueryWrapper<SyncProduction>()
                    .ge(SyncProduction::getPlanPackDate, DateUtils.getTodayStartTime(date))
                    .le(SyncProduction::getPlanPackDate, DateUtils.getTodayLastTime(date))
                    .and(wrapper -> wrapper
                            .like(SyncProduction::getModelName, "PMS320")
                            .or()
                            .like(SyncProduction::getModelName, "GM9S")
                            .or()
                            .like(SyncProduction::getModelName, "PMS300")
                    )
            );
        } else if ("DT线".equals(line)) {
            productionCount = 0L;
            productionCount += syncProductionMapper.selectCount(new LambdaQueryWrapper<SyncProduction>()
                    .ge(SyncProduction::getPlanPackDate, DateUtils.getTodayStartTime(date))
                    .le(SyncProduction::getPlanPackDate, DateUtils.getTodayLastTime(date))
                    .eq(SyncProduction::getWorkLine, "条形线-D")
            );
        } else if ("碟式马达线".equals(line)) {
            productionCount = 0L;
            productionCount += syncProductionMapper.selectCount(new LambdaQueryWrapper<SyncProduction>()
                    .ge(SyncProduction::getPlanPackDate, DateUtils.getTodayStartTime(date))
                    .le(SyncProduction::getPlanPackDate, DateUtils.getTodayLastTime(date))
                    .in(SyncProduction::getWorkLine, "条形线", "碟式马达线")
            );
        } else if ("制动器线".equals(line)) {
            productionCount = 0L;
            EveryHourProduce everyHourProduceList = everyHourProduce("制动器线", null, 22);
            for (Long l : everyHourProduceList.getTheoryList()) {
                productionCount += l;
            }
        }
        res.setPlanNo(productionCount);
        // 当日完成数量
        Long completeCount = 0L;
        if ("制动器线".equals(line)) {
            completeCount = syncPressurizationNaiyaBrakeMapper.selectCount(new LambdaQueryWrapper<SyncPressurizationNaiyaBrake>()
                    .ge(SyncPressurizationNaiyaBrake::getCsrqsj, DateUtils.getTodayStartTime(date))
                    .le(SyncPressurizationNaiyaBrake::getCsrqsj, DateUtils.getTodayLastTime(date))
                    .eq(SyncPressurizationNaiyaBrake::getCszjg, "OK")
            );
        } else {
            completeCount = syncPressurizationNaiyaMapper.selectCount(new LambdaQueryWrapper<SyncPressurizationNaiya>()
                    .ge(SyncPressurizationNaiya::getCsrqsj, DateUtils.getTodayStartTime(date))
                    .le(SyncPressurizationNaiya::getCsrqsj, DateUtils.getTodayLastTime(date))
                    .eq(SyncPressurizationNaiya::getLine, line)
                    .eq(SyncPressurizationNaiya::getCszjg, "OK")
            );
        }

        res.setCompleteNo(completeCount);
        if (productionCount != 0) {
            res.setCompleteRate(new BigDecimal(completeCount).divide(new BigDecimal(productionCount), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).doubleValue());
        } else {
            res.setCompleteRate(0.0);
        }
        return res;
    }

    /**
     * 查询当天生产进度
     *
     * @return
     */
    @Override
    public AjaxResult toDayProductionSchedule(String line, Date date) {
        if (date == null) {
            date = new Date();
        }
        //0生产1休息
        //生成理论产能
        if (StringUtils.isNotEmpty(line)) {
            AjaxResult.error("线体不能为空");
        }
        SysDictData dict = DictUtils.getDictCache("produce_bulletin_board_config", "rest");
        String value = DictUtils.getDictValue("produce_bulletin_board_config", line + "节拍");
        int jp = (int) (Double.parseDouble(value) * 1000);
        JSONArray workTimeArr = JSON.parseArray(dict.getRemark());
        ToDayProductionScheduleTotal toDayProductionScheduleTotal = new ToDayProductionScheduleTotal();
        //理论产能
        List<ToDayProductionSchedule> theoryProductionList = new ArrayList<>();

        List<ToDayProductionScheduleInterval> toDayProductionScheduleIntervalList = new ArrayList<>();
        for (int i = 0; i < workTimeArr.size(); i++) {
            JSONArray arr = workTimeArr.getJSONArray(i);
            Calendar start = Calendar.getInstance();
            start.setTime(date);
            start.set(Calendar.HOUR_OF_DAY, arr.getIntValue(0));
            start.set(Calendar.MINUTE, arr.getIntValue(1));
            start.set(Calendar.SECOND, 0);
            ToDayProductionScheduleInterval toDayProductionScheduleInterval = new ToDayProductionScheduleInterval();
            toDayProductionScheduleInterval.setStartTime(start.getTime());
            toDayProductionScheduleInterval.setEndTime(DateUtils.addMinutes(start.getTime(), arr.getIntValue(2)));
            toDayProductionScheduleInterval.setType(arr.getIntValue(3));
            toDayProductionScheduleIntervalList.add(toDayProductionScheduleInterval);
        }
        Date startTime = new Date(toDayProductionScheduleIntervalList.get(0).getStartTime().getTime());
        Date endTime = toDayProductionScheduleIntervalList.get(toDayProductionScheduleIntervalList.size() - 1).getEndTime();
        int productionNo = 0;
        while (startTime.getTime() <= endTime.getTime()) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startTime);
            calendar.add(Calendar.MILLISECOND, jp);
            startTime = calendar.getTime();
            for (ToDayProductionScheduleInterval toDayProductionScheduleInterval : toDayProductionScheduleIntervalList) {
                if (startTime.getTime() >= toDayProductionScheduleInterval.getStartTime().getTime() && startTime.getTime() <= toDayProductionScheduleInterval.getEndTime().getTime()) {
                    if (toDayProductionScheduleInterval.getType() == 0) {
                        productionNo++;
                    }
                    ToDayProductionSchedule toDayProductionSchedule = new ToDayProductionSchedule();
                    toDayProductionSchedule.setCurrentTime(startTime);
                    toDayProductionSchedule.setCurrentTimeStr(DateUtils.parseDateToStr(DateUtils.HH_MM, startTime));
                    toDayProductionSchedule.setProductionNo(productionNo);
                    theoryProductionList.add(toDayProductionSchedule);
                    break;
                }
            }
        }
        toDayProductionScheduleTotal.setProductionList(theoryProductionList);
        //实际产能
        List<SyncProduction> productionList = syncProductionMapper.selectList(new LambdaQueryWrapper<SyncProduction>()
                .ge(SyncProduction::getPlanPackDate, DateUtils.getTodayStartTime(date))
                .le(SyncProduction::getPlanPackDate, DateUtils.getTodayLastTime(date))
                .eq(SyncProduction::getWorkLine, line));
        List<SyncPressurizationNaiya> naiyaList = new ArrayList<>();
        List<SyncPressurizationNaiyaBrake> naiyaBrakeList = new ArrayList<>();
        if ("制动器线".equals(line)) {
            naiyaBrakeList = syncPressurizationNaiyaBrakeMapper.selectList(new LambdaQueryWrapper<>(new SyncPressurizationNaiyaBrake())
                    .eq(SyncPressurizationNaiyaBrake::getCsrq, DateUtils.getTodayStartTime(date)));
            for (int i = naiyaList.size() - 1; i >= 0; i--) {
                if (!"OK".equals(naiyaList.get(i).getCszjg())) {
                    naiyaList.remove(i);
                }
            }
        } else {
            naiyaList = syncPressurizationNaiyaMapper.selectList(new LambdaQueryWrapper<>(new SyncPressurizationNaiya())
                    .eq(SyncPressurizationNaiya::getCsrq, DateUtils.getTodayStartTime(date))
                    .eq(SyncPressurizationNaiya::getLine, line));
            for (int i = naiyaList.size() - 1; i >= 0; i--) {
                if (!"OK".equals(naiyaList.get(i).getCszjg())) {
                    naiyaList.remove(i);
                }
            }
        }

        int productionNo1 = 0;
        for (ToDayProductionSchedule toDayProductionSchedule : theoryProductionList) {
            if (DateUtils.getNowDate().getTime() > toDayProductionSchedule.getCurrentTime().getTime()) {
                if ("制动器线".equals(line)) {
                    for (int i = naiyaBrakeList.size() - 1; i >= 0; i--) {
                        if (toDayProductionSchedule.getCurrentTime().getTime() >= naiyaBrakeList.get(i).getCsrqsj().getTime()) {
                            productionNo1++;
                            naiyaBrakeList.remove(i);
                        }
                    }
                } else {
                    for (int i = naiyaList.size() - 1; i >= 0; i--) {
                        if (toDayProductionSchedule.getCurrentTime().getTime() >= naiyaList.get(i).getCsrqsj().getTime()) {
                            productionNo1++;
                            naiyaList.remove(i);
                        }
                    }
                }
                toDayProductionSchedule.setRealityNo(productionNo1);
            } else {
                toDayProductionSchedule.setRealityNo(null);
            }
        }
        return AjaxResult.success(toDayProductionScheduleTotal);
    }

    /**
     * 每小时产出和实际理论差异
     *
     * @return
     */
    @Override
    public EveryHourProduce everyHourProduce(String line, Date date, Integer nowHour) {
        AjaxResult res = this.toDayProductionSchedule(line, date);
        ToDayProductionScheduleTotal data = (ToDayProductionScheduleTotal) res.get("data");
        List<ToDayProductionSchedule> theoryProductionList = data.getProductionList();
        SysDictData dict = DictUtils.getDictCache("produce_bulletin_board_config", "rest");
        JSONArray workTimeArr = JSON.parseArray(dict.getRemark());
        int startHour = workTimeArr.getJSONArray(0).getIntValue(0);
        JSONArray endArr = workTimeArr.getJSONArray(workTimeArr.size() - 1);
        int endHour = endArr.getIntValue(0) + ((endArr.getIntValue(1) + endArr.getIntValue(2)) / 60);

        List<Integer> hourList = new ArrayList<>();
        List<Long> theoryList = new ArrayList<>();
        List<Long> realityList = new ArrayList<>();
        List<Double> rateDataList = new ArrayList<>();

        if (nowHour == null) {
            nowHour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        }

        List<ToDayProductionSchedule> theoryProductionNewList = new ArrayList<>();
        theoryProductionNewList.addAll(theoryProductionList);

        for (int i = startHour; i < endHour; i++) {
            long theoryProductionNo = 0;
            long realityProductionNo = 0;

            for (int i1 = theoryProductionList.size() - 1; i1 >= 0; i1--) {
                if (theoryProductionList.get(i1).getCurrentTime().getHours() == i) {
                    if (theoryProductionNo < theoryProductionList.get(i1).getProductionNo()) {
                        theoryProductionNo = theoryProductionList.get(i1).getProductionNo();
                    }
                    theoryProductionList.remove(i1);
                }
            }
            for (int i1 = theoryProductionNewList.size() - 1; i1 >= 0; i1--) {
                if (theoryProductionNewList.get(i1).getCurrentTime().getHours() == i) {
                    if (StringUtils.isNotNull(theoryProductionNewList.get(i1).getRealityNo()) && realityProductionNo < theoryProductionNewList.get(i1).getRealityNo()) {
                        realityProductionNo = theoryProductionNewList.get(i1).getRealityNo();
                    }
                    theoryProductionNewList.remove(i1);
                }
            }
            hourList.add(i + 1);
            theoryList.add(theoryProductionNo);
            if (i < nowHour + 1) {
                realityList.add(realityProductionNo);
                if (theoryProductionNo == 0) {
                    rateDataList.add(0d);
                } else {
                    rateDataList.add(new BigDecimal(realityProductionNo).divide(new BigDecimal(theoryProductionNo), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).doubleValue());
                }
            }
        }

        for (int i = hourList.size() - 1; i > 0; i--) {
            theoryList.set(i, theoryList.get(i) - theoryList.get(i - 1));
            if (rateDataList.size() > i) {
                realityList.set(i, realityList.get(i) - realityList.get(i - 1));
                rateDataList.set(i, new BigDecimal(realityList.get(i)).divide(new BigDecimal(theoryList.get(i)), 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).doubleValue());
            }
        }

        EveryHourProduce everyHourProduce = new EveryHourProduce();
        everyHourProduce.setHourList(hourList);
        everyHourProduce.setTheoryList(theoryList);
        everyHourProduce.setRealityList(realityList);
        everyHourProduce.setRateList(rateDataList);
        return everyHourProduce;
    }

    /**
     * 当前生产订单明细
     *
     * @return
     */
    @Override
    public AjaxResult currentProduceOrderInfo(String line) {
        List<CurrentProduceOrderInfo> currentProduceOrderInfoList = new ArrayList<>();
        List<String> scbhs = new ArrayList<>();
        if ("制动器线".equals(line)) {
            List<SyncPressurizationNaiyaBrake> syncPressurizationNaiyaBarkeList = syncPressurizationNaiyaBrakeMapper.selectList(new LambdaQueryWrapper<SyncPressurizationNaiyaBrake>()
                    .ge(SyncPressurizationNaiyaBrake::getCsrqsj, DateUtils.getTodayStartTime(new Date()))
                    .le(SyncPressurizationNaiyaBrake::getCsrqsj, DateUtils.getTodayLastTime(new Date()))
                    .orderByDesc(SyncPressurizationNaiyaBrake::getCsrqsj));
            if (StringUtils.isEmpty(syncPressurizationNaiyaBarkeList)) {
                return AjaxResult.success(new ArrayList<>());
            }
            scbhs = syncPressurizationNaiyaBarkeList.stream().map(SyncPressurizationNaiyaBrake::getScbh).collect(Collectors.toList());
        } else {
            List<SyncPressurizationNaiya> syncPressurizationNaiyaList = syncPressurizationNaiyaMapper.selectList(new LambdaQueryWrapper<SyncPressurizationNaiya>()
                    .ge(SyncPressurizationNaiya::getCsrqsj, DateUtils.getTodayStartTime(new Date()))
                    .le(SyncPressurizationNaiya::getCsrqsj, DateUtils.getTodayLastTime(new Date()))
                    .eq(SyncPressurizationNaiya::getLine, line)
                    .orderByDesc(SyncPressurizationNaiya::getCsrqsj));
            if (StringUtils.isEmpty(syncPressurizationNaiyaList)) {
                return AjaxResult.success(new ArrayList<>());
            }
            scbhs = syncPressurizationNaiyaList.stream().map(SyncPressurizationNaiya::getScbh).collect(Collectors.toList());
        }
        LambdaQueryWrapper<SyncProduction> lqw = new LambdaQueryWrapper<SyncProduction>()
                .in(SyncProduction::getSerialNo, scbhs);
        List<SyncProduction> syncProductionList = syncProductionMapper.selectList(lqw);
        //根据scbhs排序
        List<String> finalScbhs = scbhs;
        syncProductionList.sort(Comparator.comparingInt(o -> finalScbhs.indexOf(o.getSerialNo())));
        //取前6条
        syncProductionList = syncProductionList.stream().limit(10).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(syncProductionList)) {
            for (SyncProduction syncProduction : syncProductionList) {
                CurrentProduceOrderInfo currentProduceOrderInfo = new CurrentProduceOrderInfo();
                currentProduceOrderInfo.setSerialNo(syncProduction.getSerialNo());
//                currentProduceOrderInfo.setOrderNo(syncProduction.getOrderNo());
                currentProduceOrderInfo.setContractNo(syncProduction.getContractNo());
                currentProduceOrderInfo.setDeliveryCompany(syncProduction.getDeliveryCompany());
                if (StringUtils.isNotNull(syncProduction.getPlanDeliveryDate())) {
                    currentProduceOrderInfo.setProductionDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, syncProduction.getPlanDeliveryDate()));
                }
                if (StringUtils.isNotNull(syncProduction.getBatch())) {
                    currentProduceOrderInfo.setProductionBatch(syncProduction.getBatch());
                } else {
                    currentProduceOrderInfo.setProductionBatch("");
                }
                currentProduceOrderInfoList.add(currentProduceOrderInfo);
            }
//            currentProduceOrderInfoList = BeanUtils.copyListPropAndReturn(syncProductionList,CurrentProduceOrderInfo.class);
        }
        return AjaxResult.success(currentProduceOrderInfoList);
    }

    /**
     * 查询各个线体到岗情况
     *
     * @return
     */
    @Override
    public AjaxResult lineOnDuty(String line) {
        Date startTime = DateUtils.getTodayStartTime(new Date());
        Date endTime = DateUtils.getTodayLastTime(new Date());
        SignLine signLine = signLineMapper.selectOne(new LambdaQueryWrapper<SignLine>()
                .eq(SignLine::getName, line)
                .eq(SignLine::getDelFlag, "0"));
        if (StringUtils.isNull(signLine)) {
            return AjaxResult.error("未查询到该线体到岗情况");
        }
        List<SignLineJob> jobList = signLineJobMapper.selectList(new LambdaQueryWrapper<SignLineJob>()
                .eq(SignLineJob::getLineId, signLine.getId())
                .eq(SignLineJob::getDelFlag, "0"));
        List<SignRecord> recordList = signRecordMapper.selectList(new LambdaQueryWrapper<SignRecord>()
                .eq(SignRecord::getDelFlag, "0")
                .ge(SignRecord::getCreateTime, startTime)
                .le(SignRecord::getCreateTime, endTime));
        Map<Long, SignRecord> recordMap = recordList.stream().collect(Collectors.toMap(SignRecord::getJobId, signRecord -> signRecord));
        //计算实到人数
        int actualNo = 0;
        for (SignLineJob signLineJob : jobList) {
            if (StringUtils.isNotNull(recordMap.get(signLineJob.getId()))) {
                actualNo++;
            }
        }
        //查询线体负责人
        SysUser sysUser = sysUserMapper.selectById(signLine.getLineSuperintendent());
        AssemblyLine assemblyLine = BeanUtils.copyBeanPropAndReturn(signLine, AssemblyLine.class);
        if (StringUtils.isNotNull(sysUser)) {
            assemblyLine.setLineSuperintendentName(sysUser.getNickName());
        }
        if (StringUtils.isNotEmpty(jobList)) {
            assemblyLine.setToDutyRate(new BigDecimal(actualNo).divide(new BigDecimal(jobList.size())).multiply(new BigDecimal(100)).doubleValue());
        } else {
            assemblyLine.setToDutyRate(0.0);
        }
        SysDictData toDutyRate = DictUtils.getDictCache("produce_bulletin_board_config", "to_duty_rate");
        if ("1".equals(toDutyRate.getRemark())) {
            assemblyLine.setToDutyRate(new BigDecimal(toDutyRate.getDictValue()).doubleValue());
        }
        return AjaxResult.success(assemblyLine);
    }

    /**
     * 查询耐压测试数据
     *
     * @param line
     * @return
     */
    @Override
    public AjaxResult getPressureResistanceTestData(String line) {
        JSONArray resArr = new JSONArray();
        List<SyncPressurizationNaiya> syncPressurizationNaiyaList = syncPressurizationNaiyaMapper.selectList(Page.of(0, 20), new LambdaQueryWrapper<SyncPressurizationNaiya>()
                .eq(SyncPressurizationNaiya::getLine, line)
                .orderByDesc(SyncPressurizationNaiya::getCsrqsj));
        if (StringUtils.isEmpty(syncPressurizationNaiyaList)) {
            return AjaxResult.success(new ArrayList<>());
        }
        for (SyncPressurizationNaiya pressurizationNaiya : syncPressurizationNaiyaList) {
            JSONArray resObj = new JSONArray();
            resObj.add(pressurizationNaiya.getAbh());
            resObj.add(pressurizationNaiya.getRp() + "%");
            resObj.add(pressurizationNaiya.getNyjg());
            resObj.add(pressurizationNaiya.getRmjg());
            resObj.add(pressurizationNaiya.getRm1jg());
            resObj.add(pressurizationNaiya.getJyr() + " MΩ");
//            resObj.add(pressurizationNaiya.getJyjg());
            resObj.add(pressurizationNaiya.getRm2jg());
            resArr.add(resObj);
        }
        return AjaxResult.success(resArr);
    }

    /**
     * 获取工时统计
     *
     * @param line
     * @return
     */
    @Override
    public WorkHourStatistics getWorkHourStatistics(String line, Date date) {
        // 获取节拍 毫秒
        SysDictData modelBeatDict = DictUtils.getDictCache("produce_bulletin_board_config", line + "机型节拍");
        if (StringUtils.isNull(modelBeatDict)) {
            return null;
        }
        JSONArray modelBeatArr = JSON.parseArray(modelBeatDict.getRemark());
        // 当日排产数量
        if (StringUtils.isNull(date)) {
            date = new Date();
        }
        ProductionComplete res = new ProductionComplete();
        List<SyncPressurizationNaiya> naiyaList = syncPressurizationNaiyaMapper.selectList(new LambdaQueryWrapper<SyncPressurizationNaiya>()
                .ge(SyncPressurizationNaiya::getCsrqsj, DateUtils.getTodayStartTime(date))
                .le(SyncPressurizationNaiya::getCsrqsj, DateUtils.getTodayLastTime(date))
                .eq(SyncPressurizationNaiya::getLine, line)
                .eq(SyncPressurizationNaiya::getCszjg, "OK")
                .orderByDesc(SyncPressurizationNaiya::getCsrqsj));
        naiyaList = naiyaList.stream().filter(signPressurizationNaiya -> StringUtils.isNotEmpty(signPressurizationNaiya.getXh()) && !signPressurizationNaiya.getXh().contains("点检")).collect(Collectors.toList());
        List<String> factoryNos = naiyaList.stream().map(SyncPressurizationNaiya::getScbh).collect(Collectors.toList());
        List<SyncProduction> prouctionList = syncProductionMapper.selectList(new QueryWrapper<SyncProduction>().in("serial_no", factoryNos));
        Map<String, String> prouctionMap = prouctionList.stream().collect(Collectors.toMap(SyncProduction::getSerialNo, SyncProduction::getModelName));
        WorkHourStatistics workHourStatistics = new WorkHourStatistics();
        // 实际开始时间
        workHourStatistics.setActualStartTime(naiyaList.get(naiyaList.size() - 1).getCsrqsj());
        // 实际结束时间
        workHourStatistics.setActualEndTime(naiyaList.get(0).getCsrqsj());
        // 获取作息时间
        SysDictData dict = DictUtils.getDictCache("produce_bulletin_board_config", "rest");
        JSONArray workTimeArr = JSON.parseArray(dict.getRemark());
        int startHour = workTimeArr.getJSONArray(0).getIntValue(0);
        int startMinute = workTimeArr.getJSONArray(0).getIntValue(1);
        // 计算总休息时长
        int restMinute = 0;
        for (int i = 0; i < workTimeArr.size(); i++) {
            JSONArray timeArr = workTimeArr.getJSONArray(i);
            if (timeArr.getInteger(3) == 1) {
                restMinute += timeArr.getInteger(2);
            }
        }
        // 理论开始时间
        Date theoryStartTime = DateUtils.getTodayStartTime(date);
        theoryStartTime = DateUtils.addHours(theoryStartTime, startHour);
        theoryStartTime = DateUtils.addMinutes(theoryStartTime, startMinute);
        // 理论运行时间
        int theoryRunSecond = 0;
        for (SyncPressurizationNaiya syncProduction : naiyaList) {
            String factoryNo = prouctionMap.get(syncProduction.getScbh());
            if (StringUtils.isNotEmpty(factoryNo)) {
                factoryNo = factoryNo.split("-")[0];
            }
            theoryRunSecond += getWorkSecond(modelBeatArr, factoryNo, 1);
        }
        workHourStatistics.setTheoryRunTime(theoryRunSecond);
        // 理论结束时间
        Date theoryEndTime = DateUtils.addMinutes(DateUtils.addSeconds(theoryStartTime, theoryRunSecond), restMinute);
        workHourStatistics.setTheoryEndTime(theoryEndTime);
        // 实际运行时间
        int actualRunSecond = DateUtils.differentSecondByMillisecond(workHourStatistics.getActualStartTime(), workHourStatistics.getActualEndTime());
        actualRunSecond -= restMinute * 60;
        workHourStatistics.setActualRunTime(actualRunSecond);
        // 时间利用率
        BigDecimal timeRate = new BigDecimal(theoryRunSecond).divide(new BigDecimal(actualRunSecond), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        workHourStatistics.setTimeUseRate(timeRate);
        // 理论总工时
        String personNo = DictUtils.getDictValue("produce_bulletin_board_config", line + "人数");
        int theoryTotalSecond = 0;
        for (SyncPressurizationNaiya syncProduction : naiyaList) {
            String factoryNo = prouctionMap.get(syncProduction.getScbh());
            if (StringUtils.isNotEmpty(factoryNo)) {
                factoryNo = factoryNo.split("-")[0];
            }
            theoryTotalSecond += getWorkSecond(modelBeatArr, factoryNo, 2);
        }
        workHourStatistics.setTheoryTotalWorkTime(theoryTotalSecond);
        // 实际总工时
        int actualTotalSecond = actualRunSecond * Integer.parseInt(personNo);
        workHourStatistics.setActualTotalWorkTime(actualTotalSecond);
        // 工时利用率
        BigDecimal workTimeRate = new BigDecimal(theoryTotalSecond).divide(new BigDecimal(actualTotalSecond), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
        workHourStatistics.setWorkTimeRate(workTimeRate);
        return workHourStatistics;
    }

    private int getWorkSecond(JSONArray modelBeatArr, String model, int index) {
        for (int i = 0; i < modelBeatArr.size(); i++) {
            JSONArray arr = modelBeatArr.getJSONArray(i);
            if (arr.get(0).equals(model)) {
                return arr.getInteger(index);
            }
        }
        return modelBeatArr.getJSONArray(0).getInteger(index);
    }
}
