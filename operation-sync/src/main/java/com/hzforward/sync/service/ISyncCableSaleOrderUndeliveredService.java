package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.sync.domain.SyncCableSaleOrderUndelivered;

/**
 * 销售订单未交统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-05
 */
public interface ISyncCableSaleOrderUndeliveredService 
{
    /**
     * 查询销售订单未交统计
     * 
     * @param id 销售订单未交统计主键
     * @return 销售订单未交统计
     */
    SyncCableSaleOrderUndelivered selectSyncCableSaleOrderUndeliveredById(Long id);

    /**
     * 查询销售订单未交统计列表
     * 
     * @param syncCableSaleOrderUndelivered 销售订单未交统计
     * @return 销售订单未交统计集合
     */
    List<SyncCableSaleOrderUndelivered> selectSyncCableSaleOrderUndeliveredList(SyncCableSaleOrderUndelivered syncCableSaleOrderUndelivered);

    /**
     * 新增销售订单未交统计
     * 
     * @param syncCableSaleOrderUndelivered 销售订单未交统计
     * @return 结果
     */
    int insertSyncCableSaleOrderUndelivered(SyncCableSaleOrderUndelivered syncCableSaleOrderUndelivered);

    /**
     * 修改销售订单未交统计
     * 
     * @param syncCableSaleOrderUndelivered 销售订单未交统计
     * @return 结果
     */
    int updateSyncCableSaleOrderUndelivered(SyncCableSaleOrderUndelivered syncCableSaleOrderUndelivered);

    /**
     * 删除销售订单未交统计
     * 
     * @param ids 需要删除的销售订单未交统计主键集合
     * @return 结果
     */
    int deleteSyncCableSaleOrderUndeliveredByIds(List<Long> ids);

    /**
     * 导入
     * @param cableProductionCompletes
     * @return
     */
    String importData(List<SyncCableSaleOrderUndelivered> cableProductionCompletes);


}
