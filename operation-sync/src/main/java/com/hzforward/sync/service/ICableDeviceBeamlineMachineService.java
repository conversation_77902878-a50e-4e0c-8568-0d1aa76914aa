package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.baseData.cable.BeamLine;

import java.util.List;

/**
 * 电缆-束线机650高速绞Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-15
 */
public interface ICableDeviceBeamlineMachineService extends IService<BeamLine>
{
    /**
     * 查询电缆-束线机650高速绞列表
     * 
     * @param beamLine 电缆-束线机650高速绞
     * @return 电缆-束线机650高速绞集合
     */
    List<BeamLine> selectCableDeviceBeamlineMachineList(BeamLine beamLine);

    /**
     * 当前线速
     * @return
     */
    JSONObject currentLineSpeed();

    /**
     * 当前节距
     * @return
     */
    JSONObject currentPitch();

    /**
     * 当前张力
     * @return
     */
    JSONObject currentTension();

    /**
     * 详情列表
     * @return
     */
    JSONArray detailList();
}
