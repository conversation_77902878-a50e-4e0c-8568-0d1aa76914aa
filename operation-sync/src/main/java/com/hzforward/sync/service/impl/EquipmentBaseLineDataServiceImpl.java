package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.host.CleanBaseLine;
import com.hzforward.oee.mapper.baseData.host.CleanBaseLineMapper;
import com.hzforward.sync.service.IEquipmentBaseLineDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 机座线PLC同步数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Service
@RequiredArgsConstructor
public class EquipmentBaseLineDataServiceImpl extends ServiceImpl<CleanBaseLineMapper, CleanBaseLine> implements IEquipmentBaseLineDataService {
    private final CleanBaseLineMapper cleanBaseLineMapper;

    /**
     * 查询机座线PLC同步数据列表
     *
     * @param cleanBaseLine 机座线PLC同步数据
     * @return 机座线PLC同步数据
     */
    @Override
    public List<CleanBaseLine> selectEquipmentBaseLineDataList(CleanBaseLine cleanBaseLine) {
        return cleanBaseLineMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<CleanBaseLine>()

        );
    }

    /**
     * 查询粗洗PH值折线图
     *
     * @return
     */
    @Override
    public JSONObject roughWashingPh() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        List<String> frontPhs = new ArrayList<>();
        List<String> oppositePhs = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            phs.add(String.format("%.2f", cleanBaseLine.getVd700()));
            frontPhs.add(String.format("%.2f", cleanBaseLine.getVd736()));
            oppositePhs.add(String.format("%.2f", cleanBaseLine.getVd748()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        jsonObject.put("twoList", frontPhs);
        jsonObject.put("threeList", oppositePhs);
        return jsonObject;
    }

    /**
     * 查询粗洗温度折线图
     *
     * @return
     */
    @Override
    public JSONObject roughWashingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanBaseLine.getVd94()));
            realitys.add(String.format("%.2f", cleanBaseLine.getVd50()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询预防锈PH
     *
     * @return
     */
    @Override
    public JSONObject preventingRustPh() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        List<String> frontPhs = new ArrayList<>();
        List<String> oppositePhs = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            phs.add(String.format("%.2f", cleanBaseLine.getVd708()));
            frontPhs.add(String.format("%.2f", cleanBaseLine.getVd744()));
            oppositePhs.add(String.format("%.2f", cleanBaseLine.getVd756()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        jsonObject.put("twoList", frontPhs);
        jsonObject.put("threeList", oppositePhs);
        return jsonObject;
    }

    /**
     * 查询预防锈温度
     *
     * @return
     */
    @Override
    public JSONObject preventingRustTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanBaseLine.getVd102()));
            realitys.add(String.format("%.2f", cleanBaseLine.getVd58()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询精洗PH值折线图
     *
     * @return
     */
    @Override
    public JSONObject fineWashingPh() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        List<String> frontPhs = new ArrayList<>();
        List<String> oppositePhs = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            phs.add(String.format("%.2f", cleanBaseLine.getVd704()));
            frontPhs.add(String.format("%.2f", cleanBaseLine.getVd740()));
            oppositePhs.add(String.format("%.2f", cleanBaseLine.getVd752()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        jsonObject.put("twoList", frontPhs);
        jsonObject.put("threeList", oppositePhs);
        return jsonObject;
    }

    /**
     * 查询精洗温度
     *
     * @return
     */
    @Override
    public JSONObject fineWashingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanBaseLine.getVd98()));
            realitys.add(String.format("%.2f", cleanBaseLine.getVd54()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询强冷室温度折线图
     *
     * @return
     */
    @Override
    public JSONObject strongColdChamberTemperature() {
        List<String> times = new ArrayList<>();
        List<Short> sets = new ArrayList<>();
        List<Short> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            sets.add(cleanBaseLine.getVw2());
            realitys.add(cleanBaseLine.getVw0());
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
//        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询脱水烘干温度
     *
     * @return
     */
    @Override
    public JSONObject dehydrationDryingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanBaseLine.getVd110()));
            realitys.add(String.format("%.2f", cleanBaseLine.getVd66()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询油漆烘干温度
     *
     * @return
     */
    @Override
    public JSONObject paintDryingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        List<String> frontPhs = new ArrayList<>();
        List<String> oppositePhs = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanBaseLine> equipmentRotorLineDataList = cleanBaseLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanBaseLine>().orderByDesc(CleanBaseLine::getCreateTime));
        for (CleanBaseLine cleanBaseLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanBaseLine.getCreateTime()));
            phs.add(String.format("%.2f", cleanBaseLine.getVd118()));
            frontPhs.add(String.format("%.2f", cleanBaseLine.getVd78()));
            oppositePhs.add(String.format("%.2f", cleanBaseLine.getVd82()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        jsonObject.put("twoList", frontPhs);
        jsonObject.put("threeList", oppositePhs);
        return jsonObject;
    }
}
