package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.host.CleanFlexibleLine;
import com.hzforward.oee.domain.baseData.host.CleanRollerLine;
import com.hzforward.oee.mapper.baseData.host.CleanFlexibleLineMapper;
import com.hzforward.oee.mapper.baseData.host.CleanRollerLineMapper;
import com.hzforward.sync.service.IEquipmentFlexibleLineDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 柔性线PLC同步数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-09
 */
@Service
@RequiredArgsConstructor
public class EquipmentFlexibleLineDataServiceImpl extends ServiceImpl<CleanFlexibleLineMapper, CleanFlexibleLine> implements IEquipmentFlexibleLineDataService
{
    private final CleanFlexibleLineMapper cleanFlexibleLineMapper;

    private final CleanRollerLineMapper cleanRollerLineMapper;

    /**
     * 查询柔性线PLC同步数据列表
     *
     * @param cleanFlexibleLine 柔性线PLC同步数据
     * @return 柔性线PLC同步数据
     */
    @Override
    public List<CleanFlexibleLine> selectEquipmentFlexibleLineDataList(CleanFlexibleLine cleanFlexibleLine)
    {
        return cleanFlexibleLineMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<CleanFlexibleLine>()

        );
    }

    /**
     * 查询粗洗温度
     * @return
     */
    @Override
    public JSONObject roughWashingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanFlexibleLine> equipmentRotorLineDataList = cleanFlexibleLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanFlexibleLine>().orderByDesc(CleanFlexibleLine::getCreateTime));
        for (CleanFlexibleLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd94()));
            realitys.add(String.format("%.2f",equipmentBaseLineData.getVd50()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询精洗温度
     * @return
     */
    @Override
    public JSONObject fineWashingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanFlexibleLine> equipmentRotorLineDataList = cleanFlexibleLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanFlexibleLine>().orderByDesc(CleanFlexibleLine::getCreateTime));
        for (CleanFlexibleLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd106()));
            realitys.add(String.format("%.2f",equipmentBaseLineData.getVd54()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 防锈温度
     * @return
     */
    @Override
    public JSONObject antirustTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanFlexibleLine> equipmentRotorLineDataList = cleanFlexibleLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanFlexibleLine>().orderByDesc(CleanFlexibleLine::getCreateTime));
        for (CleanFlexibleLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd118()));
            realitys.add(String.format("%.2f",equipmentBaseLineData.getVd58()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询粗细Ph
     * @return
     */
    @Override
    public JSONObject roughWashingPh() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanFlexibleLine> equipmentRotorLineDataList = cleanFlexibleLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanFlexibleLine>().orderByDesc(CleanFlexibleLine::getCreateTime));
        for (CleanFlexibleLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd222()));
            realitys.add(String.format("%.2f",equipmentBaseLineData.getVd210()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 精洗Ph
     * @return
     */
    @Override
    public JSONObject fineWashingPh() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanFlexibleLine> equipmentRotorLineDataList = cleanFlexibleLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanFlexibleLine>().orderByDesc(CleanFlexibleLine::getCreateTime));
        for (CleanFlexibleLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd226()));
            realitys.add(String.format("%.2f",equipmentBaseLineData.getVd214()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 防锈PH
     * @return
     */
    @Override
    public JSONObject antirustPh() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanFlexibleLine> equipmentRotorLineDataList = cleanFlexibleLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanFlexibleLine>().orderByDesc(CleanFlexibleLine::getCreateTime));
        for (CleanFlexibleLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd230()));
            realitys.add(String.format("%.2f",equipmentBaseLineData.getVd218()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 油漆烘干温度
     * @return
     */
    @Override
    public JSONObject paintDryingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realityOnss = new ArrayList<>();
        List<String> realityTwos = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanFlexibleLine> equipmentRotorLineDataList = cleanFlexibleLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanFlexibleLine>().orderByDesc(CleanFlexibleLine::getCreateTime));
        for (CleanFlexibleLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd166()));
            realityOnss.add(String.format("%.2f",equipmentBaseLineData.getVd78()));
            realityTwos.add(String.format("%.2f",equipmentBaseLineData.getVd82()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realityOnss);
        jsonObject.put("threeList", realityTwos);
        return jsonObject;
    }

    /**
     * 脱水烘干温度
     * @return
     */
    @Override
    public JSONObject dehydrationDryingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realityOnss = new ArrayList<>();
        List<String> realityTwos = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanFlexibleLine> equipmentRotorLineDataList = cleanFlexibleLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanFlexibleLine>().orderByDesc(CleanFlexibleLine::getCreateTime));
        for (CleanFlexibleLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd142()));
            realityOnss.add(String.format("%.2f",equipmentBaseLineData.getVd66()));
            realityTwos.add(String.format("%.2f",equipmentBaseLineData.getVd70()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realityOnss);
        jsonObject.put("threeList", realityTwos);
        return jsonObject;
    }

    /**
     * 辊道频率
     * @return
     */
    @Override
    public JSONObject rollerFrequency() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realityOnss = new ArrayList<>();
        List<String> realityTwos = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRollerLine> equipmentRotorLineDataList = cleanRollerLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRollerLine>().orderByDesc(CleanRollerLine::getCreateTime));
        for (CleanRollerLine equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentBaseLineData.getVd1148()));
            realityOnss.add(String.format("%.2f",equipmentBaseLineData.getVd1212()));
            realityTwos.add(String.format("%.2f",equipmentBaseLineData.getVd1244()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realityOnss);
        jsonObject.put("threeList", realityTwos);
        return jsonObject;
    }

}
