package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.cable.LayingTwisting;
import com.hzforward.oee.mapper.baseData.cable.LayingTwistingMapper;
import com.hzforward.sync.service.ICableDeviceLayingMachineService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 电缆-成缆机悬臂绞Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Service
@RequiredArgsConstructor
public class CableDeviceLayingMachineServiceImpl extends ServiceImpl<LayingTwistingMapper, LayingTwisting> implements ICableDeviceLayingMachineService
{
    private final LayingTwistingMapper layingTwistingMapper;

    /**
     * 查询电缆-成缆机悬臂绞列表
     *
     * @param layingTwisting 电缆-成缆机悬臂绞
     * @return 电缆-成缆机悬臂绞
     */
    @Override
    public List<LayingTwisting> selectCableDeviceLayingMachineList(LayingTwisting layingTwisting)
    {
        return layingTwistingMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<LayingTwisting>()
                .eq(LayingTwisting::getEquipNo, layingTwisting.getEquipNo())
        );
    }

    /**
     * 当前绞距
     * @return
     */
    @Override
    public JSONObject lengthOfLay() {
        List<String> times = new ArrayList<>();
        List<String> oneList = new ArrayList<>();
//        List<String> twoList = new ArrayList<>();
//        List<String> threeList = new ArrayList<>();
//        List<String> fourList = new ArrayList<>();
        // 获取最新一条数据时间
        List<LayingTwisting> lineOneList = layingTwistingMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<LayingTwisting>().eq(LayingTwisting::getEquipNo,"DL200043").orderByDesc(LayingTwisting::getCreateTime));
        for (LayingTwisting equipmentBaseLineData : lineOneList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            oneList.add(String.valueOf(equipmentBaseLineData.getCurrentTwistDistance()));
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", oneList);
//        jsonObject.put("twoList", twoList);
//        jsonObject.put("threeList", threeList);
//        jsonObject.put("fourList", fourList);
        return jsonObject;
    }

    /**
     * 当前米数
     * @return
     */
    @Override
    public JSONObject currentMeter() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        // 获取最新一条数据时间
        List<LayingTwisting> equipmentRotorLineDataList = layingTwistingMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<LayingTwisting>().eq(LayingTwisting::getEquipNo,"DL200043").orderByDesc(LayingTwisting::getCreateTime));
        for (LayingTwisting equipmentBaseLineData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentBaseLineData.getCreateTime()));
            phs.add(String.valueOf(equipmentBaseLineData.getCurrentMeter()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        return jsonObject;
    }

    /**
     * 详情列表
     * @return
     */
    @Override
    public JSONArray detailList() {
        List<LayingTwisting> layingTwistingList = layingTwistingMapper.selectList(Page.of(0, 20),
                new LambdaQueryWrapper<LayingTwisting>().eq(LayingTwisting::getEquipNo,"DL200043").orderByDesc(LayingTwisting::getCreateTime));
        JSONArray result = new JSONArray();
        for (LayingTwisting deviceLayingTwisting : layingTwistingList) {
            JSONArray resultObj = new JSONArray();
            resultObj.add(deviceLayingTwisting.getEquipNo());
            resultObj.add("800悬臂单扭成榄机");
            resultObj.add("1#");
            resultObj.add(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, deviceLayingTwisting.getCreateTime()));

            resultObj.add(deviceLayingTwisting.getLayDirection());
            resultObj.add(deviceLayingTwisting.getCurrentLineSpeed());
            resultObj.add(deviceLayingTwisting.getCurrentTwistDistance());
            resultObj.add(deviceLayingTwisting.getCurrentMeter());
            resultObj.add(deviceLayingTwisting.getWrappingStart());
            resultObj.add(deviceLayingTwisting.getWrappingTension());
            resultObj.add(deviceLayingTwisting.getWrappingMeter());
            result.add(resultObj);
        }
        return result;
    }
}
