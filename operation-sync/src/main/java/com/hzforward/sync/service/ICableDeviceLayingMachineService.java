package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.baseData.cable.LayingTwisting;

import java.util.List;

/**
 * 电缆-成缆机悬臂绞Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-14
 */
public interface ICableDeviceLayingMachineService extends IService<LayingTwisting>
{
    /**
     * 查询电缆-成缆机悬臂绞列表
     * 
     * @param layingTwisting 电缆-成缆机悬臂绞
     * @return 电缆-成缆机悬臂绞集合
     */
    List<LayingTwisting> selectCableDeviceLayingMachineList(LayingTwisting layingTwisting);

    /**
     * 当前绞距
     * @return
     */
    JSONObject lengthOfLay();

    /**
     * 当前米数
     * @return
     */
    JSONObject currentMeter();

    /**
     * 详情列表
     * @return
     */
    JSONArray detailList();
}
