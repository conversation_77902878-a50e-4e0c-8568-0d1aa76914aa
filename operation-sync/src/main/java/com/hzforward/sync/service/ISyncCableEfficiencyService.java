package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.sync.domain.SyncCableEfficiency;

/**
 * 电缆车间效率统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface ISyncCableEfficiencyService 
{
    /**
     * 查询电缆车间效率统计
     * 
     * @param id 电缆车间效率统计主键
     * @return 电缆车间效率统计
     */
    SyncCableEfficiency selectSyncCableEfficiencyById(Long id);

    /**
     * 查询电缆车间效率统计列表
     * 
     * @param syncCableEfficiency 电缆车间效率统计
     * @return 电缆车间效率统计集合
     */
    List<SyncCableEfficiency> selectSyncCableEfficiencyList(SyncCableEfficiency syncCableEfficiency);

    /**
     * 新增电缆车间效率统计
     * 
     * @param syncCableEfficiency 电缆车间效率统计
     * @return 结果
     */
    int insertSyncCableEfficiency(SyncCableEfficiency syncCableEfficiency);

    /**
     * 修改电缆车间效率统计
     * 
     * @param syncCableEfficiency 电缆车间效率统计
     * @return 结果
     */
    int updateSyncCableEfficiency(SyncCableEfficiency syncCableEfficiency);

    /**
     * 删除电缆车间效率统计
     * 
     * @param ids 需要删除的电缆车间效率统计主键集合
     * @return 结果
     */
    int deleteSyncCableEfficiencyByIds(List<Long> ids);

    /**
     * 导入
     * @param cableEfficiencies
     * @return
     */
    String importData(List<SyncCableEfficiency> cableEfficiencies);
}
