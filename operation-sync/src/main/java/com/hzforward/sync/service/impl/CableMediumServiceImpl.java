package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.core.domain.entity.SysDictData;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.DictUtils;
import com.hzforward.dataCheck.domain.PackingCheck;
import com.hzforward.dataCheck.mapper.PackingCheckMapper;
import com.hzforward.framework.datasource.DynamicDataSourceContextHolder;
import com.hzforward.oee.domain.baseData.machining.CncRealTimeInfoRow;
import com.hzforward.sync.domain.*;
import com.hzforward.sync.mapper.CableOrderInfoMapper;
import com.hzforward.oee.mapper.baseData.machining.CncRealTimeInfoRowMapper;
import com.hzforward.sync.mapper.MakeInspectionViewMapper;
import com.hzforward.sync.mapper.OrderInfoMapper;
import com.hzforward.sync.service.ICableMediumService;
import com.hzforward.sync.util.ExcelUtils;
import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileInputStream;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 机座线PLC同步数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Service
@RequiredArgsConstructor
public class CableMediumServiceImpl implements ICableMediumService {

    private final OrderInfoMapper orderInfoMapper;
    private final CableOrderInfoMapper cableOrderInfoMapper;
    private final CncRealTimeInfoRowMapper cncRealTimeInfoRowMapper;
    private final MakeInspectionViewMapper makeInspectionViewMapper;
    private final PackingCheckMapper packingCheckMapper;

    /**
     * 获取年销量列表
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE6)
    public JSONObject getYearSaleList() {
        SysDictData date = DictUtils.getDictCache("cable_screen_data", "sale_base_no");
        JSONObject object = JSON.parseObject(date.getRemark());
        JSONObject resObj = new JSONObject();
        Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("2024-10-01 00:00:00", DateUtils.getNowDate()));
        Date endTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-12-31 23:59:59", DateUtils.getNowDate()));
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", startTime);
        params.put("endDate", endTime);
        List<OrderYearCount> orderMonthSaleList = cableOrderInfoMapper.getYearCount(params);
        JSONArray years = object.getJSONArray("years");
        JSONArray saleNos = object.getJSONArray("yearSales");
        for (OrderYearCount orderYearCount : orderMonthSaleList) {
            years.add(orderYearCount.getYear());
            Integer saleNo = orderYearCount.getSaleNo();
            if (orderYearCount.getYear().equals("2024")) {
                JSONArray monthSales = object.getJSONArray("monthSales");
                for (int i = 0; i < monthSales.size(); i++) {
                    saleNo += monthSales.getInteger(i);
                }
            }
            saleNos.add(saleNo);
        }
        resObj.put("years", years);
        resObj.put("saleNos", saleNos);
        return resObj;
    }

    /**
     * 获取月销量数据
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE6)
    public JSONObject getMonthSaleList() {
        Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", DateUtils.getNowDate()));
        if (DateUtils.parseDateToStr(DateUtils.YYYY, DateUtils.getNowDate()).equals("2024")) {
            startTime = DateUtils.parseDate(DateUtils.parseDateToStr("2024-10-01 00:00:00", DateUtils.getNowDate()));
        }
        Date endTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-12-31 23:59:59", DateUtils.getNowDate()));
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", startTime);
        params.put("endDate", endTime);
        List<OrderMonthCount> orderMonthSaleList = cableOrderInfoMapper.getMonthCount(params);
        // 使用 TreeMap 来按键（月份）排序
        Map<String, Integer> orderMonthMap = new TreeMap<>(Comparator.naturalOrder());
        for (OrderMonthCount order : orderMonthSaleList) {
            orderMonthMap.put(order.getMonth().substring(order.getMonth().length() - 2), order.getSaleNo());
        }
        // 转换为两个数组
        JSONArray months = new JSONArray();
        JSONArray counts = new JSONArray();
        if (DateUtils.parseDateToStr(DateUtils.YYYY, DateUtils.getNowDate()).equals("2024")) {
            SysDictData date = DictUtils.getDictCache("cable_screen_data", "sale_base_no");
            JSONObject object = JSON.parseObject(date.getRemark());
            months = object.getJSONArray("months");
            counts = object.getJSONArray("monthSales");
        }
        for (OrderMonthCount orderMonthCount : orderMonthSaleList) {
            months.add(orderMonthCount.getMonth().substring(orderMonthCount.getMonth().length() - 2));
            counts.add(orderMonthCount.getSaleNo());
        }
        // 生成结果
        JSONObject result = new JSONObject();
        result.put("months", months);
        result.put("saleNos", counts);
        return result;
    }

    /**
     * 获取漆包线年销量列表
     *
     * @return
     */
    @Override
    public JSONObject getEnameledWireYearSaleList() {
        try {
            SysDictData date = DictUtils.getDictCache("cable_screen_data", "enameled_wire_data");
            JSONArray array = JSON.parseArray(date.getRemark());
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE4.name());
            Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("2020-01-01 00:00:00", DateUtils.getNowDate()));
            Date endTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-12-31 23:59:59", DateUtils.getNowDate()));
            Map<String, Object> reqMap = new TreeMap<>();
            reqMap.put("startDate", startTime);
            reqMap.put("endDate", endTime);
            reqMap.put("itemCodes", array);
            List<String> years = new ArrayList<>();
            List<Integer> saleNos = new ArrayList<>();
            List<OrderYearCount> yearList = makeInspectionViewMapper.getYearCount(reqMap);
            for (OrderYearCount orderYearCount : yearList) {
                years.add(orderYearCount.getYear());
                saleNos.add(orderYearCount.getSaleNo());
            }
            JSONObject resObj = new JSONObject();
            resObj.put("years", years);
            resObj.put("saleNos", saleNos);
            return resObj;
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    /**
     * 获取漆包线月销量列表
     * @return
     */
    @Override
    public JSONObject getEnameledWireMonthSaleList() {
        try {
            SysDictData date = DictUtils.getDictCache("cable_screen_data", "enameled_wire_data");
            JSONArray array = JSON.parseArray(date.getRemark());
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE4.name());
            Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", DateUtils.getNowDate()));
            Date endTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-12-31 23:59:59", DateUtils.getNowDate()));
            Map<String, Object> reqMap = new TreeMap<>();
            reqMap.put("startDate", startTime);
            reqMap.put("endDate", endTime);
            reqMap.put("itemCodes", array);
            List<String> years = new ArrayList<>();
            List<Integer> saleNos = new ArrayList<>();
            List<OrderMonthCount> yearList = makeInspectionViewMapper.getMonthCount(reqMap);
            for (OrderMonthCount orderMonthCount : yearList) {
                years.add(orderMonthCount.getMonth().substring(orderMonthCount.getMonth().length() - 2));
                saleNos.add(orderMonthCount.getSaleNo());
            }
            JSONObject resObj = new JSONObject();
            resObj.put("months", years);
            resObj.put("saleNos", saleNos);
            return resObj;
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    /**
     * 各个客户排产完成率
     * @return
     */
    @Override
    public JSONObject getCustomerProduceComplete() {
        DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE6.name());
        Date startTime = DateUtils.addDays(Objects.requireNonNull(DateUtils.getTodayStartTime(DateUtils.getNowDate())), 0);
        Date endTime = DateUtils.addDays(Objects.requireNonNull(DateUtils.getTodayLastTime(DateUtils.getNowDate())), 0);
        List<CableOrderInfo> cableOrderInfoList = cableOrderInfoMapper.selectList(new LambdaQueryWrapper<CableOrderInfo>()
                .ge(CableOrderInfo::getDeliveryDate,startTime)
                .le(CableOrderInfo::getDeliveryDate,endTime)
                .eq(CableOrderInfo::getIsDelete, 0));
        Map<String, List<CableOrderInfo>> cableOrderMap = cableOrderInfoList.stream().collect(Collectors.groupingBy(CableOrderInfo::getCustomerName));
        List<String> contractNos = cableOrderInfoList.stream().map(CableOrderInfo::getContractNo).collect(Collectors.toList());
        DynamicDataSourceContextHolder.clearDataSourceType();
        List<PackingCheck> packingCheckList = packingCheckMapper.selectList(new LambdaQueryWrapper<PackingCheck>().in(PackingCheck::getContractNumber, contractNos));
        List<PackingCheck> packingChecks = packingCheckList.stream().filter(item -> "2".equals(item.getPackingState())).collect(Collectors.toList());
        List<String> contractNumbers = packingChecks.stream().map(PackingCheck::getContractNumber).collect(Collectors.toList());
        BigDecimal oneRate = new BigDecimal(0);
        BigDecimal twoRate = new BigDecimal(0);
        BigDecimal threeRate = new BigDecimal(0);
        for (String key : cableOrderMap.keySet()) {
            List<CableOrderInfo> list = cableOrderMap.get(key);
            int successNo = 0;
            for (CableOrderInfo cableOrderInfo : list) {
                if (contractNumbers.contains(cableOrderInfo.getContractNo())) {
                    successNo++;
                }
            }
            if (!list.isEmpty()) {
                if ("杭州西奥电梯有限公司".equals(key)) {
                    oneRate = new BigDecimal(successNo).divide(new BigDecimal(list.size()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                } else if ("浙江优迈智慧科技有限公司".equals(key)) {
                    twoRate = new BigDecimal(successNo).divide(new BigDecimal(list.size()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                } else if ("杭州优迈机电科技有限公司".equals(key)) {
                    threeRate = new BigDecimal(successNo).divide(new BigDecimal(list.size()), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                }
            }
        }
        SysDictData date = DictUtils.getDictCache("cable_screen_data", "enameled_wire_rate");
        JSONObject resObj = new JSONObject();
        resObj.put("oneRate", oneRate);
        resObj.put("twoRate", twoRate);
        resObj.put("threeRate", threeRate);
        resObj.put("fourRate", String.format("%.2f",Double.parseDouble(date.getDictValue())));
        return resObj;
    }

    /**
     * 获取区域销量统计
     *
     * @return
     */
    @Override
    public JSONObject getSectionSaleList() {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "section_sale_list");
        JSONObject object = JSON.parseObject(date.getRemark());
        JSONObject resObj = new JSONObject();
        resObj.put("sections", object.getJSONArray("sections"));
        resObj.put("saleNos", object.getJSONArray("saleNos"));
        return resObj;
    }

    /**
     * 获取订单多维度统计
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE6)
    public JSONObject getMultiDimensionalSaleList() {
        // 统计年
        Date yearStartTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-01-01 00:00:00", DateUtils.getNowDate()));
        Date yearEndTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-12-31 23:59:59", DateUtils.getNowDate()));
        Long yearCount = cableOrderInfoMapper.selectCount(new LambdaQueryWrapper<CableOrderInfo>().ge(CableOrderInfo::getCTime, yearStartTime).le(CableOrderInfo::getCTime, yearEndTime)
                .eq(CableOrderInfo::getIsDelete, 0));
        // 统计月
        Date monthStartTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-01 00:00:00", DateUtils.getNowDate()));
        Date monthEndTime = DateUtils.getTodayLastTime(DateUtils.addDays(DateUtils.addMonths(monthStartTime, 1), -1));
        Long monthCount = cableOrderInfoMapper.selectCount(new LambdaQueryWrapper<CableOrderInfo>().ge(CableOrderInfo::getCTime, monthStartTime).le(CableOrderInfo::getCTime, monthEndTime)
                .eq(CableOrderInfo::getIsDelete, 0));
        // 统计日
        Date dayStartTime = DateUtils.getTodayStartTime(DateUtils.getNowDate());
        Date dayEndTime = DateUtils.getTodayLastTime(DateUtils.getNowDate());
        Long dayCount = cableOrderInfoMapper.selectCount(new LambdaQueryWrapper<CableOrderInfo>().ge(CableOrderInfo::getCTime, dayStartTime).le(CableOrderInfo::getCTime, dayEndTime));
        // 累计
        SysDictData date = DictUtils.getDictCache("cable_screen_data", "sale_base_no");
        JSONObject object = JSON.parseObject(date.getRemark());
        JSONArray yearSales = object.getJSONArray("yearSales");
//        JSONArray monthSales = object.getJSONArray("monthSales");
//        if (DateUtils.parseDateToStr(DateUtils.YYYY, DateUtils.getNowDate()).equals("2024")) {
//            for (int i = 0; i < monthSales.size(); i++) {
//                yearCount += monthSales.getLong(i);
//            }
//        }
        Long count = 0L;
        for (int i = 0; i < yearSales.size(); i++) {
            count += yearSales.getLong(i);
        }
        count += yearCount;
        // 组合返回对象
        JSONObject result = new JSONObject();
        result.put("count", count);
        result.put("yearCount", yearCount);
        result.put("monthCount", monthCount);
        result.put("dayCount", dayCount);
        return result;
    }

    /**
     * 获取FPY
     *
     * @return
     */
    @Override
    public JSONObject getFpy() {
        SysDictData date = DictUtils.getDictCache("cable_screen_data", "fpy_data");
        return JSON.parseObject(date.getRemark());
    }

    /**
     * 获取FTB关闭率
     *
     * @return
     */
    @Override
    public JSONArray getFtbCloseRate() {
        SysDictData date = DictUtils.getDictCache("cable_screen_data", "ftb_close_rate");
        return JSON.parseArray(date.getRemark());
    }

    /**
     * 获取月度安全天数列表
     *
     * @return
     */
    @Override
    public JSONObject getMonthSecureDayList() {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "month_secure_day_list");
        return JSON.parseObject(date.getRemark());
    }

    /**
     * 获取隐患排查
     *
     * @return
     */
    @Override
    public JSONArray getHazardInvestigation() {
        SysDictData date = DictUtils.getDictCache("cable_screen_data", "hazard_investigation");
        return JSON.parseArray(date.getRemark());
    }

    /**
     * 获取质量柱状图
     *
     * @return
     */
    @Override
    public JSONObject getQualityList() {
        JSONObject result = new JSONObject();
        return result;
    }

    /**
     * 获取机加工排产完成率
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE7)
    public JSONObject getMachiningCompleteRate() {
        String networkPath = DictUtils.getDictValue("big_screen_data", "network_path");
        try {
            // jcifs 认证信息
            NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(
                    "elevator.xizi.local", "hu.hongjun", "hhjHHJ1314520."); // 请替换为你的用户名和密码
            SmbFile smbFile = new SmbFile(networkPath, auth);
            InputStream in = new SmbFileInputStream(smbFile);
            XSSFWorkbook workbook = new XSSFWorkbook(in);
            Sheet sheet = workbook.getSheet("Sheet1");
            List<List<String>> resultList = new ArrayList<>();
            if (sheet != null) {
                for (Row row : sheet) {
                    List<String> rowList = new ArrayList<>();
                    for (Cell cell : row) {
                        String cellValue = ExcelUtils.getCellValue(cell);
                        rowList.add(cellValue);
                    }
                    resultList.add(rowList);
                }
            }
            workbook.close();
            in.close();
            String[] lines = {"机座精车工段", "TD部件机座加工线", "TD部件曳引轮加工线"};
            List<BigDecimal> rates = new ArrayList<>();
            for (String line : lines) {
                int index = 0;
                for (int i = 0; i < resultList.get(0).size(); i++) {
                    if (resultList.get(0).get(i).equals(line)) index = i;
                }
                int productionNo = 0;
                int completeNo = 0;
                BigDecimal rate = new BigDecimal(0);
                for (int i = 0; i < resultList.size(); i++) {
                    if (i == 0) continue;
                    if (resultList.get(i).size() >= index + 1) {
                        if (DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.parseDate(resultList.get(i).get(0))).equals(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                            productionNo = (int) Double.parseDouble(resultList.get(i).get(index));
                        }
                    }
                }
                SysDictData date = DictUtils.getDictCache("big_screen_data", "line_device_contrast");
                JSONObject lineDeviceContrast = JSON.parseObject(date.getRemark());
                if (!lineDeviceContrast.containsKey(line)) {
                    throw new RuntimeException("线体不存在");
                }
                JSONArray baseProcessing = lineDeviceContrast.getJSONArray(line);

                //判断 当前时间是否超过7:30 如果超过就获取前一天7:30 否则获取当天7:30 作为开始时间
                Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd 07:30:00", DateUtils.getNowDate()));
                if (startTime.getTime() > System.currentTimeMillis()) {
                    startTime = DateUtils.addDays(startTime, -1);
                }

                for (int i = 0; i < baseProcessing.size(); i++) {
                    JSONObject device = baseProcessing.getJSONObject(i);
                    String deviceNo = device.getString("deviceId");
                    if (device.getBoolean("isCompute")) {
                        List<CncRealTimeInfoRow> start = cncRealTimeInfoRowMapper.selectList(Page.of(0, 1), new LambdaQueryWrapper<CncRealTimeInfoRow>()
                                .ge(CncRealTimeInfoRow::getCreateTime, startTime)
                                .le(CncRealTimeInfoRow::getCreateTime, DateUtils.getNowDate())
                                .eq(CncRealTimeInfoRow::getEquipNo, deviceNo).orderByAsc(CncRealTimeInfoRow::getCreateTime));
                        List<CncRealTimeInfoRow> end = cncRealTimeInfoRowMapper.selectList(Page.of(0, 1), new LambdaQueryWrapper<CncRealTimeInfoRow>()
                                .ge(CncRealTimeInfoRow::getCreateTime, startTime)
                                .le(CncRealTimeInfoRow::getCreateTime, DateUtils.getNowDate())
                                .eq(CncRealTimeInfoRow::getEquipNo, deviceNo).orderByDesc(CncRealTimeInfoRow::getCreateTime));
                        if (!end.isEmpty() && !start.isEmpty()) {
                            try {
                                if (deviceNo.equals("001")) {
                                    completeNo += ((end.get(0).getCncProducts() - start.get(0).getCncProducts()) / 2);
                                } else {
                                    completeNo += end.get(0).getCncProducts() - start.get(0).getCncProducts();
                                }
                            } catch (NumberFormatException e) {
                                continue;
                            }
                        }
                    }
                }
                if (productionNo != 0) {
                    rate = new BigDecimal(completeNo).divide(new BigDecimal(productionNo), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
                }
                rates.add(rate);
            }
            JSONObject resObj = new JSONObject();
            resObj.put("baseRate", rates.get(0));
            resObj.put("partBaseRate", rates.get(1));
            resObj.put("partTractionSheaveRate", rates.get(2));
            return resObj;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取设备状态
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE7)
    public JSONObject getDeviceStatus() {
        return null;
    }
}
