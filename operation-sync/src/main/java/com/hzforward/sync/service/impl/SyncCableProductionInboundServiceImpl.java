package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.sync.domain.SyncCableProductionInbound;
import com.hzforward.sync.mapper.SyncCableProductionInboundMapper;
import com.hzforward.sync.service.ISyncCableProductionInboundService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产入库Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
public class SyncCableProductionInboundServiceImpl extends ServiceImpl<SyncCableProductionInboundMapper, SyncCableProductionInbound> implements ISyncCableProductionInboundService
{
    @Resource
    private SyncCableProductionInboundMapper syncCableProductionInboundMapper;

    /**
     * 查询生产入库列表
     *
     * @param syncCableProductionInbound 生产入库
     * @return 生产入库
     */
    @Override
    public List<SyncCableProductionInbound> selectSyncCableProductionInboundList(SyncCableProductionInbound syncCableProductionInbound)
    {
        return syncCableProductionInboundMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<SyncCableProductionInbound>()
                .eq(SyncCableProductionInbound::getInboundTime, syncCableProductionInbound.getInboundTime())
                .eq(SyncCableProductionInbound::getCode, syncCableProductionInbound.getCode())
                .eq(SyncCableProductionInbound::getProduceCode, syncCableProductionInbound.getProduceCode())
                .like(SyncCableProductionInbound::getProduceName, syncCableProductionInbound.getProduceName())
                .eq(SyncCableProductionInbound::getUnit, syncCableProductionInbound.getUnit())
                .eq(SyncCableProductionInbound::getInboundQuantity, syncCableProductionInbound.getInboundQuantity())
        );
    }

    @Override
    public String importData(List<SyncCableProductionInbound> productionInbounds) {
        for (SyncCableProductionInbound productionInbound : productionInbounds) {
            syncCableProductionInboundMapper.insert(productionInbound);
        }
        return "success";
    }
}
