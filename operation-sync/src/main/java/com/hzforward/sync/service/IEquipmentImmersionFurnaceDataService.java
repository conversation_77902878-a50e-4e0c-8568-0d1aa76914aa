package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.sync.domain.EquipmentImmersionFurnaceData;

import java.util.List;

/**
 * 通过式浸漆炉Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-11
 */
public interface IEquipmentImmersionFurnaceDataService extends IService<EquipmentImmersionFurnaceData>
{
    /**
     * 查询通过式浸漆炉列表
     * 
     * @param equipmentImmersionFurnaceData 通过式浸漆炉
     * @return 通过式浸漆炉集合
     */
    List<EquipmentImmersionFurnaceData> selectEquipmentImmersionFurnaceDataList(EquipmentImmersionFurnaceData equipmentImmersionFurnaceData);

    /**
     * 通过式浸漆炉-凝胶
     * @return
     */
    JSONObject getGelList();

    /**
     * 通过式浸漆炉-固化
     * @return
     */
    JSONObject getSolidifyList();

    /**
     * 通过式浸漆炉-漆液温度
     * @return
     */
    JSONObject getPaintLiquidList();
}
