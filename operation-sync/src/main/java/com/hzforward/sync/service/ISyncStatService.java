package com.hzforward.sync.service;

import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.sync.domain.vo.EveryHourProduce;
import com.hzforward.sync.domain.vo.ProductionComplete;
import com.hzforward.sync.domain.vo.WorkHourStatistics;

import java.util.Date;

/**
 * 同步_排产数据Service接口
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ISyncStatService {

    /**
     * 查询量具点检完成率
     *
     * @return
     */
    AjaxResult measuringToolInspectionCompletionRate(String line);

    /**
     * 查询排产完成情况
     *
     * @return
     */
    ProductionComplete productionComplete(String line, Date date);

    /**
     * 查询当天生产进度
     *
     * @return
     */
    AjaxResult toDayProductionSchedule(String line, Date date);

    /**
     * 每小时产出与实际理论差异
     *
     * @return
     */
    EveryHourProduce everyHourProduce(String line, Date date, Integer nowHour);

    /**
     * 当前生产订单明细
     *
     * @return
     */
    AjaxResult currentProduceOrderInfo(String line);

    /**
     * 查询各个线体到岗情况
     *
     * @return
     */
    AjaxResult lineOnDuty(String line);

    /**
     * 查询耐压测试数据
     * @param line
     * @return
     */
    AjaxResult getPressureResistanceTestData(String line);

    /**
     * 获取工时统计
     * @param line
     * @return
     */
    WorkHourStatistics getWorkHourStatistics(String line,Date date);
}
