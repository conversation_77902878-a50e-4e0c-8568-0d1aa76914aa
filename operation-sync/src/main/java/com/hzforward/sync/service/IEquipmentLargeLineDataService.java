package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.baseData.host.CleanLargeLine;

import java.util.List;

/**
 * 大件清洗PLC同步数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-10
 */
public interface IEquipmentLargeLineDataService extends IService<CleanLargeLine>
{
    /**
     * 查询大件清洗PLC同步数据列表
     * 
     * @param cleanLargeLine 大件清洗PLC同步数据
     * @return 大件清洗PLC同步数据集合
     */
    List<CleanLargeLine> selectEquipmentLargeLineDataList(CleanLargeLine cleanLargeLine);

    /**
     * 粗洗温度
     * @return
     */
    JSONObject roughWashingTemperature();

    /**
     * 精洗温度
     * @return
     */
    JSONObject fineWashingTemperature();

    /**
     * 粗细时间
     * @return
     */
    JSONObject roughWashingTime();
}
