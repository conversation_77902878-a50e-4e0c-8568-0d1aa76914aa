package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.sync.domain.SyncCableCopperWireUsage;
import com.hzforward.sync.mapper.SyncCableCopperWireUsageMapper;
import com.hzforward.sync.service.ISyncCableCopperWireUsageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 铜丝用量Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
public class SyncCableCopperWireUsageServiceImpl extends ServiceImpl<SyncCableCopperWireUsageMapper, SyncCableCopperWireUsage> implements ISyncCableCopperWireUsageService
{
    @Resource
    private SyncCableCopperWireUsageMapper syncCableCopperWireUsageMapper;

    /**
     * 查询铜丝用量列表
     *
     * @param syncCableCopperWireUsage 铜丝用量
     * @return 铜丝用量
     */
    @Override
    public List<SyncCableCopperWireUsage> selectSyncCableCopperWireUsageList(SyncCableCopperWireUsage syncCableCopperWireUsage)
    {
        return syncCableCopperWireUsageMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<SyncCableCopperWireUsage>()
                .eq(SyncCableCopperWireUsage::getPurchaseOrderNo, syncCableCopperWireUsage.getPurchaseOrderNo())
                .eq(SyncCableCopperWireUsage::getPurchaseOrderLine, syncCableCopperWireUsage.getPurchaseOrderLine())
                .eq(SyncCableCopperWireUsage::getMaterialCode, syncCableCopperWireUsage.getMaterialCode())
                .eq(SyncCableCopperWireUsage::getMaterialDesc, syncCableCopperWireUsage.getMaterialDesc())
                .eq(SyncCableCopperWireUsage::getQuantity, syncCableCopperWireUsage.getQuantity())
                .eq(SyncCableCopperWireUsage::getDocumentDate, syncCableCopperWireUsage.getDocumentDate())
                .eq(SyncCableCopperWireUsage::getUnit, syncCableCopperWireUsage.getUnit())
        );
    }

    /**
     * 导入
     * @param cableCopperWireUsages
     * @return
     */
    @Override
    public String importData(List<SyncCableCopperWireUsage> cableCopperWireUsages) {
        for (SyncCableCopperWireUsage cableCopperWireUsage : cableCopperWireUsages) {
            syncCableCopperWireUsageMapper.insert(cableCopperWireUsage);
        }
        return "success";
    }
}
