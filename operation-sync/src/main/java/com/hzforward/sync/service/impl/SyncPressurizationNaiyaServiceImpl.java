package com.hzforward.sync.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import com.hzforward.sync.mapper.SyncPressurizationNaiyaMapper;
import com.hzforward.sync.domain.SyncPressurizationNaiya;
import com.hzforward.sync.service.ISyncPressurizationNaiyaService;

import javax.annotation.Resource;

/**
 * 耐压测试_耐压Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncPressurizationNaiyaServiceImpl implements ISyncPressurizationNaiyaService
{
    @Resource
    private SyncPressurizationNaiyaMapper syncPressurizationNaiyaMapper;

    /**
     * 查询耐压测试_耐压
     *
     * @param abh 耐压测试_耐压主键
     * @return 耐压测试_耐压
     */
    @Override
    public SyncPressurizationNaiya selectSyncPressurizationNaiyaByAbh(Long abh)
    {
        return syncPressurizationNaiyaMapper.selectById(abh);
    }

    /**
     * 查询耐压测试_耐压列表
     *
     * @param syncPressurizationNaiya 耐压测试_耐压
     * @return 耐压测试_耐压
     */
    @Override
    public List<SyncPressurizationNaiya> selectSyncPressurizationNaiyaList(SyncPressurizationNaiya syncPressurizationNaiya)
    {
        return syncPressurizationNaiyaMapper.selectList(new LambdaQueryWrapper<SyncPressurizationNaiya>().orderByDesc(SyncPressurizationNaiya::getCsrqsj));
    }

    /**
     * 新增耐压测试_耐压
     *
     * @param syncPressurizationNaiya 耐压测试_耐压
     * @return 结果
     */
    @Override
    public int insertSyncPressurizationNaiya(SyncPressurizationNaiya syncPressurizationNaiya)
    {
        return syncPressurizationNaiyaMapper.insert(syncPressurizationNaiya);
    }

    /**
     * 修改耐压测试_耐压
     *
     * @param syncPressurizationNaiya 耐压测试_耐压
     * @return 结果
     */
    @Override
    public int updateSyncPressurizationNaiya(SyncPressurizationNaiya syncPressurizationNaiya)
    {
        return syncPressurizationNaiyaMapper.updateById(syncPressurizationNaiya);
    }

    /**
     * 批量删除耐压测试_耐压
     *
     * @param abhs 需要删除的耐压测试_耐压主键
     * @return 结果
     */
    @Override
    public int deleteSyncPressurizationNaiyaByAbhs(List<Long> abhs)
    {
        return syncPressurizationNaiyaMapper.deleteBatchIds(abhs);
    }

    /**
     * 删除耐压测试_耐压信息
     *
     * @param abh 耐压测试_耐压主键
     * @return 结果
     */
    @Override
    public int deleteSyncPressurizationNaiyaByAbh(Long abh)
    {
        return syncPressurizationNaiyaMapper.deleteById(abh);
    }
}
