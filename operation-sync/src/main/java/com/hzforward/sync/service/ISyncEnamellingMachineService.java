package com.hzforward.sync.service;

import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.sync.domain.EnamellingMachine;
import com.hzforward.sync.domain.SyncProduction;
import com.hzforward.sync.domain.vo.SyncProductionQualified;
import com.hzforward.sync.domain.vo.SyncProductionQualifiedRes;

import java.util.List;

/**
 * 同步_排产数据Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ISyncEnamellingMachineService
{

    List<EnamellingMachine> queryList();

    AjaxResult getTemperature();

    AjaxResult getLineSpeed();
}
