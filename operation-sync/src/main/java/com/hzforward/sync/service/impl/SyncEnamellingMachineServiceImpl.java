package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.sync.domain.EnamellingMachine;
import com.hzforward.sync.mapper.SyncEnamellingMachineMapper;
import com.hzforward.sync.service.ISyncEnamellingMachineService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncEnamellingMachineServiceImpl implements ISyncEnamellingMachineService {
    @Resource
    private SyncEnamellingMachineMapper enamellingMachineMapper;


    /**
     * 查询分页列表
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE3)
    public List<EnamellingMachine> queryList() {
        return enamellingMachineMapper.selectList(new QueryWrapper<EnamellingMachine>().orderByDesc("create_time"));
    }

    /**
     * 查询温度监控
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE3)
    public AjaxResult getTemperature() {
        List<EnamellingMachine> list = enamellingMachineMapper.selectList(new LambdaQueryWrapper<EnamellingMachine>().eq(EnamellingMachine::getDeviceNo, "1#设备").orderByDesc(EnamellingMachine::getCreateTime).last(" limit 40"));
        List<String> timeList = new ArrayList<>();
        Map<String, BigDecimal> oneMap = new LinkedHashMap<>();
        Map<String, BigDecimal> twoMap = new LinkedHashMap<>();
        Map<String, BigDecimal> threeMap = new LinkedHashMap<>();
        Map<String, BigDecimal> fourMap = new LinkedHashMap<>();

        // 遍历每台设备，将数据分类存储到对应的映射中
        for (EnamellingMachine machine : list) {
            String createTime = DateUtils.parseDateToStr("HH:mm",machine.getCreateTime());
            BigDecimal temperature = machine.getOvenWeldingRealityTemperature();
            if (!timeList.contains(createTime)) {
                timeList.add(createTime);
            }

            switch (machine.getLineNo()) {
                case "1#线":
                    oneMap.put(createTime, temperature);
                    break;
                case "2#线":
                    twoMap.put(createTime, temperature);
                    break;
                case "3#线":
                    threeMap.put(createTime, temperature);
                    break;
                case "4#线":
                    fourMap.put(createTime, temperature);
                    break;
            }
        }

        // 基于timeList创建对应的温度列表，确保时间和温度一一对应
        List<BigDecimal> oneList = new ArrayList<>();
        List<BigDecimal> twoList = new ArrayList<>();
        List<BigDecimal> threeList = new ArrayList<>();
        List<BigDecimal> fourList = new ArrayList<>();

        for (String time : timeList) {
            oneList.add(oneMap.getOrDefault(time, null)); // 若没有对应值，则添加null或其他占位符
            twoList.add(twoMap.getOrDefault(time, null));
            threeList.add(threeMap.getOrDefault(time, null));
            fourList.add(fourMap.getOrDefault(time, null));
        }
        JSONObject resObj = new JSONObject();
        resObj.put("timeList", timeList);
        resObj.put("oneList", oneList);
        resObj.put("twoList", twoList);
        resObj.put("threeList", threeList);
        resObj.put("fourList", fourList);
        return AjaxResult.success(resObj);
    }

    /**
     * 查询线速
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE3)
    public AjaxResult getLineSpeed() {
        List<EnamellingMachine> list = enamellingMachineMapper.selectList(new LambdaQueryWrapper<EnamellingMachine>().eq(EnamellingMachine::getDeviceNo, "1#设备").orderByDesc(EnamellingMachine::getCreateTime).last(" limit 40"));
        List<String> timeList = new ArrayList<>();
        Map<String, BigDecimal> oneMap = new LinkedHashMap<>();
        Map<String, BigDecimal> twoMap = new LinkedHashMap<>();
        Map<String, BigDecimal> threeMap = new LinkedHashMap<>();
        Map<String, BigDecimal> fourMap = new LinkedHashMap<>();

        // 遍历每台设备，将数据分类存储到对应的映射中
        for (EnamellingMachine machine : list) {
            String createTime = DateUtils.parseDateToStr("HH:mm",machine.getCreateTime());
            BigDecimal temperature = machine.getRealityLineSpeed();
            if (!timeList.contains(createTime)) {
                timeList.add(createTime);
            }

            switch (machine.getLineNo()) {
                case "1#线":
                    oneMap.put(createTime, temperature);
                    break;
                case "2#线":
                    twoMap.put(createTime, temperature);
                    break;
                case "3#线":
                    threeMap.put(createTime, temperature);
                    break;
                case "4#线":
                    fourMap.put(createTime, temperature);
                    break;
            }
        }

        // 基于timeList创建对应的温度列表，确保时间和温度一一对应
        List<BigDecimal> oneList = new ArrayList<>();
        List<BigDecimal> twoList = new ArrayList<>();
        List<BigDecimal> threeList = new ArrayList<>();
        List<BigDecimal> fourList = new ArrayList<>();

        for (String time : timeList) {
            oneList.add(oneMap.getOrDefault(time, null)); // 若没有对应值，则添加null或其他占位符
            twoList.add(twoMap.getOrDefault(time, null));
            threeList.add(threeMap.getOrDefault(time, null));
            fourList.add(fourMap.getOrDefault(time, null));
        }
        JSONObject resObj = new JSONObject();
        resObj.put("timeList", timeList);
        resObj.put("oneList", oneList);
        resObj.put("twoList", twoList);
        resObj.put("threeList", threeList);
        resObj.put("fourList", fourList);
        return AjaxResult.success(resObj);
    }
}
