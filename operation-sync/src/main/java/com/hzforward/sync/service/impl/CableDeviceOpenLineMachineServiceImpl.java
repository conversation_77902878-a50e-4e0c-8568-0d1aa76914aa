package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.cable.OpenLine;
import com.hzforward.oee.mapper.baseData.cable.OpenLineMapper;
import com.hzforward.sync.service.ICableDeviceOpenLineMachineService;
import com.hzforward.sync.util.DataUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 电缆-开线机Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-16
 */
@Service
@RequiredArgsConstructor
public class CableDeviceOpenLineMachineServiceImpl extends ServiceImpl<OpenLineMapper, OpenLine> implements ICableDeviceOpenLineMachineService {
    private final OpenLineMapper openLineMapper;

    /**
     * 查询电缆-开线机列表
     *
     * @param openLine 电缆-开线机
     * @return 电缆-开线机
     */
    @Override
    public List<OpenLine> selectCableDeviceOpenLineMachineList(OpenLine openLine) {
        return openLineMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<OpenLine>()
                .eq(OpenLine::getEquipNo, openLine.getEquipNo())
        );
    }

    /**
     * 堵塞次数
     *
     * @return
     */
    @Override
    public JSONObject blockageTimes() {
        return getResObj("blockageTimes");
    }

    /**
     * 当前编码器记米数
     *
     * @return
     */
    @Override
    public JSONObject currentEncoderCount() {
        return getResObj("currentEncoderCount");
    }

    /**
     * 详情列表
     *
     * @return
     */
    @Override
    public JSONArray detailList() {
        List<OpenLine> cableDeviceLayingMachineList = openLineMapper.selectList(Page.of(0, 80),
                new LambdaQueryWrapper<OpenLine>().orderByDesc(OpenLine::getCreateTime));
        JSONArray result = new JSONArray();
        for (OpenLine deviceBeamlineMachine : cableDeviceLayingMachineList) {
            JSONArray resultObj = new JSONArray();
            resultObj.add(deviceBeamlineMachine.getEquipNo());
            resultObj.add("四线组开线/放线设备");
            resultObj.add("#1");
            resultObj.add(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, deviceBeamlineMachine.getCreateTime()));
            resultObj.add(deviceBeamlineMachine.getGroupType());
            resultObj.add(String.format("%.2f", deviceBeamlineMachine.getTotalFloor()));
            resultObj.add(String.format("%.2f", deviceBeamlineMachine.getCurrentEncoderCount()));
            resultObj.add(String.format("%.2f", deviceBeamlineMachine.getCurrentInSpeed()));
            resultObj.add(String.format("%.2f", deviceBeamlineMachine.getCurrentOutSpeed()));
            resultObj.add(String.format("%.2f", deviceBeamlineMachine.getCurrentSlideSpeed()));
            resultObj.add(String.format("%.2f", deviceBeamlineMachine.getCurrentCutterSpeed()));
            result.add(resultObj);
        }
        return result;
    }

    private JSONObject getResObj(String attribute) {
        List<OpenLine> list = openLineMapper.selectList(new LambdaQueryWrapper<OpenLine>().orderByDesc(OpenLine::getCreateTime).last(" limit 80"));
        List<String> timeList = new ArrayList<>();
        Map<String, BigDecimal> oneMap = new LinkedHashMap<>();
        Map<String, BigDecimal> twoMap = new LinkedHashMap<>();
        Map<String, BigDecimal> threeMap = new LinkedHashMap<>();
        Map<String, BigDecimal> fourMap = new LinkedHashMap<>();
        Map<String, BigDecimal> fiveMap = new LinkedHashMap<>();
        Map<String, BigDecimal> sixMap = new LinkedHashMap<>();
        Map<String, BigDecimal> sevenMap = new LinkedHashMap<>();
        Map<String, BigDecimal> eightMap = new LinkedHashMap<>();
        // 遍历每台设备，将数据分类存储到对应的映射中
        for (OpenLine machine : list) {
            String createTime = DateUtils.parseDateToStr("mm:ss", machine.getCreateTime());
            BigDecimal temperature = null;
            if ("blockageTimes".equals(attribute)) {
                temperature = BigDecimal.valueOf(machine.getBlockageTimes());
            } else if ("currentEncoderCount".equals(attribute)) {
                temperature = BigDecimal.valueOf(machine.getCurrentEncoderCount());
            }
            if (!timeList.contains(createTime)) {
                timeList.add(createTime);
            }
            switch (machine.getEquipNo() + machine.getGroupType()) {
                case "DL2000061A":
                    oneMap.put(createTime, temperature);
                    break;
                case "DL2000061B":
                    twoMap.put(createTime, temperature);
                    break;
                case "DL2000061C":
                    threeMap.put(createTime, temperature);
                    break;
                case "DL2000061D":
                    fourMap.put(createTime, temperature);
                    break;
                case "DL2000105A":
                    fiveMap.put(createTime, temperature);
                    break;
                case "DL2000105B":
                    sixMap.put(createTime, temperature);
                    break;
                case "DL2000105C":
                    sevenMap.put(createTime, temperature);
                    break;
                case "DL2000105D":
                    eightMap.put(createTime, temperature);
                    break;
            }
        }
        // 基于timeList创建对应的温度列表，确保时间和温度一一对应
        List<BigDecimal> oneList = new ArrayList<>();
        List<BigDecimal> twoList = new ArrayList<>();
        List<BigDecimal> threeList = new ArrayList<>();
        List<BigDecimal> fourList = new ArrayList<>();
        List<BigDecimal> fiveList = new ArrayList<>();
        List<BigDecimal> sixList = new ArrayList<>();
        List<BigDecimal> sevenList = new ArrayList<>();
        List<BigDecimal> eightList = new ArrayList<>();
        for (String time : timeList) {
            oneList.add(DataUtils.findClosestValue(oneMap, time, 2)); // 查找前后2秒内最接近的值
            twoList.add(DataUtils.findClosestValue(twoMap, time, 2));
            threeList.add(DataUtils.findClosestValue(threeMap, time, 2));
            fourList.add(DataUtils.findClosestValue(fourMap, time, 2));
            fiveList.add(DataUtils.findClosestValue(fiveMap, time, 2));
            sixList.add(DataUtils.findClosestValue(sixMap, time, 2));
            sevenList.add(DataUtils.findClosestValue(sevenMap, time, 2));
            eightList.add(DataUtils.findClosestValue(eightMap, time, 2));
        }
        JSONObject resObj = new JSONObject();
        resObj.put("timeList", timeList);
        resObj.put("oneList", oneList);
        resObj.put("twoList", twoList);
        resObj.put("threeList", threeList);
        resObj.put("fourList", fourList);
        resObj.put("fiveList", fiveList);
        resObj.put("sixList", sixList);
        resObj.put("sevenList", sevenList);
        resObj.put("eightList", eightList);
        return resObj;
    }
}
