package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.baseData.host.CleanBaseLine;

import java.util.List;

/**
 * 机座线PLC同步数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-30
 */
public interface IEquipmentBaseLineDataService extends IService<CleanBaseLine>
{
    /**
     * 查询机座线PLC同步数据列表
     * 
     * @param cleanBaseLine 机座线PLC同步数据
     * @return 机座线PLC同步数据集合
     */
    List<CleanBaseLine> selectEquipmentBaseLineDataList(CleanBaseLine cleanBaseLine);

    /**
     * 查询粗洗PH值折线图
     * @return
     */
    JSONObject roughWashingPh();

    /**
     * 查询粗洗温度折线图
     * @return
     */
    JSONObject roughWashingTemperature();

    /**
     * 查询预防锈PH
     * @return
     */
    JSONObject preventingRustPh();

    /**
     * 查询预防锈温度
     * @return
     */
    JSONObject preventingRustTemperature();

    /**
     * 查询精洗PH值折线图
     * @return
     */
    JSONObject fineWashingPh();

    /**
     * 查询精洗温度
     * @return
     */
    JSONObject fineWashingTemperature();

    /**
     * 查询强冷室温度折线图
     * @return
     */
    JSONObject strongColdChamberTemperature();

    /**
     * 查询脱水烘干温度
     * @return
     */
    JSONObject dehydrationDryingTemperature();

    /**
     * 查询油漆烘干温度
     * @return
     */
    JSONObject paintDryingTemperature();
}
