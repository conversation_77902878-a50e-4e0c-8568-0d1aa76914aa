package com.hzforward.sync.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.sync.domain.SyncCableQualityProcessInspect;
import com.hzforward.sync.domain.vo.TypeRatioRes;
import com.hzforward.sync.mapper.SyncCableQualityProcessInspectMapper;
import com.hzforward.sync.service.ICableStatQualityProcessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class CableStatQualityProcessServiceImpl implements ICableStatQualityProcessService {

    @Resource
    private SyncCableQualityProcessInspectMapper qualityProcessInspectMapper;

    /**
     * 获取检验列表
     *
     * @return
     */
    @Override
    public List<String[]> getInspectionList(String createBy) {
        List<SyncCableQualityProcessInspect> qualityProcessInspectList = qualityProcessInspectMapper.selectList(new LambdaQueryWrapper<SyncCableQualityProcessInspect>().eq(SyncCableQualityProcessInspect::getCreateBy, createBy)
                .orderByDesc(SyncCableQualityProcessInspect::getRecordDate));
        return getInspectionStrings(qualityProcessInspectList);
    }

    /**
     * 获取过程问题类型占比
     *
     * @return
     */
    @Override
    public List<TypeRatioRes> getQuestionType(String createBy) {
        List<SyncCableQualityProcessInspect> qualityProcessInspectList = qualityProcessInspectMapper.selectList(new LambdaQueryWrapper<SyncCableQualityProcessInspect>().eq(SyncCableQualityProcessInspect::getCreateBy, createBy));
        Map<String, BigDecimal> questionTypeMap = new HashMap<>();
        BigDecimal total = new BigDecimal(0);
        for (SyncCableQualityProcessInspect qualityProcessInspect : qualityProcessInspectList) {
            total = total.add(BigDecimal.valueOf(qualityProcessInspect.getUnqualifiedNo()));
            if (!questionTypeMap.containsKey(qualityProcessInspect.getProcedureName())) {
                questionTypeMap.put(qualityProcessInspect.getProcedureName(), BigDecimal.valueOf(qualityProcessInspect.getUnqualifiedNo()));
            } else {
                questionTypeMap.put(qualityProcessInspect.getProcedureName(), questionTypeMap.get(qualityProcessInspect.getProcedureName()).add(BigDecimal.valueOf(qualityProcessInspect.getUnqualifiedNo())));
            }
        }
        List<TypeRatioRes> typeRatioResList = new ArrayList<>();
        for (String key : questionTypeMap.keySet()) {
            TypeRatioRes typeRatioRes = new TypeRatioRes();
            typeRatioRes.setName(key);
            typeRatioRes.setValue(questionTypeMap.get(key).divide(total, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            typeRatioResList.add(typeRatioRes);
        }
        return typeRatioResList;
    }

    /**
     * 获取过程工序合格率
     *
     * @return
     */
    @Override
    public JSONObject getProductionProcessesQualified(String createBy) {
        List<SyncCableQualityProcessInspect> qualityProcessInspectList = qualityProcessInspectMapper.selectList(new LambdaQueryWrapper<SyncCableQualityProcessInspect>().eq(SyncCableQualityProcessInspect::getCreateBy, createBy));
        Map<String, BigDecimal> questionTypeMap = new HashMap<>();
        BigDecimal total = new BigDecimal(0);
        for (SyncCableQualityProcessInspect qualityProcessInspect : qualityProcessInspectList) {
            total = total.add(new BigDecimal(1));
            if (!questionTypeMap.containsKey(qualityProcessInspect.getProcedureName())) {
                questionTypeMap.put(qualityProcessInspect.getProcedureName(), qualityProcessInspect.getPassRate());
            } else {
                questionTypeMap.put(qualityProcessInspect.getProcedureName(), questionTypeMap.get(qualityProcessInspect.getProcedureName()).add(qualityProcessInspect.getPassRate()).divide(new BigDecimal(2), 2, RoundingMode.HALF_UP));
            }
        }
        List<String> procedureNameList = new ArrayList<>();
        List<BigDecimal> passRateList = new ArrayList<>();
        for (String key : questionTypeMap.keySet()) {
            procedureNameList.add(key);
            passRateList.add(questionTypeMap.get(key));
        }
        return new JSONObject().putOnce("procedureNameList", procedureNameList).putOnce("passRateList", passRateList);
    }

    /**
     * 过程问题责任人统计
     *
     * @return
     */
    @Override
    public JSONObject getQuestionPerson(String createBy) {
        List<SyncCableQualityProcessInspect> qualityProcessInspectList = qualityProcessInspectMapper.selectList(new LambdaQueryWrapper<SyncCableQualityProcessInspect>().eq(SyncCableQualityProcessInspect::getCreateBy, createBy));
        Map<String, BigDecimal> questionTypeMap = new HashMap<>();
        BigDecimal total = new BigDecimal(0);
        for (SyncCableQualityProcessInspect qualityProcessInspect : qualityProcessInspectList) {
            total = total.add(new BigDecimal(1));
            if (!questionTypeMap.containsKey(qualityProcessInspect.getDutyBy())) {
                questionTypeMap.put(qualityProcessInspect.getDutyBy(), new BigDecimal(1));
            } else {
                questionTypeMap.put(qualityProcessInspect.getDutyBy(), questionTypeMap.get(qualityProcessInspect.getDutyBy()).add(new BigDecimal(1)));
            }
        }
        List<String> procedureNameList = new ArrayList<>();
        List<BigDecimal> passRateList = new ArrayList<>();
        for (String key : questionTypeMap.keySet()) {
            procedureNameList.add(key);
            passRateList.add(questionTypeMap.get(key));
        }
        return new JSONObject().putOnce("dutyByList", procedureNameList).putOnce("dutyNoList", passRateList);
    }

    /**
     * 获取问题工序分类
     *
     * @return
     */
    @Override
    public JSONObject getProductionProcessesClassify(String createBy) {
        List<SyncCableQualityProcessInspect> qualityProcessInspectList = qualityProcessInspectMapper.selectList(new LambdaQueryWrapper<SyncCableQualityProcessInspect>().eq(SyncCableQualityProcessInspect::getCreateBy, createBy));
        Map<String, BigDecimal> questionTypeMap = new HashMap<>();
        BigDecimal total = new BigDecimal(0);
        for (SyncCableQualityProcessInspect qualityProcessInspect : qualityProcessInspectList) {
            total = total.add(new BigDecimal(1));
            if (!questionTypeMap.containsKey(qualityProcessInspect.getProcedureName())) {
                questionTypeMap.put(qualityProcessInspect.getProcedureName(), new BigDecimal(1));
            } else {
                questionTypeMap.put(qualityProcessInspect.getProcedureName(), questionTypeMap.get(qualityProcessInspect.getProcedureName()).add(new BigDecimal(1)));
            }
        }
        List<String> procedureNameList = new ArrayList<>();
        List<BigDecimal> passRateList = new ArrayList<>();
        for (String key : questionTypeMap.keySet()) {
            procedureNameList.add(key);
            passRateList.add(questionTypeMap.get(key));
        }
        return new JSONObject().putOnce("procedureList", procedureNameList).putOnce("procedureNoList", passRateList);
    }

    private static List<String[]> getInspectionStrings(List<SyncCableQualityProcessInspect> list) {
        List<String[]> resList = new ArrayList<>();
        //['厂家', '月份', '进料数量', '不合格数量', '合格率']
        for (SyncCableQualityProcessInspect qualityProcessInspect : list) {
            String[] res = {DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, qualityProcessInspect.getRecordDate()), qualityProcessInspect.getProcedureName(), qualityProcessInspect.getProduceNo().toString(),
                    qualityProcessInspect.getUnqualifiedNo().toString(), qualityProcessInspect.getDutyBy(), qualityProcessInspect.getProblemType(), qualityProcessInspect.getPassRate().toString()};
            resList.add(res);
        }
        return resList;
    }
}
