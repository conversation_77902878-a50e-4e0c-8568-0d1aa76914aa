package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.sync.domain.SyncMeasuringTool;

/**
 * 同步_量具Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ISyncMeasuringToolService 
{
    /**
     * 查询同步_量具
     * 
     * @param id 同步_量具主键
     * @return 同步_量具
     */
    public SyncMeasuringTool selectSyncMeasuringToolById(Long id);

    /**
     * 查询同步_量具列表
     * 
     * @param syncMeasuringTool 同步_量具
     * @return 同步_量具集合
     */
    public List<SyncMeasuringTool> selectSyncMeasuringToolList(SyncMeasuringTool syncMeasuringTool);

    /**
     * 新增同步_量具
     * 
     * @param syncMeasuringTool 同步_量具
     * @return 结果
     */
    public int insertSyncMeasuringTool(SyncMeasuringTool syncMeasuringTool);

    /**
     * 修改同步_量具
     * 
     * @param syncMeasuringTool 同步_量具
     * @return 结果
     */
    public int updateSyncMeasuringTool(SyncMeasuringTool syncMeasuringTool);

    /**
     * 批量删除同步_量具
     * 
     * @param ids 需要删除的同步_量具主键集合
     * @return 结果
     */
    public int deleteSyncMeasuringToolByIds(List<Long> ids);

    /**
     * 删除同步_量具信息
     * 
     * @param id 同步_量具主键
     * @return 结果
     */
    public int deleteSyncMeasuringToolById(Long id);
}
