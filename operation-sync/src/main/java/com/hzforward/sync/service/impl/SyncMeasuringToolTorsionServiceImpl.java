package com.hzforward.sync.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import com.hzforward.sync.mapper.SyncMeasuringToolTorsionMapper;
import com.hzforward.sync.domain.SyncMeasuringToolTorsion;
import com.hzforward.sync.service.ISyncMeasuringToolTorsionService;

import javax.annotation.Resource;

/**
 * 同步_扭力测试Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncMeasuringToolTorsionServiceImpl implements ISyncMeasuringToolTorsionService
{
    @Resource
    private SyncMeasuringToolTorsionMapper syncMeasuringToolTorsionMapper;

    /**
     * 查询同步_扭力测试
     *
     * @param id 同步_扭力测试主键
     * @return 同步_扭力测试
     */
    @Override
    public SyncMeasuringToolTorsion selectSyncMeasuringToolTorsionById(Long id)
    {
        return syncMeasuringToolTorsionMapper.selectById(id);
    }

    /**
     * 查询同步_扭力测试列表
     *
     * @param syncMeasuringToolTorsion 同步_扭力测试
     * @return 同步_扭力测试
     */
    @Override
    public List<SyncMeasuringToolTorsion> selectSyncMeasuringToolTorsionList(SyncMeasuringToolTorsion syncMeasuringToolTorsion)
    {
        return syncMeasuringToolTorsionMapper.selectList(new LambdaQueryWrapper<>());
    }

    /**
     * 新增同步_扭力测试
     *
     * @param syncMeasuringToolTorsion 同步_扭力测试
     * @return 结果
     */
    @Override
    public int insertSyncMeasuringToolTorsion(SyncMeasuringToolTorsion syncMeasuringToolTorsion)
    {
        return syncMeasuringToolTorsionMapper.insert(syncMeasuringToolTorsion);
    }

    /**
     * 修改同步_扭力测试
     *
     * @param syncMeasuringToolTorsion 同步_扭力测试
     * @return 结果
     */
    @Override
    public int updateSyncMeasuringToolTorsion(SyncMeasuringToolTorsion syncMeasuringToolTorsion)
    {
        return syncMeasuringToolTorsionMapper.updateById(syncMeasuringToolTorsion);
    }

    /**
     * 批量删除同步_扭力测试
     *
     * @param ids 需要删除的同步_扭力测试主键
     * @return 结果
     */
    @Override
    public int deleteSyncMeasuringToolTorsionByIds(List<Long> ids)
    {
        return syncMeasuringToolTorsionMapper.deleteBatchIds(ids);
    }

    /**
     * 删除同步_扭力测试信息
     *
     * @param id 同步_扭力测试主键
     * @return 结果
     */
    @Override
    public int deleteSyncMeasuringToolTorsionById(Long id)
    {
        return syncMeasuringToolTorsionMapper.deleteById(id);
    }
}
