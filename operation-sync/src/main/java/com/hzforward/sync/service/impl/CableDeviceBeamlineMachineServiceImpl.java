package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.cable.BeamLine;
import com.hzforward.oee.mapper.baseData.cable.BeamLineMapper;
import com.hzforward.sync.service.ICableDeviceBeamlineMachineService;
import com.hzforward.sync.util.DataUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 电缆-束线机650高速绞Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Service
@RequiredArgsConstructor
public class CableDeviceBeamlineMachineServiceImpl extends ServiceImpl<BeamLineMapper, BeamLine> implements ICableDeviceBeamlineMachineService
{
    private final BeamLineMapper beamLineMapper;

    /**
     * 查询电缆-束线机650高速绞列表
     *
     * @param beamLine 电缆-束线机650高速绞
     * @return 电缆-束线机650高速绞
     */
    @Override
    public List<BeamLine> selectCableDeviceBeamlineMachineList(BeamLine beamLine)
    {
        return beamLineMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<BeamLine>()
                .eq(BeamLine::getEquipNo, beamLine.getEquipNo())
        );
    }

    @Override
    public JSONObject currentLineSpeed() {
        return getResObj("lineSpeed");
    }

    @Override
    public JSONObject currentPitch() {
        return getResObj("pitch");
    }

    @Override
    public JSONObject currentTension() {
        return getResObj("tension");
    }

    @Override
    public JSONArray detailList() {
        List<BeamLine> cableDeviceLayingMachineList = beamLineMapper.selectList(Page.of(0, 20),
                new LambdaQueryWrapper<BeamLine>().orderByDesc(BeamLine::getCreateTime));
        JSONArray result = new JSONArray();
        for (BeamLine deviceBeamline : cableDeviceLayingMachineList) {
            JSONArray resultObj = new JSONArray();
            resultObj.add(deviceBeamline.getEquipNo());
            resultObj.add("650高速绞线机");
            resultObj.add("#1");
            resultObj.add(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, deviceBeamline.getCreateTime()));
            resultObj.add(deviceBeamline.getRotationalSpeed());
            resultObj.add(deviceBeamline.getCurrentLineSpeed());
            resultObj.add(deviceBeamline.getSetMetersNumber());
            resultObj.add(deviceBeamline.getCurrentMetersNumber());
            resultObj.add(deviceBeamline.getPitch());
            resultObj.add(deviceBeamline.getCurrentTension());
            result.add(resultObj);
        }
        return result;
    }

    private JSONObject getResObj(String attribute) {
        List<BeamLine> list = beamLineMapper.selectList(new LambdaQueryWrapper<BeamLine>().orderByDesc(BeamLine::getCreateTime).last(" limit 80"));
        List<String> timeList = new ArrayList<>();
        Map<String, BigDecimal> oneMap = new LinkedHashMap<>();
        Map<String, BigDecimal> twoMap = new LinkedHashMap<>();
        Map<String, BigDecimal> threeMap = new LinkedHashMap<>();
        Map<String, BigDecimal> fourMap = new LinkedHashMap<>();
        Map<String, BigDecimal> fiveMap = new LinkedHashMap<>();
        Map<String, BigDecimal> sixMap = new LinkedHashMap<>();
        // 遍历每台设备，将数据分类存储到对应的映射中
        for (BeamLine machine : list) {
            String createTime = DateUtils.parseDateToStr("mm:ss", machine.getCreateTime());
            BigDecimal temperature = null;
            if ("lineSpeed".equals(attribute)) {
                temperature = BigDecimal.valueOf(machine.getCurrentLineSpeed());
            } else if ("pitch".equals(attribute)) {
                temperature = BigDecimal.valueOf(machine.getPitch());
            } else if ("tension".equals(attribute)) {
                temperature = BigDecimal.valueOf(machine.getCurrentTension());
            }
            if (!timeList.contains(createTime)) {
                timeList.add(createTime);
            }
            switch (machine.getEquipNo()) {
                case "*********":
                    oneMap.put(createTime, temperature);
                    break;
                case "*********":
                    twoMap.put(createTime, temperature);
                    break;
                case "*********":
                    threeMap.put(createTime, temperature);
                    break;
                case "*********":
                    fourMap.put(createTime, temperature);
                    break;
                case "*********":
                    fiveMap.put(createTime, temperature);
                    break;
                case "*********":
                    sixMap.put(createTime, temperature);
                    break;
            }
        }
        // 基于timeList创建对应的温度列表，确保时间和温度一一对应
        List<BigDecimal> oneList = new ArrayList<>();
        List<BigDecimal> twoList = new ArrayList<>();
        List<BigDecimal> threeList = new ArrayList<>();
        List<BigDecimal> fourList = new ArrayList<>();
        List<BigDecimal> fiveList = new ArrayList<>();
        List<BigDecimal> sixList = new ArrayList<>();

        for (String time : timeList) {
            oneList.add(DataUtils.findClosestValue(oneMap, time, 2)); // 查找前后2秒内最接近的值
            twoList.add(DataUtils.findClosestValue(twoMap, time, 2));
            threeList.add(DataUtils.findClosestValue(threeMap, time, 2));
            fourList.add(DataUtils.findClosestValue(fourMap, time, 2));
            fiveList.add(DataUtils.findClosestValue(fiveMap, time, 2));
            sixList.add(DataUtils.findClosestValue(sixMap, time, 2));
        }
        JSONObject resObj = new JSONObject();
        resObj.put("timeList", timeList);
        resObj.put("oneList", oneList);
        resObj.put("twoList", twoList);
        resObj.put("threeList", threeList);
        resObj.put("fourList", fourList);
        resObj.put("fiveList", fiveList);
        resObj.put("sixList", sixList);
        return resObj;
    }


}
