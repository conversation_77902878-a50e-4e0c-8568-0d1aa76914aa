package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.sync.domain.SyncMeasuringTool;
import com.hzforward.sync.mapper.SyncMeasuringToolMapper;
import com.hzforward.sync.service.ISyncMeasuringToolService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 同步_量具Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncMeasuringToolServiceImpl implements ISyncMeasuringToolService
{
    @Resource
    private SyncMeasuringToolMapper syncMeasuringToolMapper;

    /**
     * 查询同步_量具
     *
     * @param id 同步_量具主键
     * @return 同步_量具
     */
    @Override
    public SyncMeasuringTool selectSyncMeasuringToolById(Long id)
    {
        return syncMeasuringToolMapper.selectById(id);
    }

    /**
     * 查询同步_量具列表
     *
     * @param syncMeasuringTool 同步_量具
     * @return 同步_量具
     */
    @Override
    public List<SyncMeasuringTool> selectSyncMeasuringToolList(SyncMeasuringTool syncMeasuringTool)
    {
        return syncMeasuringToolMapper.selectList(new LambdaQueryWrapper<>());
    }

    /**
     * 新增同步_量具
     *
     * @param syncMeasuringTool 同步_量具
     * @return 结果
     */
    @Override
    public int insertSyncMeasuringTool(SyncMeasuringTool syncMeasuringTool)
    {
        return syncMeasuringToolMapper.insert(syncMeasuringTool);
    }

    /**
     * 修改同步_量具
     *
     * @param syncMeasuringTool 同步_量具
     * @return 结果
     */
    @Override
    public int updateSyncMeasuringTool(SyncMeasuringTool syncMeasuringTool)
    {
        return syncMeasuringToolMapper.updateById(syncMeasuringTool);
    }

    /**
     * 批量删除同步_量具
     *
     * @param ids 需要删除的同步_量具主键
     * @return 结果
     */
    @Override
    public int deleteSyncMeasuringToolByIds(List<Long> ids)
    {
        return syncMeasuringToolMapper.deleteBatchIds(ids);
    }

    /**
     * 删除同步_量具信息
     *
     * @param id 同步_量具主键
     * @return 结果
     */
    @Override
    public int deleteSyncMeasuringToolById(Long id)
    {
        return syncMeasuringToolMapper.deleteById(id);
    }
}
