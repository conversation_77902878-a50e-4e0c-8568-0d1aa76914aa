package com.hzforward.sync.service.impl;

import java.util.List;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.hzforward.sync.mapper.SyncCableProductionCompleteMapper;
import com.hzforward.sync.domain.SyncCableProductionComplete;
import com.hzforward.sync.service.ISyncCableProductionCompleteService;

/**
 * 电缆车间排产完成Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class SyncCableProductionCompleteServiceImpl implements ISyncCableProductionCompleteService
{
    @Resource
    private SyncCableProductionCompleteMapper syncCableProductionCompleteMapper;

    /**
     * 查询电缆车间排产完成
     *
     * @param id 电缆车间排产完成主键
     * @return 电缆车间排产完成
     */
    @Override
    public SyncCableProductionComplete selectSyncCableProductionCompleteById(Long id)
    {
        return syncCableProductionCompleteMapper.selectById(id);
    }

    /**
     * 查询电缆车间排产完成列表
     *
     * @param syncCableProductionComplete 电缆车间排产完成
     * @return 电缆车间排产完成
     */
    @Override
    public List<SyncCableProductionComplete> selectSyncCableProductionCompleteList(SyncCableProductionComplete syncCableProductionComplete)
    {
        return syncCableProductionCompleteMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<SyncCableProductionComplete>()
                .eq(SyncCableProductionComplete::getRecordDate, syncCableProductionComplete.getRecordDate())
                .eq(SyncCableProductionComplete::getProductionNo, syncCableProductionComplete.getProductionNo())
                .eq(SyncCableProductionComplete::getCompleteNo, syncCableProductionComplete.getCompleteNo())
                .eq(SyncCableProductionComplete::getCompleteRate, syncCableProductionComplete.getCompleteRate())
                .like(SyncCableProductionComplete::getLineName, syncCableProductionComplete.getLineName())
        );
    }

    /**
     * 新增电缆车间排产完成
     *
     * @param syncCableProductionComplete 电缆车间排产完成
     * @return 结果
     */
    @Override
    public int insertSyncCableProductionComplete(SyncCableProductionComplete syncCableProductionComplete)
    {
        return syncCableProductionCompleteMapper.insert(syncCableProductionComplete);
    }

    /**
     * 修改电缆车间排产完成
     *
     * @param syncCableProductionComplete 电缆车间排产完成
     * @return 结果
     */
    @Override
    public int updateSyncCableProductionComplete(SyncCableProductionComplete syncCableProductionComplete)
    {
        return syncCableProductionCompleteMapper.updateById(syncCableProductionComplete);
    }

    /**
     * 删除电缆车间排产完成
     *
     * @param ids 需要删除的电缆车间排产完成主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSyncCableProductionCompleteByIds(List<Long> ids)
    {
        return syncCableProductionCompleteMapper.deleteBatchIds(ids);
    }

    /**
     * 导入数据
     * @param cableProductionCompletes
     * @return
     */
    @Override
    public String importData(List<SyncCableProductionComplete> cableProductionCompletes) {
        for (SyncCableProductionComplete syncCableProductionComplete : cableProductionCompletes) {
            syncCableProductionCompleteMapper.insert(syncCableProductionComplete);
        }
        return "";
    }

}
