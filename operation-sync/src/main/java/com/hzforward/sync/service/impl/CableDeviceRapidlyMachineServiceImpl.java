package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.cable.HighSpeedTwisting;
import com.hzforward.sync.service.ICableDeviceRapidlyMachineService;
import com.hzforward.oee.mapper.baseData.cable.HighSpeedTwistingMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 电缆-成缆机高速绞Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Service
@RequiredArgsConstructor
public class CableDeviceRapidlyMachineServiceImpl extends ServiceImpl<HighSpeedTwistingMapper, HighSpeedTwisting> implements ICableDeviceRapidlyMachineService
{
    private final HighSpeedTwistingMapper highSpeedTwistingMapper;

    /**
     * 查询电缆-成缆机高速绞列表
     *
     * @param highSpeedTwisting 电缆-成缆机高速绞
     * @return 电缆-成缆机高速绞
     */
    @Override
    public List<HighSpeedTwisting> selectCableDeviceRapidlyMachineList(HighSpeedTwisting highSpeedTwisting)
    {
        return highSpeedTwistingMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<HighSpeedTwisting>()
                .eq(HighSpeedTwisting::getEquipNo, highSpeedTwisting.getEquipNo())
        );
    }

    @Override
    public JSONObject currentLineSpeed() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        // 获取最新一条数据时间
        List<HighSpeedTwisting> deviceHighSpeedTwistingList = highSpeedTwistingMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<HighSpeedTwisting>().eq(HighSpeedTwisting::getEquipNo,"*********").orderByDesc(HighSpeedTwisting::getCreateTime));
        for (HighSpeedTwisting deviceHighSpeedTwisting : deviceHighSpeedTwistingList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, deviceHighSpeedTwisting.getCreateTime()));
            phs.add(String.format("%.2f", deviceHighSpeedTwisting.getCurrentLineSpeed()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        return jsonObject;
    }

    @Override
    public JSONObject currentTension() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        // 获取最新一条数据时间
        List<HighSpeedTwisting> deviceHighSpeedTwistingList = highSpeedTwistingMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<HighSpeedTwisting>().eq(HighSpeedTwisting::getEquipNo,"*********").orderByDesc(HighSpeedTwisting::getCreateTime));
        for (HighSpeedTwisting deviceHighSpeedTwisting : deviceHighSpeedTwistingList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, deviceHighSpeedTwisting.getCreateTime()));
            phs.add(String.format("%.2f", deviceHighSpeedTwisting.getCurrentTension()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        return jsonObject;
    }

    @Override
    public JSONArray detailList() {
        List<HighSpeedTwisting> cableDeviceLayingMachineList = highSpeedTwistingMapper.selectList(Page.of(0, 20),
                new LambdaQueryWrapper<HighSpeedTwisting>().eq(HighSpeedTwisting::getEquipNo,"*********")
                        .orderByDesc(HighSpeedTwisting::getCreateTime));
        JSONArray result = new JSONArray();
        for (HighSpeedTwisting deviceHighSpeedTwisting : cableDeviceLayingMachineList) {
            JSONArray resultObj = new JSONArray();
            resultObj.add(deviceHighSpeedTwisting.getEquipNo());
            resultObj.add("悬臂绞");
            resultObj.add("1#");
            resultObj.add(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, deviceHighSpeedTwisting.getCreateTime()));
            resultObj.add(deviceHighSpeedTwisting.getLayDirection());
            resultObj.add(String.format("%.2f", deviceHighSpeedTwisting.getCurrentLineSpeed()));
            resultObj.add(String.format("%.2f", deviceHighSpeedTwisting.getSetTwistDistance()));
            resultObj.add(String.format("%.2f", deviceHighSpeedTwisting.getCurrentMeter()));
            resultObj.add(String.format("%.2f", deviceHighSpeedTwisting.getFullPlateTension()));
            resultObj.add(String.format("%.2f", deviceHighSpeedTwisting.getCurrentTension()));
            result.add(resultObj);
        }
        return result;
    }
}
