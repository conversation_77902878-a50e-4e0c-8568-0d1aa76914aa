package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.baseData.host.CleanRotorLine;

import java.util.List;

/**
 * 转子线PLC同步数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-30
 */
public interface IEquipmentRotorLineDataService extends IService<CleanRotorLine>
{
    /**
     * 查询转子线PLC同步数据列表
     * 
     * @param cleanRotorLine 转子线PLC同步数据
     * @return 转子线PLC同步数据集合
     */
    List<CleanRotorLine> selectEquipmentRotorLineDataList(CleanRotorLine cleanRotorLine);

    /**
     * 查询粗洗PH值折线图
     * @return
     */
    JSONObject roughWashingPh();

    /**
     * 查询粗洗温度折线图
     * @return
     */
    JSONObject roughWashingTemperature();

    /**
     * 查询精洗PH值折线图
     * @return
     */
    JSONObject fineWashingPh();

    /**
     * 查询精洗温度折线图
     * @return
     */
    JSONObject fineWashingTemperature();

    /**
     * 查询冷却预热炉温度
     * @return
     */
    JSONObject coolingPreheatingFurnaceTemperature();

    /**
     * 查询脱水烘干温度
     * @return
     */
    JSONObject dehydrationDryingTemperature();

    /**
     * 查询预防锈PH
     * @return
     */
    JSONObject preventingRustPh();


    /**
     * 查询预防锈温度
     * @return
     */
    JSONObject preventingRustTemperature();

    /**
     * 查询主防锈PH
     * @return
     */
    JSONObject mainRustPreventionPh();

    /**
     * 查询主防锈温度
     * @return
     */
    JSONObject mainRustPreventionTemperature();
}
