package com.hzforward.sync.service.impl;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.framework.datasource.DynamicDataSourceContextHolder;
import com.hzforward.partCode.domain.PartCodeCenterType;
import com.hzforward.partCode.mapper.PartCodeCenterTypeMapper;
import com.hzforward.sync.domain.MakeInspectionView;
import com.hzforward.sync.domain.vo.InspectionRes;
import com.hzforward.sync.domain.vo.InspectionStatRes;
import com.hzforward.sync.domain.vo.TypeRatioRes;
import com.hzforward.sync.mapper.MakeInspectionViewMapper;
import com.hzforward.sync.service.ICableStatQualityService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class CableStatQualityServiceImpl implements ICableStatQualityService {

    @Resource
    private MakeInspectionViewMapper makeInspectionViewMapper;
    @Resource
    private PartCodeCenterTypeMapper partCodeCenterTypeMapper;


    /**
     * 获取报检列表
     *
     * @return
     */
    @Override
    public List<String[]> getInspectionReportList(Integer code) {
        try {
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE4.name());
            Page<MakeInspectionView> purchaseOrderViewPage = makeInspectionViewMapper.selectPage(Page.of(0, 10), new LambdaQueryWrapper<MakeInspectionView>().eq(MakeInspectionView::getWERKS, code).orderByDesc(MakeInspectionView::getCTime));
            List<MakeInspectionView> purchaseOrderViewList = purchaseOrderViewPage.getRecords();
            List<String> partCodes = new ArrayList<>();
            for (MakeInspectionView makeInspectionView : purchaseOrderViewList) {
                partCodes.add(makeInspectionView.getMATNR().substring(0, 7));
            }
            partCodes = partCodes.stream().distinct().collect(Collectors.toList());
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.MASTER.name());
            List<PartCodeCenterType> partCodeCenterTypeList = partCodeCenterTypeMapper.selectPartCodeCenterTypeByCenterTypeCodes(partCodes);
            Map<String, String> partCodeCenterTypeMap = partCodeCenterTypeList.stream().collect(Collectors.toMap(PartCodeCenterType::getCenterTypeCode, PartCodeCenterType::getCenterTypeName, (existingValue, newValue) -> existingValue));
            return getChaseOrderViewStrings(purchaseOrderViewList, partCodeCenterTypeMap);
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    @Override
    @DataSource(DataSourceType.SLAVE4)
    public List<String[]> getInspectionList(Integer code) {
        Date startTime = DateUtils.getTodayStartTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM + "-01", DateUtils.getNowDate())));
        Date endTime = DateUtils.getTodayLastTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(DateUtils.addMonths(startTime, 1), -1))));
        List<MakeInspectionView> purchaseOrderViewList = makeInspectionViewMapper.selectList(new LambdaQueryWrapper<MakeInspectionView>()
                .eq(MakeInspectionView::getWERKS, code)
                .ge(MakeInspectionView::getCTime, startTime)
                .le(MakeInspectionView::getCTime, endTime)
                .orderByDesc(MakeInspectionView::getCTime));
        List<InspectionRes> inspectionResList = new ArrayList<>();
        Map<String, InspectionRes> inspectionResMap = new HashMap<>();
        for (MakeInspectionView makeInspectionView : purchaseOrderViewList) {
            // 校验数据是否可用
            if (makeInspectionView.getInspectionQty() == null || makeInspectionView.getUnQualifiedQty() == null) {
                continue;
            }
            InspectionRes inspectionRes;
            if (!inspectionResMap.containsKey(makeInspectionView.getSupplyerName1())) {
                inspectionRes = new InspectionRes();
                inspectionRes.setSupplierName(makeInspectionView.getSupplyerName1());
                inspectionRes.setInQuantity(makeInspectionView.getInspectionQty());
                inspectionRes.setMonth(DateUtils.parseDateToStr(DateUtils.MM, makeInspectionView.getCTime()));
                inspectionRes.setUnqualifiedQuantity(makeInspectionView.getUnQualifiedQty());
                inspectionResMap.put(makeInspectionView.getSupplyerName1(), inspectionRes);
            } else {
                inspectionRes = inspectionResMap.get(makeInspectionView.getSupplyerName1());
                inspectionRes.setInQuantity(inspectionRes.getInQuantity().add(makeInspectionView.getInspectionQty()));
                inspectionRes.setUnqualifiedQuantity(inspectionRes.getUnqualifiedQuantity().add(makeInspectionView.getUnQualifiedQty()));
            }
        }
        for (String key : inspectionResMap.keySet()) {
            InspectionRes inspectionRes = inspectionResMap.get(key);
            inspectionRes.setQualifiedRate(inspectionRes.getInQuantity().subtract(inspectionRes.getUnqualifiedQuantity()).divide(inspectionRes.getInQuantity(), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
            inspectionResList.add(inspectionRes);
        }
        return getInspectionStrings(inspectionResList);
    }

    /**
     * 获取检验统计
     *
     * @return
     */
    @Override
    @DataSource(DataSourceType.SLAVE4)
    public InspectionStatRes getInspectionStat(Integer code) {
        Date startTime = DateUtils.getTodayStartTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM + "-01", DateUtils.getNowDate())));
        Date endTime = DateUtils.getTodayLastTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(DateUtils.addMonths(startTime, 1), -1))));
        List<MakeInspectionView> purchaseOrderViewList = makeInspectionViewMapper.selectList(new LambdaQueryWrapper<MakeInspectionView>()
                .eq(MakeInspectionView::getWERKS, code)
                .ge(MakeInspectionView::getCTime, startTime)
                .le(MakeInspectionView::getCTime, endTime)
                .orderByDesc(MakeInspectionView::getCTime));
        InspectionStatRes inspectionStatRes = new InspectionStatRes();
        inspectionStatRes.setInspectionReportRate(new BigDecimal(0));
        inspectionStatRes.setReceivedAndVerified(new BigDecimal(0));
        BigDecimal total = new BigDecimal(0);
        for (MakeInspectionView makeInspectionView : purchaseOrderViewList) {
            if (makeInspectionView.getInspectionQty() != null) {
                total = total.add(makeInspectionView.getInspectionQty());
            }
            if (makeInspectionView.getStorageQty() != null) {
                inspectionStatRes.setReceivedAndVerified(inspectionStatRes.getReceivedAndVerified().add(makeInspectionView.getStorageQty()));
            }
        }
        inspectionStatRes.setInspectionReportRate(inspectionStatRes.getReceivedAndVerified().divide(total, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        return inspectionStatRes;
    }

    /**
     * 获取类型比
     *
     * @return
     */
    @Override
    public List<TypeRatioRes> getTypeRatio(Integer code) {
        try {
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE4.name());
            Date startTime = DateUtils.getTodayStartTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM + "-01", DateUtils.getNowDate())));
            Date endTime = DateUtils.getTodayLastTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(DateUtils.addMonths(startTime, 1), -1))));
            if (code.equals(2002)){
                startTime = DateUtils.getTodayStartTime(DateUtils.addDays(DateUtils.getNowDate(),-3));
                endTime = DateUtils.getTodayLastTime(DateUtils.getNowDate());
            }
            List<MakeInspectionView> purchaseOrderViewList = makeInspectionViewMapper.selectList(new LambdaQueryWrapper<MakeInspectionView>().eq(MakeInspectionView::getWERKS, code).ge(MakeInspectionView::getCTime, startTime)
                    .le(MakeInspectionView::getCTime, endTime).orderByDesc(MakeInspectionView::getCTime));
            List<String> partCodes = new ArrayList<>();
            for (MakeInspectionView makeInspectionView : purchaseOrderViewList) {
                if (makeInspectionView.getMATNR() != null && makeInspectionView.getMATNR().length() >= 7) {
                    partCodes.add(makeInspectionView.getMATNR().substring(0, 7));
                }
            }
            partCodes = partCodes.stream().distinct().collect(Collectors.toList());
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.MASTER.name());
            List<PartCodeCenterType> partCodeCenterTypeList = partCodeCenterTypeMapper.selectPartCodeCenterTypeByCenterTypeCodes(partCodes);
            Map<String, String> partCodeCenterTypeMap = partCodeCenterTypeList.stream().collect(Collectors.toMap(PartCodeCenterType::getCenterTypeCode, PartCodeCenterType::getCenterTypeName, (existingValue, newValue) -> existingValue));
            Map<String, BigDecimal> typeQuantityMap = new HashMap<>();
            BigDecimal total = new BigDecimal(0);
            for (MakeInspectionView makeInspectionView : purchaseOrderViewList) {
                if (makeInspectionView.getStorageQty() != null) {
                    total = total.add(makeInspectionView.getStorageQty());
                    if (makeInspectionView.getMATNR() != null && makeInspectionView.getMATNR().length() >= 7) {
                        String key = partCodeCenterTypeMap.get(makeInspectionView.getMATNR().substring(0, 7));
                        if (!typeQuantityMap.containsKey(key)) {
                            typeQuantityMap.put(key, makeInspectionView.getStorageQty());
                        } else {
                            typeQuantityMap.put(key, typeQuantityMap.get(key).add(makeInspectionView.getStorageQty()));
                        }
                    }
                }
            }
            List<TypeRatioRes> typeRatioResList = new ArrayList<>();
            for (String key : typeQuantityMap.keySet()) {
                TypeRatioRes typeRatioRes = new TypeRatioRes();
                typeRatioRes.setName(key);
                typeRatioRes.setValue(typeQuantityMap.get(key).divide(total, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
                if (typeRatioRes.getValue().compareTo(BigDecimal.ZERO) != 0) {
                    typeRatioResList.add(typeRatioRes);
                }
            }
            return typeRatioResList;
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    /**
     * 月度合格率
     *
     * @return
     */
    @Override
    @DataSource(DataSourceType.SLAVE4)
    public JSONObject getMonthQualifiedList(Integer code) {
        Date startTime = DateUtils.getTodayStartTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM + "-01", DateUtils.addMonths(DateUtils.getNowDate(), -2))));
        Date endTime = DateUtils.getTodayLastTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(DateUtils.addMonths(startTime, 3), -1))));
        List<MakeInspectionView> purchaseOrderViewList = makeInspectionViewMapper.selectList(new LambdaQueryWrapper<MakeInspectionView>()
                .eq(MakeInspectionView::getWERKS, code)
                .ge(MakeInspectionView::getCTime, startTime)
                .le(MakeInspectionView::getCTime, endTime)
                .orderByAsc(MakeInspectionView::getCTime));
        Map<String, BigDecimal> monthTotalMap = new HashMap<>();
        Map<String, BigDecimal> monthUnqualifiedMap = new HashMap<>();
        for (MakeInspectionView makeInspectionView : purchaseOrderViewList) {
            // 校验数据是否可用
            if (makeInspectionView.getInspectionQty() == null || makeInspectionView.getUnQualifiedQty() == null) {
                continue;
            }
            String key = DateUtils.parseDateToStr(DateUtils.MM, makeInspectionView.getCTime());
            if (!monthTotalMap.containsKey(key)) {
                monthTotalMap.put(key, makeInspectionView.getInspectionQty());
            } else {
                monthTotalMap.put(key, monthTotalMap.get(key).add(makeInspectionView.getInspectionQty()));
            }
            if (!monthUnqualifiedMap.containsKey(key)) {
                monthUnqualifiedMap.put(key, makeInspectionView.getUnQualifiedQty());
            } else {
                monthUnqualifiedMap.put(key, monthUnqualifiedMap.get(key).add(makeInspectionView.getUnQualifiedQty()));
            }
        }
        List<String> monthList = new ArrayList<>();
        List<BigDecimal> qualifiedList = new ArrayList<>();
        for (String key : monthTotalMap.keySet()) {
            monthList.add(key);
            qualifiedList.add(monthTotalMap.get(key).subtract(monthUnqualifiedMap.get(key)).divide(monthTotalMap.get(key),4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)));
        }
        return new JSONObject().putOnce("monthList", monthList).putOnce("qualifiedList", qualifiedList);
    }

    /**
     * 收料合格
     *
     * @return
     */
    @Override
    @DataSource(DataSourceType.SLAVE4)
    public JSONObject getInQualified(Integer code) {
        Date startTime = DateUtils.getTodayStartTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM + "-01", DateUtils.getNowDate())));
        Date endTime = DateUtils.getTodayLastTime(DateUtils.parseDate(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(DateUtils.addMonths(startTime, 1), -1))));
        List<MakeInspectionView> purchaseOrderViewList = makeInspectionViewMapper.selectList(new LambdaQueryWrapper<MakeInspectionView>()
                .eq(MakeInspectionView::getWERKS, code)
                .ge(MakeInspectionView::getCTime, startTime)
                .le(MakeInspectionView::getCTime, endTime)
                .orderByAsc(MakeInspectionView::getCTime));
        Map<String, BigDecimal> monthTotalMap = new HashMap<>();
        Map<String, BigDecimal> monthUnqualifiedMap = new HashMap<>();
        for (MakeInspectionView makeInspectionView : purchaseOrderViewList) {
            // 校验数据是否可用
            if (makeInspectionView.getInspectionQty() == null || makeInspectionView.getUnQualifiedQty() == null) {
                continue;
            }
            String key = DateUtils.parseDateToStr(DateUtils.MM_DD, makeInspectionView.getCTime());
            if (!monthTotalMap.containsKey(key)) {
                monthTotalMap.put(key, makeInspectionView.getInspectionQty());
            } else {
                monthTotalMap.put(key, monthTotalMap.get(key).add(makeInspectionView.getInspectionQty()));
            }
            if (!monthUnqualifiedMap.containsKey(key)) {
                monthUnqualifiedMap.put(key, makeInspectionView.getUnQualifiedQty());
            } else {
                monthUnqualifiedMap.put(key, monthUnqualifiedMap.get(key).add(makeInspectionView.getUnQualifiedQty()));
            }
        }
        monthTotalMap = sortMap(monthTotalMap);
        monthUnqualifiedMap = sortMap(monthUnqualifiedMap);
        List<String> monthList = new ArrayList<>();
        List<BigDecimal> inspectionList = new ArrayList<>();
        List<BigDecimal> qualifiedList = new ArrayList<>();
        for (String key : monthTotalMap.keySet()) {
            monthList.add(key);
            inspectionList.add(monthTotalMap.get(key));
            qualifiedList.add(monthTotalMap.get(key).subtract(monthUnqualifiedMap.get(key)));
        }
        return new JSONObject().putOnce("dayList", monthList).putOnce("qualifiedList", qualifiedList).putOnce("inspectionList", inspectionList);
    }

    private Map<String,BigDecimal> sortMap(Map<String,BigDecimal> dateMap) {
        // 日期格式化工具
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");

        // 排序 Map，生成 LinkedHashMap
        return dateMap.entrySet().stream()
                .sorted((entry1, entry2) -> {
                    try {
                        Date date1 = dateFormat.parse(entry1.getKey());
                        Date date2 = dateFormat.parse(entry2.getKey());
                        return date1.compareTo(date2);
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing, // 合并函数
                        LinkedHashMap::new // 使用 LinkedHashMap 保持插入顺序
                ));
    }

    private static List<String[]> getChaseOrderViewStrings(List<MakeInspectionView> list, Map<String, String> partCodeCenterTypeMap) {
        List<String[]> resList = new ArrayList<>();
        //['报检单号', '物料号', '物料名称', '报检时间', '分类']
        for (MakeInspectionView makeInspectionView : list) {
            String[] res = {makeInspectionView.getInspectionNo(), makeInspectionView.getInspectionLine(), makeInspectionView.getMATNR(), makeInspectionView.getTXZ01(),
                    DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, makeInspectionView.getCTime()), partCodeCenterTypeMap.get(makeInspectionView.getMATNR().substring(0, 7))};
            resList.add(res);
        }
        return resList;
    }

    private static List<String[]> getInspectionStrings(List<InspectionRes> list) {
        List<String[]> resList = new ArrayList<>();
        //['厂家', '月份', '进料数量', '不合格数量', '合格率']
        for (InspectionRes inspectionRes : list) {
            String[] res = {inspectionRes.getSupplierName(), inspectionRes.getMonth(), inspectionRes.getInQuantity().toString(), inspectionRes.getUnqualifiedQuantity().toString(), inspectionRes.getQualifiedRate().toString()};
            resList.add(res);
        }
        return resList;
    }

}
