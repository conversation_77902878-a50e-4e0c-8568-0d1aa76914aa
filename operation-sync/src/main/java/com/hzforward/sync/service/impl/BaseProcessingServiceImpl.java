package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.core.domain.entity.SysDictData;
import com.hzforward.common.core.redis.RedisCache;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.DictUtils;
import com.hzforward.equipmentManage.service.ILedgerInfoService;
import com.hzforward.oee.domain.baseData.machining.CncRealTimeInfoRow;
import com.hzforward.oee.mapper.baseData.machining.CncRealTimeInfoRowMapper;
import com.hzforward.sync.service.IBaseProcessingService;
import com.hzforward.sync.util.DeviceUtils;
import com.hzforward.sync.util.ExcelUtils;
import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileInputStream;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class BaseProcessingServiceImpl implements IBaseProcessingService {

    @Resource
    private CncRealTimeInfoRowMapper cncRealTimeInfoRowMapper;
    @Resource
    private RedisCache redisCache;
    @Resource
    private ILedgerInfoService ledgerInfoService;

    /**
     * 获取设备列表
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE7)
    public JSONArray getDeviceList(String line) {
        SysDictData date = DictUtils.getDictCache("big_screen_data", "line_device_contrast");
        JSONObject lineDeviceContrast = JSON.parseObject(date.getRemark());
        if (!lineDeviceContrast.containsKey(line)) {
            throw new RuntimeException("线体不存在");
        }
        JSONArray baseProcessing = lineDeviceContrast.getJSONArray(line);
        List<String> deviceNos = new ArrayList<>();
        for (int i = 0; i < baseProcessing.size(); i++) {
            deviceNos.add(baseProcessing.getJSONObject(i).getString("deviceId"));
        }
        //判断 当前时间是否超过7:30 如果超过就获取前一天7:30 否则获取当天7:30 作为开始时间
        Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd 07:30:00", DateUtils.getNowDate()));
        if (startTime.getTime() > System.currentTimeMillis()) {
            startTime = DateUtils.addDays(startTime, -1);
        }
        List<CncRealTimeInfoRow> cncRealTimeInfoList = cncRealTimeInfoRowMapper.selectList(new LambdaQueryWrapper<CncRealTimeInfoRow>().in(CncRealTimeInfoRow::getEquipNo, deviceNos)
                .ge(CncRealTimeInfoRow::getCreateTime, startTime).le(CncRealTimeInfoRow::getCreateTime, DateUtils.getNowDate()).orderByDesc(CncRealTimeInfoRow::getCreateTime));
        //获取利用率计算数据
        Date userEndTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd 07:30:00", DateUtils.getNowDate()));
        if (startTime.getTime() > System.currentTimeMillis()) {
            userEndTime = DateUtils.addDays(userEndTime, -1);
        }
        Date userStartTime = DateUtils.addDays(userEndTime, -1);
        List<CncRealTimeInfoRow> useCncRealTimeInfoList = cncRealTimeInfoRowMapper.selectList(new LambdaQueryWrapper<CncRealTimeInfoRow>()
                .in(CncRealTimeInfoRow::getEquipNo, deviceNos).ge(CncRealTimeInfoRow::getCreateTime, userStartTime)
                .le(CncRealTimeInfoRow::getCreateTime, userEndTime).orderByDesc(CncRealTimeInfoRow::getCreateTime));
        Map<String, List<CncRealTimeInfoRow>> useCncRealTimeInfoMap = useCncRealTimeInfoList.stream().collect(Collectors.groupingBy(CncRealTimeInfoRow::getEquipNo));
        //按照设备号分组
        Map<String, List<CncRealTimeInfoRow>> cncRealTimeInfoMap = cncRealTimeInfoList.stream().collect(Collectors.groupingBy(CncRealTimeInfoRow::getEquipNo));
        JSONArray result = new JSONArray();
        for (String key : cncRealTimeInfoMap.keySet()) {
            List<CncRealTimeInfoRow> cncRealTimeInfos = cncRealTimeInfoMap.get(key);
            CncRealTimeInfoRow cncRealTimeInfo = cncRealTimeInfos.get(0);
            JSONArray resultObj = new JSONArray();
            resultObj.add(cncRealTimeInfo.getEquipNo());
            resultObj.add(ledgerInfoService.getInfoByEquipNo(cncRealTimeInfo.getEquipNo()).getEquipName());
            resultObj.add(line);
            resultObj.add(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, cncRealTimeInfo.getCreateTime()));
            resultObj.add(cncRealTimeInfo.getCncMainProName());
            resultObj.add(cncRealTimeInfo.getCncSrate() + "%");
            resultObj.add(cncRealTimeInfo.getCncFrate() + "%");
            resultObj.add(cncRealTimeInfo.getCncRapidfeed() + "%");
            resultObj.add(cncRealTimeInfo.getCncSetsspeed());
            resultObj.add(cncRealTimeInfo.getCncActsspeed());
            resultObj.add(cncRealTimeInfo.getCncSetfspeed());
            resultObj.add(cncRealTimeInfo.getCncActfspeed());
            // 运行状态
//            if (System.currentTimeMillis() - cncRealTimeInfo.cTime.getTime() > 1000 * 60 * 10) {
//                resultObj.add(3);
//            } else if (StringUtils.isNotEmpty(cncRealTimeInfo.getCncAlarmMsg())) {
//                resultObj.add(2);
//            } else {
//                resultObj.add(1);
//            }
            resultObj.add(DeviceUtils.getDeviceStatus(cncRealTimeInfo.getEquipType(), cncRealTimeInfo.getCncRunStatus()));
            resultObj.add(cncRealTimeInfo.getCncMode());
            resultObj.add(cncRealTimeInfo.getCncAlarmMsg());
            resultObj.add(cncRealTimeInfo.getCncRuntime());
            try {
                // 当班加工数
                int dayNo = cncRealTimeInfo.getCncProducts() - cncRealTimeInfos.get(cncRealTimeInfos.size() - 1).getCncProducts();
                if (cncRealTimeInfo.getEquipNo().equals("001")) {
                    dayNo = dayNo / 2;
                }
                resultObj.add(dayNo);
                // 设备利用率
                if (!useCncRealTimeInfoMap.containsKey(key)) {
                    resultObj.add("");
                    result.add(resultObj);
                    continue;
                }
                List<CncRealTimeInfoRow> useCncRealTimeList = useCncRealTimeInfoMap.get(key);
                int currentNo = useCncRealTimeList.get(0).getCncRuntime();
                CncRealTimeInfoRow lastInfo = null;
                int runtime = currentNo - useCncRealTimeList.get(useCncRealTimeList.size() - 1).getCncRuntime();
                for (CncRealTimeInfoRow realTimeInfo : useCncRealTimeList) {
                    if (currentNo > realTimeInfo.getCncRuntime()) {
                        lastInfo = realTimeInfo;
                        break;
                    }
                }
                if (lastInfo != null) {
                    BigDecimal userRate = new BigDecimal(runtime).divide(BigDecimal.valueOf((DateUtils.differentSecondByMillisecond(userStartTime, lastInfo.getCreateTime()))), 2, RoundingMode.HALF_UP);
                    resultObj.add(userRate.multiply(new BigDecimal(100)) + "%");
                } else {
                    resultObj.add("");
                }
                result.add(resultObj);
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }
        return result;
    }

    /**
     * 获取排产完成
     *
     * @param line
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE7)
    public JSONObject getProductionComplete(String line) {
        String networkPath = DictUtils.getDictValue("big_screen_data", "network_path");
        try {
            // jcifs 认证信息
            NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(
                    "elevator.xizi.local", "hu.hongjun", "hhjHHJ1314520."); // 请替换为你的用户名和密码
            SmbFile smbFile = new SmbFile(networkPath, auth);
            InputStream in = new SmbFileInputStream(smbFile);
            XSSFWorkbook workbook = new XSSFWorkbook(in);
            Sheet sheet = workbook.getSheet("Sheet1");
            List<List<String>> resultList = new ArrayList<>();
            if (sheet != null) {
                for (Row row : sheet) {
                    List<String> rowList = new ArrayList<>();
                    for (Cell cell : row) {
                        String cellValue = ExcelUtils.getCellValue(cell);
                        rowList.add(cellValue);
                    }
                    resultList.add(rowList);
                }
            }
            if (resultList.size() > 8) {
                resultList.subList(1, resultList.size() - 7).clear();
            }
            int index = 0;
            for (int i = 0; i < resultList.get(0).size(); i++) {
                if (resultList.get(0).get(i).equals(line)) index = i;
            }
            List<String> times = new ArrayList<>();
            List<Integer> productions = new ArrayList<>();
            List<Integer> completes = new ArrayList<>();
            for (int i = 0; i < resultList.size(); i++) {
                if (i == 0) continue;
                if (resultList.get(i).size() >= index + 1) {
                    times.add(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.parseDate(resultList.get(i).get(0))));
                    productions.add((int) Double.parseDouble(resultList.get(i).get(index)));
                }
            }
            SysDictData date = DictUtils.getDictCache("big_screen_data", "line_device_contrast");
            JSONObject lineDeviceContrast = JSON.parseObject(date.getRemark());
            if (!lineDeviceContrast.containsKey(line)) {
                throw new RuntimeException("线体不存在");
            }
            JSONArray baseProcessing = lineDeviceContrast.getJSONArray(line);
            for (String time : times) {
                int completeNo = 0;
                for (int i = 0; i < baseProcessing.size(); i++) {
                    //判断 当前时间是否超过7:30 如果超过就获取前一天7:30 否则 获取当天7:30 作为开始时间
                    Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd 07:30:00", DateUtils.parseDate(time, DateUtils.YYYY_MM_DD)));
                    Date endTime = DateUtils.addDays(startTime, 1);
                    JSONObject device = baseProcessing.getJSONObject(i);
                    String deviceNo = device.getString("deviceId");
                    if (device.getBoolean("isCompute")) {
                        List<CncRealTimeInfoRow> start = cncRealTimeInfoRowMapper.selectList(Page.of(0, 1), new LambdaQueryWrapper<CncRealTimeInfoRow>()
                                .ge(CncRealTimeInfoRow::getCreateTime, startTime)
                                .le(CncRealTimeInfoRow::getCreateTime, endTime)
                                .eq(CncRealTimeInfoRow::getEquipNo, deviceNo).orderByAsc(CncRealTimeInfoRow::getCreateTime));
                        List<CncRealTimeInfoRow> end = cncRealTimeInfoRowMapper.selectList(Page.of(0, 1), new LambdaQueryWrapper<CncRealTimeInfoRow>()
                                .ge(CncRealTimeInfoRow::getCreateTime, startTime)
                                .le(CncRealTimeInfoRow::getCreateTime, endTime)
                                .eq(CncRealTimeInfoRow::getEquipNo, deviceNo).orderByDesc(CncRealTimeInfoRow::getCreateTime));
                        if (!end.isEmpty() && !start.isEmpty()) {
                            try {
                                if (deviceNo.equals("001")) {
                                    completeNo += ((end.get(0).getCncProducts() - start.get(0).getCncProducts()) / 2);
                                } else {
                                    completeNo += end.get(0).getCncProducts() - start.get(0).getCncProducts();
                                }
                            } catch (NumberFormatException e) {
                                continue;
                            }
                        }
                    }
                }
                completes.add(completeNo);
            }
            workbook.close();
            in.close();
            JSONObject resObj = new JSONObject();
            resObj.put("times", times);
            resObj.put("productions", productions);
            resObj.put("completes", completes);
            return resObj;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取今日排产完成
     *
     * @param line
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE7)
    public JSONObject getTodayProductionComplete(String line) {
        String networkPath = DictUtils.getDictValue("big_screen_data", "network_path");
        try {
            // jcifs 认证信息
            NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(
                    "elevator.xizi.local", "hu.hongjun", "hhjHHJ1314520."); // 请替换为你的用户名和密码
            SmbFile smbFile = new SmbFile(networkPath, auth);
            InputStream in = new SmbFileInputStream(smbFile);
            XSSFWorkbook workbook = new XSSFWorkbook(in);
            Sheet sheet = workbook.getSheet("Sheet1");
            List<List<String>> resultList = new ArrayList<>();
            if (sheet != null) {
                for (Row row : sheet) {
                    List<String> rowList = new ArrayList<>();
                    for (Cell cell : row) {
                        String cellValue = ExcelUtils.getCellValue(cell);
                        rowList.add(cellValue);
                    }
                    resultList.add(rowList);
                }
            }
            workbook.close();
            in.close();
            int index = 0;
            for (int i = 0; i < resultList.get(0).size(); i++) {
                if (resultList.get(0).get(i).equals(line)) index = i;
            }
            int productionNo = 0;
            int completeNo = 0;
            BigDecimal rate = new BigDecimal(0);
            for (int i = 0; i < resultList.size(); i++) {
                if (i == 0) continue;
                if (resultList.get(i).size() >= index + 1) {
                    if (DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.parseDate(resultList.get(i).get(0))).equals(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                        productionNo = (int) Double.parseDouble(resultList.get(i).get(index));
                    }
                }
            }
            SysDictData date = DictUtils.getDictCache("big_screen_data", "line_device_contrast");
            JSONObject lineDeviceContrast = JSON.parseObject(date.getRemark());
            if (!lineDeviceContrast.containsKey(line)) {
                throw new RuntimeException("线体不存在");
            }
            JSONArray baseProcessing = lineDeviceContrast.getJSONArray(line);

            //判断 当前时间是否超过7:30 如果超过就获取前一天7:30 否则获取当天7:30 作为开始时间
            Date startTime = DateUtils.parseDate(DateUtils.parseDateToStr("yyyy-MM-dd 07:30:00", DateUtils.getNowDate()));
            if (startTime.getTime() > System.currentTimeMillis()) {
                startTime = DateUtils.addDays(startTime, -1);
            }

            for (int i = 0; i < baseProcessing.size(); i++) {
                JSONObject device = baseProcessing.getJSONObject(i);
                String deviceNo = device.getString("deviceId");
                if (device.getBoolean("isCompute")) {
                    List<CncRealTimeInfoRow> start = cncRealTimeInfoRowMapper.selectList(Page.of(0, 1), new LambdaQueryWrapper<CncRealTimeInfoRow>()
                            .ge(CncRealTimeInfoRow::getCreateTime, startTime)
                            .le(CncRealTimeInfoRow::getCreateTime, DateUtils.getNowDate())
                            .eq(CncRealTimeInfoRow::getEquipNo, deviceNo).orderByAsc(CncRealTimeInfoRow::getCreateTime));
                    List<CncRealTimeInfoRow> end = cncRealTimeInfoRowMapper.selectList(Page.of(0, 1), new LambdaQueryWrapper<CncRealTimeInfoRow>()
                            .ge(CncRealTimeInfoRow::getCreateTime, startTime)
                            .le(CncRealTimeInfoRow::getCreateTime, DateUtils.getNowDate())
                            .eq(CncRealTimeInfoRow::getEquipNo, deviceNo).orderByDesc(CncRealTimeInfoRow::getCreateTime));
                    if (!end.isEmpty() && !start.isEmpty()) {
                        try {
                            if (deviceNo.equals("001")) {
                                completeNo += ((end.get(0).getCncProducts() - start.get(0).getCncProducts()) / 2);
                            } else {
                                completeNo += end.get(0).getCncProducts() - start.get(0).getCncProducts();
                            }
                        } catch (NumberFormatException e) {
                            continue;
                        }
                    }
                }
            }
            if (productionNo != 0) {
                rate = new BigDecimal(completeNo).divide(new BigDecimal(productionNo), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            }
            JSONObject resObj = new JSONObject();
            resObj.put("rate", rate);
            resObj.put("completeNo", completeNo);
            resObj.put("productionNo", productionNo);
            return resObj;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


}
