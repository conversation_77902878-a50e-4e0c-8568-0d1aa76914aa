package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.sync.domain.SyncCableProductionComplete;

/**
 * 电缆车间排产完成Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface ISyncCableProductionCompleteService 
{
    /**
     * 查询电缆车间排产完成
     * 
     * @param id 电缆车间排产完成主键
     * @return 电缆车间排产完成
     */
    SyncCableProductionComplete selectSyncCableProductionCompleteById(Long id);

    /**
     * 查询电缆车间排产完成列表
     * 
     * @param syncCableProductionComplete 电缆车间排产完成
     * @return 电缆车间排产完成集合
     */
    List<SyncCableProductionComplete> selectSyncCableProductionCompleteList(SyncCableProductionComplete syncCableProductionComplete);

    /**
     * 新增电缆车间排产完成
     * 
     * @param syncCableProductionComplete 电缆车间排产完成
     * @return 结果
     */
    int insertSyncCableProductionComplete(SyncCableProductionComplete syncCableProductionComplete);

    /**
     * 修改电缆车间排产完成
     * 
     * @param syncCableProductionComplete 电缆车间排产完成
     * @return 结果
     */
    int updateSyncCableProductionComplete(SyncCableProductionComplete syncCableProductionComplete);

    /**
     * 删除电缆车间排产完成
     * 
     * @param ids 需要删除的电缆车间排产完成主键集合
     * @return 结果
     */
    int deleteSyncCableProductionCompleteByIds(List<Long> ids);

    /**
     * 导入数据
     * @param cableProductionCompletes
     * @return
     */
    String importData(List<SyncCableProductionComplete> cableProductionCompletes);
}
