package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.sync.domain.SyncProduction;
import com.hzforward.sync.domain.vo.SyncProductionQualified;
import com.hzforward.sync.domain.vo.SyncProductionQualifiedRes;

/**
 * 同步_排产数据Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ISyncProductionService 
{
    /**
     * 查询同步_排产数据
     *
     * @param id 同步_排产数据主键
     * @return 同步_排产数据
     */
    public SyncProduction selectSyncProductionById(Long id);

    /**
     * 查询同步_排产数据列表
     * 
     * @param syncProduction 同步_排产数据
     * @return 同步_排产数据集合
     */
    public List<SyncProduction> selectSyncProductionList(SyncProduction syncProduction);

    /**
     * 新增同步_排产数据
     * 
     * @param syncProduction 同步_排产数据
     * @return 结果
     */
    public int insertSyncProduction(SyncProduction syncProduction);

    /**
     * 修改同步_排产数据
     * 
     * @param syncProduction 同步_排产数据
     * @return 结果
     */
    public int updateSyncProduction(SyncProduction syncProduction);

    /**
     * 批量删除同步_排产数据
     * 
     * @param ids 需要删除的同步_排产数据主键集合
     * @return 结果
     */
    public int deleteSyncProductionByIds(List<Long> ids);

    /**
     * 删除同步_排产数据信息
     * 
     * @param id 同步_排产数据主键
     * @return 结果
     */
    public int deleteSyncProductionById(Long id);

    /**
     * 查询合格
     * @param syncProductionQualified
     * @return
     */
    List<SyncProductionQualifiedRes> queryQualified(SyncProductionQualified syncProductionQualified);
}
