package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.sync.domain.SyncPressurizationNaiya;

/**
 * 耐压测试_耐压Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ISyncPressurizationNaiyaService 
{
    /**
     * 查询耐压测试_耐压
     * 
     * @param abh 耐压测试_耐压主键
     * @return 耐压测试_耐压
     */
    public SyncPressurizationNaiya selectSyncPressurizationNaiyaByAbh(Long abh);

    /**
     * 查询耐压测试_耐压列表
     * 
     * @param syncPressurizationNaiya 耐压测试_耐压
     * @return 耐压测试_耐压集合
     */
    public List<SyncPressurizationNaiya> selectSyncPressurizationNaiyaList(SyncPressurizationNaiya syncPressurizationNaiya);

    /**
     * 新增耐压测试_耐压
     * 
     * @param syncPressurizationNaiya 耐压测试_耐压
     * @return 结果
     */
    public int insertSyncPressurizationNaiya(SyncPressurizationNaiya syncPressurizationNaiya);

    /**
     * 修改耐压测试_耐压
     * 
     * @param syncPressurizationNaiya 耐压测试_耐压
     * @return 结果
     */
    public int updateSyncPressurizationNaiya(SyncPressurizationNaiya syncPressurizationNaiya);

    /**
     * 批量删除耐压测试_耐压
     * 
     * @param abhs 需要删除的耐压测试_耐压主键集合
     * @return 结果
     */
    public int deleteSyncPressurizationNaiyaByAbhs(List<Long> abhs);

    /**
     * 删除耐压测试_耐压信息
     * 
     * @param abh 耐压测试_耐压主键
     * @return 结果
     */
    public int deleteSyncPressurizationNaiyaByAbh(Long abh);
}
