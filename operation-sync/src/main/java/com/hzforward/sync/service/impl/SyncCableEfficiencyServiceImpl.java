package com.hzforward.sync.service.impl;

import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.sync.domain.SyncCableEfficiency;
import com.hzforward.sync.mapper.SyncCableEfficiencyMapper;
import com.hzforward.sync.service.ISyncCableEfficiencyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 电缆车间效率统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Service
public class SyncCableEfficiencyServiceImpl implements ISyncCableEfficiencyService
{
    @Resource
    private SyncCableEfficiencyMapper syncCableEfficiencyMapper;

    /**
     * 查询电缆车间效率统计
     *
     * @param id 电缆车间效率统计主键
     * @return 电缆车间效率统计
     */
    @Override
    public SyncCableEfficiency selectSyncCableEfficiencyById(Long id)
    {
        return syncCableEfficiencyMapper.selectById(id);
    }

    /**
     * 查询电缆车间效率统计列表
     *
     * @param syncCableEfficiency 电缆车间效率统计
     * @return 电缆车间效率统计
     */
    @Override
    public List<SyncCableEfficiency> selectSyncCableEfficiencyList(SyncCableEfficiency syncCableEfficiency)
    {
        return syncCableEfficiencyMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<SyncCableEfficiency>()
                .eq(SyncCableEfficiency::getRecordDate, syncCableEfficiency.getRecordDate())
                .eq(SyncCableEfficiency::getQuantity, syncCableEfficiency.getQuantity())
                .eq(SyncCableEfficiency::getEffectiveWorkingHour, syncCableEfficiency.getEffectiveWorkingHour())
                .eq(SyncCableEfficiency::getPerCapitaEfficiency, syncCableEfficiency.getPerCapitaEfficiency())
                .like(SyncCableEfficiency::getLineName, syncCableEfficiency.getLineName())
        );
    }

    /**
     * 新增电缆车间效率统计
     *
     * @param syncCableEfficiency 电缆车间效率统计
     * @return 结果
     */
    @Override
    public int insertSyncCableEfficiency(SyncCableEfficiency syncCableEfficiency)
    {
        return syncCableEfficiencyMapper.insert(syncCableEfficiency);
    }

    /**
     * 修改电缆车间效率统计
     *
     * @param syncCableEfficiency 电缆车间效率统计
     * @return 结果
     */
    @Override
    public int updateSyncCableEfficiency(SyncCableEfficiency syncCableEfficiency)
    {
        return syncCableEfficiencyMapper.updateById(syncCableEfficiency);
    }

    /**
     * 删除电缆车间效率统计
     *
     * @param ids 需要删除的电缆车间效率统计主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSyncCableEfficiencyByIds(List<Long> ids)
    {
        return syncCableEfficiencyMapper.deleteBatchIds(ids);
    }

    /**
     * 导入
     * @param cableEfficiencies
     * @return
     */
    @Override
    public String importData(List<SyncCableEfficiency> cableEfficiencies) {
        for (SyncCableEfficiency syncCableEfficiency : cableEfficiencies) {
            syncCableEfficiencyMapper.insert(syncCableEfficiency);
        }
        return "";
    }

}
