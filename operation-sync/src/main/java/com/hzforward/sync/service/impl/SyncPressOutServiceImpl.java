package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.cable.HighSpeedExtruder;
import com.hzforward.oee.mapper.baseData.cable.HighSpeedExtruderMapper;
import com.hzforward.sync.service.ISyncPressOutService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncPressOutServiceImpl implements ISyncPressOutService {

    @Resource
    private HighSpeedExtruderMapper highSpeedExtruderMapper;


    /**
     * 查询分页列表
     *
     * @return
     */
    @Override
    public List<HighSpeedExtruder> queryList() {
        return highSpeedExtruderMapper.selectList(new QueryWrapper<HighSpeedExtruder>().orderByDesc("create_time"));
    }

    /**
     * 查询线速
     * @return
     */
    @Override
    public AjaxResult getLineSpeed() {
        List<HighSpeedExtruder> list = highSpeedExtruderMapper.selectList(new LambdaQueryWrapper<HighSpeedExtruder>().eq(HighSpeedExtruder::getEquipNo, "挤塑机").orderByDesc(HighSpeedExtruder::getCreateTime).last(" limit 40"));
        List<String> timeList = new ArrayList<>();
        Map<String, Short> oneMap = new LinkedHashMap<>();

        // 遍历每台设备，将数据分类存储到对应的映射中
        for (HighSpeedExtruder machine : list) {
            String createTime = DateUtils.parseDateToStr("HH:mm", machine.getCreateTime());
            Short speedReality = machine.getHmiNowSpeed();
            if (!timeList.contains(createTime)) {
                timeList.add(createTime);
            }

            switch (machine.getEquipNo()) {
                case "*********":
                    oneMap.put(createTime, speedReality);
                    break;
            }
        }

        // 基于timeList创建对应的温度列表，确保时间和温度一一对应
        List<Short> oneList = new ArrayList<>();

        for (String time : timeList) {
            oneList.add(oneMap.getOrDefault(time, null));
        }
        JSONObject resObj = new JSONObject();
        resObj.put("timeList", timeList);
        resObj.put("oneList", oneList);
        return AjaxResult.success(resObj);
    }
}
