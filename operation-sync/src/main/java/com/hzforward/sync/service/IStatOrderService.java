package com.hzforward.sync.service;

import com.hzforward.sync.domain.vo.CableOrderCountRes;
import com.hzforward.sync.domain.vo.CableProductionCompleteRes;
import com.hzforward.sync.domain.vo.PurchaseCountRes;

import java.util.List;

/**
 * 同步_排产数据Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface IStatOrderService
{

    /**
     * 获取采购统计
     * @return
     */
    PurchaseCountRes getProcureCount();

    /**
     * 获取采购订单列表
     * @return
     */
    List<String[]> getProcureList();

    /**
     * 获取销售订单未交明细
     * @return
     */
    List<String[]> getSaleOrderUnsubmittedList();

    /**
     * 获取销售订单列表
     * @return
     */
    List<String[]> getSaleOrderList();

    /**
     * 获取一周销量
     * @return
     */
    CableOrderCountRes getWeekSaleOrder();

    /**
     * 获取铜用量明细
     * @return
     */
    List<String[]> getCopperUsageList();

    /**
     * 效率明细
     * @return
     */
    List<String[]> getCableEfficiencyList();

    /**
     * 完工率
     * @return
     */
    CableProductionCompleteRes getProductionComplete();
}
