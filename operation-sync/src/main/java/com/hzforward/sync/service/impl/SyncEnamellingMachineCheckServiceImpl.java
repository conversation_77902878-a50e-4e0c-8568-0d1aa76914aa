package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.sync.domain.EnamellingMachineCheck;
import com.hzforward.sync.mapper.SyncEnamellingMachineCheckMapper;
import com.hzforward.sync.service.ISyncEnamellingMachineCheckService;
import com.hzforward.sync.util.DataUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncEnamellingMachineCheckServiceImpl implements ISyncEnamellingMachineCheckService {

    @Resource
    private SyncEnamellingMachineCheckMapper enamellingMachineCheckMapper;


    /**
     * 查询分页列表
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE3)
    public List<EnamellingMachineCheck> queryList() {
        return enamellingMachineCheckMapper.selectList(new QueryWrapper<EnamellingMachineCheck>().orderByDesc("create_time"));
    }

    /**
     * 查询温度监控
     *
     * @return
     */
    @Override
    @DataSource(value = DataSourceType.SLAVE3)
    public AjaxResult getLineSpeed() {
        return getAjaxResult("lineSpeed");
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE3)
    public AjaxResult getMaxParticlePolice() {
        return getAjaxResult("maxParticlePolice");
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE3)
    public AjaxResult getMinParticlePolice() {
        return getAjaxResult("minParticlePolice");
    }

    private AjaxResult getAjaxResult(String attribute) {
        List<EnamellingMachineCheck> list = enamellingMachineCheckMapper.selectList(new LambdaQueryWrapper<EnamellingMachineCheck>().orderByDesc(EnamellingMachineCheck::getCreateTime).last(" limit 80"));
        List<String> timeList = new ArrayList<>();
        Map<String, BigDecimal> oneMap = new LinkedHashMap<>();
        Map<String, BigDecimal> twoMap = new LinkedHashMap<>();
        Map<String, BigDecimal> threeMap = new LinkedHashMap<>();
        Map<String, BigDecimal> fourMap = new LinkedHashMap<>();
        Map<String, BigDecimal> fiveMap = new LinkedHashMap<>();
        Map<String, BigDecimal> sixMap = new LinkedHashMap<>();
        Map<String, BigDecimal> sevenMap = new LinkedHashMap<>();
        Map<String, BigDecimal> eightMap = new LinkedHashMap<>();

        // 遍历每台设备，将数据分类存储到对应的映射中
        for (EnamellingMachineCheck machine : list) {

            String createTime = DateUtils.parseDateToStr("mm:ss", machine.getCreateTime());
            BigDecimal temperature = null;
            if ("lineSpeed".equals(attribute)) {
                temperature = machine.getLineSpeed();
            } else if ("minParticlePolice".equals(attribute)) {
                temperature = new BigDecimal(machine.getMinParticlePolice());
            } else if ("maxParticlePolice".equals(attribute)) {
                temperature = new BigDecimal(machine.getMaxParticlePolice());
            }
            if (!timeList.contains(createTime)) {
                timeList.add(createTime);
            }
            switch (machine.getMachineNo()) {
                case "G13-01":
                    oneMap.put(createTime, temperature);
                    break;
                case "G13-02":
                    twoMap.put(createTime, temperature);
                    break;
                case "G13-03":
                    threeMap.put(createTime, temperature);
                    break;
                case "G13-04":
                    fourMap.put(createTime, temperature);
                    break;
                case "G14-01":
                    fiveMap.put(createTime, temperature);
                    break;
                case "G14-02":
                    sixMap.put(createTime, temperature);
                    break;
                case "G14-03":
                    sevenMap.put(createTime, temperature);
                    break;
                case "G14-04":
                    eightMap.put(createTime, temperature);
                    break;
            }
        }

        // 基于timeList创建对应的温度列表，确保时间和温度一一对应
        List<BigDecimal> oneList = new ArrayList<>();
        List<BigDecimal> twoList = new ArrayList<>();
        List<BigDecimal> threeList = new ArrayList<>();
        List<BigDecimal> fourList = new ArrayList<>();
        List<BigDecimal> fiveList = new ArrayList<>();
        List<BigDecimal> sixList = new ArrayList<>();
        List<BigDecimal> sevenList = new ArrayList<>();
        List<BigDecimal> eightList = new ArrayList<>();

//        for (String time : timeList) {
//            oneList.add(oneMap.getOrDefault(time, null)); // 若没有对应值，则添加null或其他占位符
//            twoList.add(twoMap.getOrDefault(time, null));
//            threeList.add(threeMap.getOrDefault(time, null));
//            fourList.add(fourMap.getOrDefault(time, null));
//            fiveList.add(fiveMap.getOrDefault(time, null));
//            sixList.add(sixMap.getOrDefault(time, null));
//            sevenList.add(sevenMap.getOrDefault(time, null));
//            eightList.add(eightMap.getOrDefault(time, null));
//        }

        for (String time : timeList) {
            oneList.add(DataUtils.findClosestValue(oneMap, time, 2)); // 查找前后2秒内最接近的值
            twoList.add(DataUtils.findClosestValue(twoMap, time, 2));
            threeList.add(DataUtils.findClosestValue(threeMap, time, 2));
            fourList.add(DataUtils.findClosestValue(fourMap, time, 2));
            fiveList.add(DataUtils.findClosestValue(fiveMap, time, 2));
            sixList.add(DataUtils.findClosestValue(sixMap, time, 2));
            sevenList.add(DataUtils.findClosestValue(sevenMap, time, 2));
            eightList.add(DataUtils.findClosestValue(eightMap, time, 2));
        }


        JSONObject resObj = new JSONObject();
        resObj.put("timeList", timeList);
        resObj.put("oneList", oneList);
        resObj.put("twoList", twoList);
        resObj.put("threeList", threeList);
        resObj.put("fourList", fourList);
        resObj.put("fiveList", fiveList);
        resObj.put("sixList", sixList);
        resObj.put("sevenList", sevenList);
        resObj.put("eightList", eightList);
        return AjaxResult.success(resObj);
    }


}
