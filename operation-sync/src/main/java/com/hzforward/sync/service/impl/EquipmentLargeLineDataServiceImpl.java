package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.host.CleanLargeLine;
import com.hzforward.oee.mapper.baseData.host.CleanLargeLineMapper;
import com.hzforward.sync.service.IEquipmentLargeLineDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 大件清洗PLC同步数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Service
@RequiredArgsConstructor
public class EquipmentLargeLineDataServiceImpl extends ServiceImpl<CleanLargeLineMapper, CleanLargeLine> implements IEquipmentLargeLineDataService
{
    private final CleanLargeLineMapper cleanLargeLineMapper;

    /**
     * 查询大件清洗PLC同步数据列表
     *
     * @param cleanLargeLine 大件清洗PLC同步数据
     * @return 大件清洗PLC同步数据
     */
    @Override
    public List<CleanLargeLine> selectEquipmentLargeLineDataList(CleanLargeLine cleanLargeLine)
    {
        return cleanLargeLineMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<CleanLargeLine>()

        );
    }

    /**
     * 粗洗温度
     * @return
     */
    @Override
    public JSONObject roughWashingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realityOnss = new ArrayList<>();
        List<String> realityTwos = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanLargeLine> equipmentRotorLineDataList = cleanLargeLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanLargeLine>().orderByDesc(CleanLargeLine::getCreateTime));
        for (CleanLargeLine cleanLargeLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanLargeLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanLargeLine.getVd8()));
            realityOnss.add(String.format("%.2f", cleanLargeLine.getVd0()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realityOnss);
        return jsonObject;
    }

    /**
     * 精洗温度
     * @return
     */
    @Override
    public JSONObject fineWashingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realityOnss = new ArrayList<>();
        List<String> realityTwos = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanLargeLine> equipmentRotorLineDataList = cleanLargeLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanLargeLine>().orderByDesc(CleanLargeLine::getCreateTime));
        for (CleanLargeLine cleanLargeLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanLargeLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanLargeLine.getVd20()));
            realityOnss.add(String.format("%.2f", cleanLargeLine.getVd4()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realityOnss);
        return jsonObject;
    }

    /**
     * 粗洗时间
     * @return
     */
    @Override
    public JSONObject roughWashingTime() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanLargeLine> equipmentRotorLineDataList = cleanLargeLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanLargeLine>().orderByDesc(CleanLargeLine::getCreateTime));
        for (CleanLargeLine cleanLargeLine : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanLargeLine.getCreateTime()));
            sets.add(String.valueOf(cleanLargeLine.getVw38()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        return jsonObject;
    }
}
