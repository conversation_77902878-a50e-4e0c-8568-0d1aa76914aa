package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.equipmentManage.service.ILedgerInfoService;
import com.hzforward.framework.datasource.DynamicDataSourceContextHolder;
import com.hzforward.oee.domain.baseData.cable.HighSpeedExtruder;
import com.hzforward.oee.domain.baseData.cable.SheathExtruder;
import com.hzforward.oee.mapper.baseData.cable.HighSpeedExtruderMapper;
import com.hzforward.oee.mapper.baseData.cable.SheathExtruderMapper;
import com.hzforward.sync.domain.EnamellingMachine;
import com.hzforward.sync.domain.vo.DeviceRes;
import com.hzforward.sync.mapper.SyncEnamellingMachineMapper;
import com.hzforward.sync.service.ISyncDeviceService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncDeviceServiceImpl implements ISyncDeviceService {

    @Resource
    private SheathExtruderMapper sheathExtruderMapper;
    @Resource
    private SyncEnamellingMachineMapper enamellingMachineMapper;
    @Resource
    private HighSpeedExtruderMapper highSpeedExtruderMapper;
    @Resource
    private ILedgerInfoService ledgerInfoService;



    @Override
    public AjaxResult getDeviceList() {
//        Date startTime = DateUtils.getTodayStartTime(DateUtils.addMonths(new Date(), -5));
        Date startTime = DateUtils.getTodayStartTime(new Date());
        Date endTime = DateUtils.getTodayLastTime(new Date());
        Map<String, DeviceRes> resMap = new HashMap<>();

        String[] devices = {"高速连续漆包线1号生产线", "高速连续漆包线2号生产线", "高速连续漆包线3号生产线", "高速连续漆包线4号生产线",
                "*********", "*********", "*********", "*********", "*********", "*********", "*********"};

        DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE3.name());
        try{
            // 处理数据
            List<EnamellingMachine> enamellingMachineList = enamellingMachineMapper.selectList(new LambdaQueryWrapper<EnamellingMachine>().ge(EnamellingMachine::getCreateTime, startTime)
                    .le(EnamellingMachine::getCreateTime, endTime).orderByDesc(EnamellingMachine::getCreateTime));
            Map<String, List<EnamellingMachine>> enamellingMachineMap = enamellingMachineList.stream()
                    .collect(Collectors.groupingBy(EnamellingMachine::getLineNo));
            for (String s : enamellingMachineMap.keySet()) {
                List<EnamellingMachine> list = enamellingMachineMap.get(s);
                for (EnamellingMachine extruder : list) {
                    if (extruder.getLineNo().equals("1#线")) {
                        getEnamellingMachine(extruder, resMap, "高速连续漆包线1号生产线");
                    }

                    if (extruder.getLineNo().equals("2#线")) {
                        getEnamellingMachine(extruder, resMap, "高速连续漆包线2号生产线");
                    }

                    if (extruder.getLineNo().equals("3#线")) {
                        getEnamellingMachine(extruder, resMap, "高速连续漆包线3号生产线");
                    }

                    if (extruder.getLineNo().equals("4#线")) {
                        getEnamellingMachine(extruder, resMap, "高速连续漆包线4号生产线");
                    }
                }
            }
        }finally {
            //清除
            DynamicDataSourceContextHolder.clearDataSourceType();
        }

        List<SheathExtruder> sheathExtruderList = sheathExtruderMapper.selectList(new LambdaQueryWrapper<SheathExtruder>().ge(SheathExtruder::getCreateTime, startTime).le(SheathExtruder::getCreateTime, endTime).orderByDesc(SheathExtruder::getCreateTime));
        Map<String, List<SheathExtruder>> extruderMap = sheathExtruderList.stream()
                .collect(Collectors.groupingBy(SheathExtruder::getEquipNo));

        for (String s : extruderMap.keySet()) {
            List<SheathExtruder> list = extruderMap.get(s);
            for (SheathExtruder sheathExtruder : list) {
                getExtruder(sheathExtruder, resMap);
            }
        }


        List<HighSpeedExtruder> highSpeedExtruderList = highSpeedExtruderMapper.selectList(new LambdaQueryWrapper<HighSpeedExtruder>().ge(HighSpeedExtruder::getCreateTime, startTime)
                .le(HighSpeedExtruder::getCreateTime, endTime).orderByDesc(HighSpeedExtruder::getCreateTime));
        Map<String, List<HighSpeedExtruder>> pressOutMap = highSpeedExtruderList.stream()
                .collect(Collectors.groupingBy(HighSpeedExtruder::getEquipNo));

        for (String s : pressOutMap.keySet()) {
            List<HighSpeedExtruder> list = pressOutMap.get(s);

            for (HighSpeedExtruder extruder : list) {
                getPressOut(extruder, resMap);
            }
        }

        // 补全设备
        for (String device : devices) {
            if (!resMap.containsKey(device)) {
                DeviceRes deviceRes = new DeviceRes();
                deviceRes.setName(device);
                deviceRes.setRunMinute(0);
                deviceRes.setTotalLine(new BigDecimal(0));
                deviceRes.setOverhaulTime(DateUtils.addDays(DateUtils.getNowDate(), 15));
                deviceRes.setOutputRate(new BigDecimal(0));
                deviceRes.setStatus(3);
                resMap.put(device, deviceRes);
            }
        }

        List<DeviceRes> resDetailList = new ArrayList<>();
        List<List<String>> resList = new ArrayList<>();
        Map<String, BigDecimal> resTotalMap = new HashMap<>();
        BigDecimal total = new BigDecimal(0);
        BigDecimal runNo = new BigDecimal(0);
        BigDecimal runRate = null;
        BigDecimal errNo = new BigDecimal(0);
        BigDecimal errRate = null;
        BigDecimal deviceNo = new BigDecimal(0);
        BigDecimal totalNo = new BigDecimal(0);
        for (String s : resMap.keySet()) {
            DeviceRes resObj = resMap.get(s);
            resDetailList.add(resObj);
            List<String> addList = new ArrayList<>();
            addList.add(resObj.getName());
            addList.add(DateUtils.parseDateToStr("yyyy-MM-dd", resObj.getOverhaulTime()));
            addList.add(String.valueOf(resObj.getRunMinute() / 60));
            addList.add(String.valueOf(resObj.getStatus()));
            resList.add(addList);
            if (resObj.getStatus() == 1) {
                runNo = runNo.add(new BigDecimal(1));
            }
            deviceNo = deviceNo.add(new BigDecimal(1));
            totalNo = totalNo.add(resObj.getTotalLine());
        }
        errNo = deviceNo.subtract(runNo);
        if (!deviceNo.equals(new BigDecimal(0))) {
            errRate = errNo.divide(deviceNo, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            runRate = runNo.divide(deviceNo, 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
        } else {
            errRate = new BigDecimal(0);
            runRate = new BigDecimal(0);
        }

        resTotalMap.put("total", total);
        resTotalMap.put("runNo", runNo);
        resTotalMap.put("runRate", runRate);
        resTotalMap.put("errNo", errNo);
        resTotalMap.put("errRate", errRate);
        resTotalMap.put("deviceNo", deviceNo);
        resTotalMap.put("totalNo", totalNo);
        resTotalMap.put("useRate", new BigDecimal(79));

        Map<String, Integer> deviceIndexMap = new HashMap<>();
        for (int i = 0; i < devices.length; i++) {
            deviceIndexMap.put(devices[i], i);
        }

        // 对 resDetailList 排序
        resDetailList.sort((o1, o2) -> {
            Integer index1 = deviceIndexMap.get(o1.getName());
            Integer index2 = deviceIndexMap.get(o2.getName());
            return Integer.compare(index1, index2);
        });

        // 对 resList 排序
        resList.sort((o1, o2) -> {
            String name1 = o1.get(0);
            String name2 = o2.get(0);
            Integer index1 = deviceIndexMap.get(name1);
            Integer index2 = deviceIndexMap.get(name2);
            return Integer.compare(index1, index2);
        });
        if (resDetailList.size() > 10) {
            for (int i = resDetailList.size() - 1; i >= 0; i--) {
                if ("扁电缆150挤塑7号生产线".equals(resDetailList.get(i).getName())) {
                    resDetailList.remove(i);
                }
            }
        }

        for (DeviceRes deviceRes : resDetailList) {
            deviceRes.setName(getName(deviceRes.getName()));
        }

        for (int i = 0; i < resList.size(); i++) {
            resList.get(i).set(0, getName(resList.get(i).get(0)));
        }

        Map<String, Object> map = new HashMap<>();
        map.put("resDetailList", resDetailList);
        map.put("resList", resList);
        map.put("resTotalMap", resTotalMap);
        return AjaxResult.success(map);
    }

    private String getName(String name) {
        switch (name) {
            case "高速连续漆包线1号生产线":
                return "卧式高速拉丝漆包机1#";
            case "高速连续漆包线2号生产线":
                return "卧式高速拉丝漆包机2#";
            case "高速连续漆包线3号生产线":
                return "卧式高速拉丝漆包机3#";
            case "高速连续漆包线4号生产线":
                return "卧式高速拉丝漆包机4#";
            case "*********":
                return "高速挤出机1#";
            case "*********":
                return "高速挤出机2#";
            case "*********":
                return "高速挤出机3#";
            case "*********":
                return "高速挤出机生产线1#";
            case "*********":
                return "高速挤出机生产线2#";
            case "*********":
                return "扁电缆护套挤出生产线120";
            case "*********":
                return "扁电缆护套挤出生产线150";
            default:
                return name;
        }
    }

    private void getExtruder(SheathExtruder sheathExtruder, Map<String, DeviceRes> resMap) {
        DeviceRes obj = resMap.get(sheathExtruder.getEquipNo());
        if (obj == null) {
            obj = new DeviceRes();
            if (DateUtils.differentMinuteByMillisecond(sheathExtruder.getCreateTime(), new Date()) > 30) {
                obj.setStatus(3);
            } else if (DateUtils.differentMinuteByMillisecond(new Date(), sheathExtruder.getCreateTime()) < 30 && DateUtils.differentMinuteByMillisecond(new Date(), sheathExtruder.getCreateTime()) > 10) {
                obj.setStatus(2);
            } else {
                obj.setStatus(1);
            }
            obj.setRunMinute(0);
            obj.setTotalLine(new BigDecimal(0));
            obj.setName(sheathExtruder.getEquipNo());
        }
        obj.setRunMinute(obj.getRunMinute() + 5);
        obj.setTotalLine(obj.getTotalLine().add(BigDecimal.valueOf(sheathExtruder.getLineSpeedReality()).multiply(new BigDecimal(5))));
        obj.setOverhaulTime(DateUtils.addDays(DateUtils.getNowDate(), 15));
        obj.setOutputRate(new BigDecimal(obj.getRunMinute() / 1440 * 20 + 80));
        resMap.put(sheathExtruder.getEquipNo(), obj);
    }

    private void getEnamellingMachine(EnamellingMachine extruder, Map<String, DeviceRes> resMap, String name) {
        DeviceRes obj = resMap.get(name);
        if (obj == null) {
            obj = new DeviceRes();
            if (DateUtils.differentMinuteByMillisecond(extruder.getCreateTime(), new Date()) > 30) {
                obj.setStatus(3);
            } else if (DateUtils.differentMinuteByMillisecond(new Date(), extruder.getCreateTime()) < 30 && DateUtils.differentMinuteByMillisecond(new Date(), extruder.getCreateTime()) > 10) {
                obj.setStatus(2);
            } else {
                obj.setStatus(1);
            }
            obj.setRunMinute(0);
            obj.setTotalLine(new BigDecimal(0));
            obj.setName(name);
        }
        obj.setRunMinute(obj.getRunMinute() + 5);
        obj.setTotalLine(obj.getTotalLine().add(extruder.getRealityLineSpeed().multiply(new BigDecimal(5))));
        obj.setOverhaulTime(DateUtils.addDays(DateUtils.getNowDate(), 15));
        obj.setOutputRate(new BigDecimal(obj.getRunMinute() / 1440 * 20 + 80));
        resMap.put(name, obj);
    }

    private void getPressOut(HighSpeedExtruder extruder, Map<String, DeviceRes> resMap) {
        DeviceRes obj = resMap.get(extruder.getEquipNo());
        if (obj == null) {
            obj = new DeviceRes();
            if (DateUtils.differentMinuteByMillisecond(extruder.getCreateTime(), new Date()) > 30) {
                obj.setStatus(3);
            } else if (DateUtils.differentMinuteByMillisecond(new Date(), extruder.getCreateTime()) < 30 && DateUtils.differentMinuteByMillisecond(new Date(), extruder.getCreateTime()) > 10) {
                obj.setStatus(2);
            } else {
                obj.setStatus(1);
            }
            obj.setRunMinute(0);
            obj.setTotalLine(new BigDecimal(0));

            obj.setName(extruder.getEquipNo());
        }
        obj.setRunMinute(obj.getRunMinute() + 5);
        obj.setTotalLine(obj.getTotalLine().add(BigDecimal.valueOf(extruder.getHmiNowSpeed() * 5)));
        obj.setOverhaulTime(DateUtils.addDays(DateUtils.getNowDate(), 15));
        obj.setOutputRate(new BigDecimal(obj.getRunMinute() / 1440 * 20 + 80));
        resMap.put(extruder.getEquipNo(), obj);
    }
}
