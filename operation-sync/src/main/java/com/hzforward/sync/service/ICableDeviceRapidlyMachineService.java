package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.baseData.cable.HighSpeedTwisting;

import java.util.List;

/**
 * 电缆-成缆机高速绞Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-14
 */
public interface ICableDeviceRapidlyMachineService extends IService<HighSpeedTwisting>
{
    /**
     * 查询电缆-成缆机高速绞列表
     * 
     * @param highSpeedTwisting 电缆-成缆机高速绞
     * @return 电缆-成缆机高速绞集合
     */
    List<HighSpeedTwisting> selectCableDeviceRapidlyMachineList(HighSpeedTwisting highSpeedTwisting);

    /**
     * 当前线速
     * @return
     */
    JSONObject currentLineSpeed();

    /**
     * 当前张力
     * @return
     */
    JSONObject currentTension();

    /**
     * 详情列表
     * @return
     */
    JSONArray detailList();
}
