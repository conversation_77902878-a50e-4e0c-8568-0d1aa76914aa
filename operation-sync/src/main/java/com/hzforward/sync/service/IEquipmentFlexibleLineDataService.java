package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.baseData.host.CleanFlexibleLine;

import java.util.List;

/**
 * 柔性线PLC同步数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-09
 */
public interface IEquipmentFlexibleLineDataService extends IService<CleanFlexibleLine>
{
    /**
     * 查询柔性线PLC同步数据列表
     * 
     * @param cleanFlexibleLine 柔性线PLC同步数据
     * @return 柔性线PLC同步数据集合
     */
    List<CleanFlexibleLine> selectEquipmentFlexibleLineDataList(CleanFlexibleLine cleanFlexibleLine);

    /**
     * 查询粗洗温度
     * @return
     */
    JSONObject roughWashingTemperature();

    /**
     * 查询精洗温度
     * @return
     */
    JSONObject fineWashingTemperature();

    /**
     * 防锈温度
     * @return
     */
    JSONObject antirustTemperature();

    /**
     * 查询粗细Ph
     * @return
     */
    JSONObject roughWashingPh();

    /**
     * 精洗Ph
     * @return
     */
    JSONObject fineWashingPh();

    /**
     * 防锈PH
     * @return
     */
    JSONObject antirustPh();

    /**
     * 油漆烘干温度
     * @return
     */
    JSONObject paintDryingTemperature();

    /**
     * 脱水烘干温度
     * @return
     */
    JSONObject dehydrationDryingTemperature();

    /**
     * 辊道频率
     * @return
     */
    JSONObject rollerFrequency();
}
