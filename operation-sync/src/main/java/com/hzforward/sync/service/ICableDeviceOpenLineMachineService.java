package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.oee.domain.baseData.cable.OpenLine;

import java.util.List;

/**
 * 电缆-开线机Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-16
 */
public interface ICableDeviceOpenLineMachineService extends IService<OpenLine>
{
    /**
     * 查询电缆-开线机列表
     * 
     * @param openLine 电缆-开线机
     * @return 电缆-开线机集合
     */
    List<OpenLine> selectCableDeviceOpenLineMachineList(OpenLine openLine);

    /**
     * 堵塞次数
     * @return
     */
    JSONObject blockageTimes();

    /**
     * 当前编码器记米数
     * @return
     */
    JSONObject currentEncoderCount();

    /**
     * 详情列表
     * @return
     */
    JSONArray detailList();
}
