package com.hzforward.sync.service;

import cn.hutool.json.JSONObject;
import com.hzforward.sync.domain.vo.InspectionStatRes;
import com.hzforward.sync.domain.vo.PurchaseCountRes;
import com.hzforward.sync.domain.vo.TypeRatioRes;

import java.util.List;

/**
 * 同步_排产数据Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ICableStatQualityService
{
    /**
     * 获取报检列表
     * @return
     */
    List<String[]> getInspectionReportList(Integer code);

    /**
     * 获取检验列表
     * @return
     */
    List<String[]> getInspectionList(Integer code);

    /**
     * 获取检验统计
     * @return
     */
    InspectionStatRes getInspectionStat(Integer code);

    /**
     * 获取类型比
     * @return
     */
    List<TypeRatioRes> getTypeRatio(Integer code);

    /**
     * 月度合格率
     * @return
     */
    JSONObject getMonthQualifiedList(Integer code);

    /**
     * 收料合格
     * @return
     */
    JSONObject getInQualified(Integer code);
}
