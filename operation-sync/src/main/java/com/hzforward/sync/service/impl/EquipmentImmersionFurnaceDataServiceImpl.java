package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.sync.domain.EquipmentImmersionFurnaceData;
import com.hzforward.sync.mapper.EquipmentImmersionFurnaceDataMapper;
import com.hzforward.sync.service.IEquipmentImmersionFurnaceDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 通过式浸漆炉Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-11
 */
@Service
@RequiredArgsConstructor
public class EquipmentImmersionFurnaceDataServiceImpl extends ServiceImpl<EquipmentImmersionFurnaceDataMapper, EquipmentImmersionFurnaceData> implements IEquipmentImmersionFurnaceDataService
{
    private final EquipmentImmersionFurnaceDataMapper equipmentImmersionFurnaceDataMapper;

    /**
     * 查询通过式浸漆炉列表
     *
     * @param equipmentImmersionFurnaceData 通过式浸漆炉
     * @return 通过式浸漆炉
     */
    @Override
    public List<EquipmentImmersionFurnaceData> selectEquipmentImmersionFurnaceDataList(EquipmentImmersionFurnaceData equipmentImmersionFurnaceData)
    {
        return equipmentImmersionFurnaceDataMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<EquipmentImmersionFurnaceData>()
                .eq(EquipmentImmersionFurnaceData::getD31, equipmentImmersionFurnaceData.getD31())
                .eq(EquipmentImmersionFurnaceData::getD32, equipmentImmersionFurnaceData.getD32())
                .eq(EquipmentImmersionFurnaceData::getD33, equipmentImmersionFurnaceData.getD33())
                .eq(EquipmentImmersionFurnaceData::getD34, equipmentImmersionFurnaceData.getD34())
                .eq(EquipmentImmersionFurnaceData::getD35, equipmentImmersionFurnaceData.getD35())
                .eq(EquipmentImmersionFurnaceData::getD36, equipmentImmersionFurnaceData.getD36())
                .eq(EquipmentImmersionFurnaceData::getD37, equipmentImmersionFurnaceData.getD37())
                .eq(EquipmentImmersionFurnaceData::getD38, equipmentImmersionFurnaceData.getD38())
                .eq(EquipmentImmersionFurnaceData::getD402, equipmentImmersionFurnaceData.getD402())
                .eq(EquipmentImmersionFurnaceData::getD498, equipmentImmersionFurnaceData.getD498())
                .eq(EquipmentImmersionFurnaceData::getD450, equipmentImmersionFurnaceData.getD450())
                .eq(EquipmentImmersionFurnaceData::getD451, equipmentImmersionFurnaceData.getD451())
                .eq(EquipmentImmersionFurnaceData::getD452, equipmentImmersionFurnaceData.getD452())
                .eq(EquipmentImmersionFurnaceData::getD455, equipmentImmersionFurnaceData.getD455())
                .eq(EquipmentImmersionFurnaceData::getD458, equipmentImmersionFurnaceData.getD458())
                .eq(EquipmentImmersionFurnaceData::getD406, equipmentImmersionFurnaceData.getD406())
        );
    }

    /**
     * 通过式浸漆炉-凝胶
     * @return
     */
    @Override
    public JSONObject getGelList() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<EquipmentImmersionFurnaceData> equipmentRotorLineDataList = equipmentImmersionFurnaceDataMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<EquipmentImmersionFurnaceData>().orderByDesc(EquipmentImmersionFurnaceData::getCreateTime));
        for (EquipmentImmersionFurnaceData equipmentImmersionFurnaceData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentImmersionFurnaceData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentImmersionFurnaceData.getD328()));
            realitys.add(String.format("%.2f",equipmentImmersionFurnaceData.getD32()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 通过式浸漆炉-固化
     * @return
     */
    @Override
    public JSONObject getSolidifyList() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<EquipmentImmersionFurnaceData> equipmentRotorLineDataList = equipmentImmersionFurnaceDataMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<EquipmentImmersionFurnaceData>().orderByDesc(EquipmentImmersionFurnaceData::getCreateTime));
        for (EquipmentImmersionFurnaceData equipmentImmersionFurnaceData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentImmersionFurnaceData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentImmersionFurnaceData.getD348()));
            realitys.add(String.format("%.2f",equipmentImmersionFurnaceData.getD34()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 通过式浸漆炉-漆液温度
     * @return
     */
    @Override
    public JSONObject getPaintLiquidList() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<EquipmentImmersionFurnaceData> equipmentRotorLineDataList = equipmentImmersionFurnaceDataMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<EquipmentImmersionFurnaceData>().orderByDesc(EquipmentImmersionFurnaceData::getCreateTime));
        for (EquipmentImmersionFurnaceData equipmentImmersionFurnaceData : equipmentRotorLineDataList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, equipmentImmersionFurnaceData.getCreateTime()));
            sets.add(String.format("%.2f",equipmentImmersionFurnaceData.getD378()));
            realitys.add(String.format("%.2f",equipmentImmersionFurnaceData.getD37()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }
}
