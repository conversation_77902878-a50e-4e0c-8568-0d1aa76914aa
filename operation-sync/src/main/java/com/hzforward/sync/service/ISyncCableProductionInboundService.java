package com.hzforward.sync.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.sync.domain.SyncCableProductionInbound;

import java.util.List;

/**
 * 生产入库Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ISyncCableProductionInboundService extends IService<SyncCableProductionInbound>
{
    /**
     * 查询生产入库列表
     * 
     * @param syncCableProductionInbound 生产入库
     * @return 生产入库集合
     */
    List<SyncCableProductionInbound> selectSyncCableProductionInboundList(SyncCableProductionInbound syncCableProductionInbound);

    /**
     * 导入
     * @param productionInbounds
     * @return
     */
    String importData(List<SyncCableProductionInbound> productionInbounds);
}
