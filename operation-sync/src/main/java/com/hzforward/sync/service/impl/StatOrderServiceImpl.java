package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.framework.datasource.DynamicDataSourceContextHolder;
import com.hzforward.sync.domain.*;
import com.hzforward.sync.domain.vo.CableOrderCountRes;
import com.hzforward.sync.domain.vo.CableProductionCompleteRes;
import com.hzforward.sync.domain.vo.PurchaseCountRes;
import com.hzforward.sync.mapper.*;
import com.hzforward.sync.service.IStatOrderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class StatOrderServiceImpl implements IStatOrderService {

    @Resource
    private PurchaseInspectionMapper purchaseInspectionMapper;
    @Resource
    private PurchaseOrderViewMapper purchaseOrderViewMapper;
    @Resource
    private SyncCableSaleOrderUndeliveredMapper cableSaleOrderUndeliveredMapper;
    @Resource
    private SyncCableProductionCompleteMapper cableProductionCompleteMapper;
    @Resource
    private OrderInfoMapper orderInfoMapper;
    @Resource
    private SyncCableCopperWireUsageMapper cableCopperWireUsageMapper;
    @Resource
    private SyncCableEfficiencyMapper cableEfficiencyMapper;


    /**
     * 查询分页列表
     *
     * @return
     */
    @Override
    public PurchaseCountRes getProcureCount() {
        try {
            PurchaseCountRes res = new PurchaseCountRes();
            // 切换到 SLAVE4 数据源
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE4.name());
            List<PurchaseOrderView> purchaseOrderViewList = purchaseOrderViewMapper.selectList(new LambdaQueryWrapper<PurchaseOrderView>()
                    .ge(PurchaseOrderView::getCTime, DateUtils.addDays(new Date(), -30)).eq(PurchaseOrderView::getBUKRS, "2002")
                    .orderByDesc(PurchaseOrderView::getCTime));
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE5.name());
            List<String> supplierCodes = purchaseOrderViewList.stream().map(PurchaseOrderView::getLIFNR).distinct().collect(Collectors.toList());
            List<PurchaseInspection> purchaseInspectionList = purchaseInspectionMapper.selectList(new LambdaQueryWrapper<PurchaseInspection>()
                    .ge(PurchaseInspection::getCTime, DateUtils.addDays(new Date(), -30)).in(PurchaseInspection::getSupplierCode, supplierCodes));

            // purchaseOrderViewList 筛选计划时间小于当前时间
            purchaseOrderViewList = purchaseOrderViewList.stream().filter(purchaseOrderView -> purchaseOrderView.getEINDT() != null && purchaseOrderView.getEINDT().getTime() < new Date().getTime()).collect(Collectors.toList());
            Map<String, PurchaseOrderView> purchaseOrderViewMap = purchaseOrderViewList.stream().collect(Collectors.toMap(purchaseOrderView -> purchaseOrderView.getEBELN() + purchaseOrderView.getEBELP(), purchaseOrderView -> purchaseOrderView,
                    (existing, replacement) -> {
                        // 处理重复键的情况，这里以 existing 为主
                        // 你可以根据需求选择保留 existing、replacement 或者合并
                        return existing; // 或者 return replacement;
                    }));
            // value 为list key 会重复
            Map<String, List<PurchaseInspection>> purchaseInspectionMap = purchaseInspectionList.stream()
                    .collect(Collectors.groupingBy(
                            purchaseInspection -> purchaseInspection.getBaseNum() + purchaseInspection.getBaseLine()
                    ));
            BigDecimal timelyDeliveryNo = new BigDecimal(0);
            BigDecimal qualifiedNo = new BigDecimal(0);
            BigDecimal deliveryNo = new BigDecimal(0);
            for (String key : purchaseOrderViewMap.keySet()) {
                PurchaseOrderView purchaseOrderView = purchaseOrderViewMap.get(key);
                List<PurchaseInspection> purchaseInspections = purchaseInspectionMap.get(key);
                if (purchaseInspections != null) {
                    BigDecimal quantity = new BigDecimal(0);
                    BigDecimal qualified = new BigDecimal(0);
                    BigDecimal delivery = new BigDecimal(0);
                    for (PurchaseInspection purchaseInspection : purchaseInspections) {
                        if (DateUtils.addDays(purchaseOrderView.getEINDT(),10).getTime() > purchaseInspection.getCTime().getTime()) {
                            quantity = quantity.add(purchaseInspection.getQualifiedQty());
                            quantity = quantity.add(purchaseInspection.getUnqualifiedQty());
                        }
                        qualified = qualified.add(purchaseInspection.getQualifiedQty());
                        delivery = delivery.add(purchaseInspection.getQualifiedQty());
                        delivery = delivery.add(purchaseInspection.getUnqualifiedQty());
                    }
                    if (quantity.compareTo(new BigDecimal(purchaseOrderView.getMENGE())) >= 0) {
                        timelyDeliveryNo = timelyDeliveryNo.add(new BigDecimal(1));
                    }
                    if (qualified.compareTo(new BigDecimal(purchaseOrderView.getMENGE())) >= 0) {
                        qualifiedNo = qualifiedNo.add(new BigDecimal(1));
                    }
                    if (delivery.compareTo(new BigDecimal(purchaseOrderView.getMENGE())) >= 0) {
                        deliveryNo = deliveryNo.add(new BigDecimal(1));
                    }
                }
            }
            // 计算及时率
            BigDecimal timelyRate = new BigDecimal(0);
            if (!purchaseOrderViewList.isEmpty()) {
                timelyRate = timelyDeliveryNo.divide(new BigDecimal(purchaseOrderViewList.size()), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            }
            // 计算合格率
            BigDecimal qualifiedRate = new BigDecimal(0);
            if (!purchaseOrderViewList.isEmpty()) {
                qualifiedRate = qualifiedNo.divide(deliveryNo, 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));
            }
            res.setTimelyRate(timelyRate.toString());
            res.setQualifiedRate(qualifiedRate.toString());
            res.setOrderQuantity(String.valueOf(purchaseOrderViewList.size()));
            res.setDeliveryQuantity(deliveryNo.toString());
            res.setQualifiedQuantity(qualifiedNo.toString());
            return res;
        } finally {
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    @Override
    @DataSource(DataSourceType.SLAVE4)
    public List<String[]> getProcureList() {
        Page<PurchaseOrderView> page = new Page<>(1, 20);
        Page<PurchaseOrderView> pageRes = purchaseOrderViewMapper.selectPage(page, new LambdaQueryWrapper<PurchaseOrderView>()
                .ge(PurchaseOrderView::getCTime, DateUtils.addDays(new Date(), -7))
                .eq(PurchaseOrderView::getBUKRS, "2002")
                .orderByDesc(PurchaseOrderView::getCTime));
        List<PurchaseOrderView> list = pageRes.getRecords();
        return getProcureStrings(list);
    }

    /**
     * 获取销售订单未交明细
     *
     * @return
     */
    @Override
    public List<String[]> getSaleOrderUnsubmittedList() {
        List<SyncCableSaleOrderUndelivered> list = cableSaleOrderUndeliveredMapper.selectList(new LambdaQueryWrapper<SyncCableSaleOrderUndelivered>());
        return getSaleOrderUnsubmittedStrings(list);
    }

    /**
     * 获取销售的订单列表
     *
     * @return
     */
    @Override
    @DataSource(DataSourceType.SLAVE6)
    public List<String[]> getSaleOrderList() {
        List<OrderInfo> list = orderInfoMapper.listOrderInfo();
        return getSaleOrderStrings(list);
    }

    /**
     * 获取一周销量
     *
     * @return
     */
    @Override
    @DataSource(DataSourceType.SLAVE6)
    public CableOrderCountRes getWeekSaleOrder() {
        Date startTTime = DateUtils.getTodayStartTime(DateUtils.addDays(DateUtils.getNowDate(), -7));
        Date endTTime = DateUtils.getTodayLastTime(DateUtils.getNowDate());
        List<OrderInfo> cableOrderList = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfo>()
                .ge(OrderInfo::getCTime, startTTime).le(OrderInfo::getCTime, endTTime).orderByDesc(OrderInfo::getCTime));
        Map<String, Integer> map = new TreeMap<>();
        for (OrderInfo orderInfo : cableOrderList) {
            Date cTime = orderInfo.getCTime();
            String key = DateUtils.parseDateToStr(DateUtils.MM_DD, cTime);
            if (!map.containsKey(key)) {
                map.put(key, 1);
            } else {
                map.put(key, map.get(key) + 1);
            }
        }
        CableOrderCountRes orderCountRes = new CableOrderCountRes();
        List<String> dates = new ArrayList<>();
        List<Integer> sales = new ArrayList<>();
        for (String s : map.keySet()) {
            dates.add(s);
            sales.add(map.get(s));
        }
        orderCountRes.setDates(dates);
        orderCountRes.setSaleNos(sales);
        return orderCountRes;
    }

    /**
     * 铜用量明细
     *
     * @return
     */
    @Override
    public List<String[]> getCopperUsageList() {
        List<SyncCableCopperWireUsage> cableCopperWireUsageList = cableCopperWireUsageMapper.selectList(new LambdaQueryWrapper<>());
        return getCopperUsageStrings(cableCopperWireUsageList);
    }

    /**
     * 效率明细
     *
     * @return
     */
    @Override
    public List<String[]> getCableEfficiencyList() {
        Page<SyncCableEfficiency> cableEfficiencyPage = cableEfficiencyMapper.selectPage(Page.of(0, 10), new LambdaQueryWrapper<SyncCableEfficiency>().orderByDesc(SyncCableEfficiency::getCreateTime));
        return getCableEfficiencyStrings(cableEfficiencyPage.getRecords());
    }

    /**
     * 完工率
     *
     * @return
     */
    @Override
    public CableProductionCompleteRes getProductionComplete() {
        Page<SyncCableProductionComplete> cableProductionComplete = cableProductionCompleteMapper.selectPage(Page.of(0, 7), new LambdaQueryWrapper<SyncCableProductionComplete>().orderByDesc(SyncCableProductionComplete::getRecordDate));
        List<SyncCableProductionComplete> productionComplete = cableProductionComplete.getRecords();
        List<String> dates = new ArrayList<>();
        for (SyncCableProductionComplete complete : productionComplete) {
            String dateStr = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, complete.getRecordDate());
            dates.add(dateStr);
        }
        List<BigDecimal> completeRateList = productionComplete.stream().map(SyncCableProductionComplete::getCompleteRate).collect(Collectors.toList());
        CableProductionCompleteRes cableProductionCompleteRes = new CableProductionCompleteRes();
        cableProductionCompleteRes.setCompleteRates(completeRateList);
        cableProductionCompleteRes.setDates(dates);
        return cableProductionCompleteRes;
    }

    private static List<String[]> getProcureStrings(List<PurchaseOrderView> list) {
        List<String[]> resList = new ArrayList<>();
        //['采购单号', '采购行号', '供应商名称', '物料编码', '采购数量', '报检单数量', '入库数量', '单位', '仓库代码', '状态']
        for (PurchaseOrderView purchaseOrderView : list) {
            String[] res = {purchaseOrderView.getEBELN(), purchaseOrderView.getEBELP(), purchaseOrderView.getNAME1(), purchaseOrderView.getMATNR(), purchaseOrderView.getMENGE(), purchaseOrderView.getInspectionQty() == null ? "" : purchaseOrderView.getInspectionQty().toString(),
                    purchaseOrderView.getStorageQty() == null ? "" : purchaseOrderView.getStorageQty().toString(), purchaseOrderView.getUnit(), purchaseOrderView.getLGORT(), purchaseOrderView.getStatus()};
            resList.add(res);
        }
        return resList;
    }

    private static List<String[]> getSaleOrderUnsubmittedStrings(List<SyncCableSaleOrderUndelivered> list) {
        List<String[]> resList = new ArrayList<>();
        //['物料编码', '编号', '单位', '订单数量', '当前库存数量', '差异数量']
        for (SyncCableSaleOrderUndelivered saleOrderUndelivered : list) {
            String[] res = {saleOrderUndelivered.getMaterialCode(), saleOrderUndelivered.getRef(), saleOrderUndelivered.getSpecifications(),
                    saleOrderUndelivered.getUnit(), saleOrderUndelivered.getOrderNo().toString(), saleOrderUndelivered.getStoreNo().toString(), saleOrderUndelivered.getDifferenceNo().toString()};
            resList.add(res);
        }
        return resList;
    }

    private static List<String[]> getSaleOrderStrings(List<OrderInfo> list) {
        List<String[]> resList = new ArrayList<>();
        //['订单号', '订单行', '客户订单号', '合同号', '创建时间', '交货日期']
        for (OrderInfo orderInfo : list) {
            String[] res = {orderInfo.getCustomerOrderNum(), orderInfo.getContractNum(), orderInfo.getProductModel(),
                    DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, orderInfo.getDeliveryDate()), orderInfo.getCustomerName(), DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, orderInfo.getCTime())};
            resList.add(res);
        }
        return resList;
    }

    private static List<String[]> getCopperUsageStrings(List<SyncCableCopperWireUsage> list) {
        List<String[]> resList = new ArrayList<>();
        //['采购单号', '采购行号', '物料编码', '物料描述', '数量', '单位', '凭证日期']
        for (SyncCableCopperWireUsage cableCopperWireUsage : list) {
            String[] res = {cableCopperWireUsage.getPurchaseOrderNo(), cableCopperWireUsage.getPurchaseOrderLine(), cableCopperWireUsage.getMaterialCode(),
                    cableCopperWireUsage.getMaterialDesc(), String.valueOf(cableCopperWireUsage.getQuantity()), cableCopperWireUsage.getUnit()};
            resList.add(res);
        }
        return resList;
    }

    private static List<String[]> getCableEfficiencyStrings(List<SyncCableEfficiency> list) {
        List<String[]> resList = new ArrayList<>();
        //['记录日期', '台量', '有效工时', '人均效率', '铜用量', '废铜量', '铜成材率']
        for (SyncCableEfficiency cableEfficiency : list) {
            String[] res = {DateUtils.parseDateToStr(DateUtils.MM, cableEfficiency.getRecordDate()) + "月", String.valueOf(cableEfficiency.getQuantity()), String.valueOf(cableEfficiency.getEffectiveWorkingHour()),
                    String.valueOf(cableEfficiency.getPerCapitaEfficiency()), String.valueOf(cableEfficiency.getCopperUsage()), String.valueOf(cableEfficiency.getWasteCopperQuantity()), String.valueOf(cableEfficiency.getCopperYieldRate())};
            resList.add(res);
        }
        return resList;
    }


}
