package com.hzforward.sync.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.sync.domain.SyncCableCopperWireUsage;

import java.util.List;

/**
 * 铜丝用量Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ISyncCableCopperWireUsageService extends IService<SyncCableCopperWireUsage>
{
    /**
     * 查询铜丝用量列表
     * 
     * @param syncCableCopperWireUsage 铜丝用量
     * @return 铜丝用量集合
     */
    List<SyncCableCopperWireUsage> selectSyncCableCopperWireUsageList(SyncCableCopperWireUsage syncCableCopperWireUsage);

    /**
     * 导入
     * @param cableCopperWireUsages
     * @return
     */
    String importData(List<SyncCableCopperWireUsage> cableCopperWireUsages);
}
