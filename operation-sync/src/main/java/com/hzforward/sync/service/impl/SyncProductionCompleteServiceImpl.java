package com.hzforward.sync.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import javax.annotation.Resource;

import com.hzforward.sync.domain.SyncProductionCompleteDetail;
import com.hzforward.sync.domain.vo.SyncProductionCompleteListReq;
import com.hzforward.sync.mapper.SyncProductionCompleteDetailMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.hzforward.sync.mapper.SyncProductionCompleteMapper;
import com.hzforward.sync.domain.SyncProductionComplete;
import com.hzforward.sync.service.ISyncProductionCompleteService;

/**
 * 排产完成记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Service
public class SyncProductionCompleteServiceImpl implements ISyncProductionCompleteService
{
    @Resource
    private SyncProductionCompleteMapper syncProductionCompleteMapper;
    @Resource
    private SyncProductionCompleteDetailMapper syncProductionCompleteDetailMapper;

    /**
     * 查询排产完成记录
     *
     * @param id 排产完成记录主键
     * @return 排产完成记录
     */
    @Override
    public SyncProductionComplete selectSyncProductionCompleteById(Long id)
    {
        return syncProductionCompleteMapper.selectById(id);
    }

    /**
     * 查询排产完成记录列表
     *
     * @param syncProductionComplete 排产完成记录
     * @return 排产完成记录
     */
    @Override
    public List<SyncProductionComplete> selectSyncProductionCompleteList(SyncProductionCompleteListReq syncProductionComplete)
    {
        LambdaQueryWrapper<SyncProductionComplete> lambdaQueryWrapper = new ExcludeEmptyLambdaQueryWrapper<SyncProductionComplete>()
                .eq(SyncProductionComplete::getLine, syncProductionComplete.getLine());
        if (syncProductionComplete.getRecordDate() != null && syncProductionComplete.getRecordDate().length > 1) {
            lambdaQueryWrapper.between(SyncProductionComplete::getRecordDate, syncProductionComplete.getRecordDate()[0], syncProductionComplete.getRecordDate()[1]);
        }
        List<SyncProductionComplete> list = syncProductionCompleteMapper.selectList(lambdaQueryWrapper.orderByDesc(SyncProductionComplete::getRecordDate));
        List<Long> ids = list.stream().map(SyncProductionComplete::getId).collect(Collectors.toList());
        List<SyncProductionCompleteDetail> details = syncProductionCompleteDetailMapper
                .selectList(new ExcludeEmptyLambdaQueryWrapper<SyncProductionCompleteDetail>().in(SyncProductionCompleteDetail::getPid, ids));
        // 通过pid 分组
        details.forEach(detail -> {
            list.forEach(item -> {
                if (item.getId().equals(detail.getPid())) {
                    if (item.getDetails() == null) {
                        item.setDetails(new ArrayList<>());
                    }
                    item.getDetails().add(detail);
                }
            });
        });
        return list;
    }

    /**
     * 新增排产完成记录
     *
     * @param syncProductionComplete 排产完成记录
     * @return 结果
     */
    @Override
    public int insertSyncProductionComplete(SyncProductionComplete syncProductionComplete)
    {
        return syncProductionCompleteMapper.insert(syncProductionComplete);
    }

    /**
     * 修改排产完成记录
     *
     * @param syncProductionComplete 排产完成记录
     * @return 结果
     */
    @Override
    public int updateSyncProductionComplete(SyncProductionComplete syncProductionComplete)
    {
        return syncProductionCompleteMapper.updateById(syncProductionComplete);
    }

    /**
     * 删除排产完成记录
     *
     * @param ids 需要删除的排产完成记录主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSyncProductionCompleteByIds(List<Long> ids)
    {
        return syncProductionCompleteMapper.deleteBatchIds(ids);
    }

}
