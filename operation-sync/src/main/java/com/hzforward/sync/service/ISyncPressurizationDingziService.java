package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.sync.domain.SyncPressurizationDingzi;

/**
 * 耐压定子Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ISyncPressurizationDingziService 
{
    /**
     * 查询耐压定子
     * 
     * @param abh 耐压定子主键
     * @return 耐压定子
     */
    public SyncPressurizationDingzi selectSyncPressurizationDingziByAbh(Long abh);

    /**
     * 查询耐压定子列表
     * 
     * @param syncPressurizationDingzi 耐压定子
     * @return 耐压定子集合
     */
    public List<SyncPressurizationDingzi> selectSyncPressurizationDingziList(SyncPressurizationDingzi syncPressurizationDingzi);

    /**
     * 新增耐压定子
     * 
     * @param syncPressurizationDingzi 耐压定子
     * @return 结果
     */
    public int insertSyncPressurizationDingzi(SyncPressurizationDingzi syncPressurizationDingzi);

    /**
     * 修改耐压定子
     * 
     * @param syncPressurizationDingzi 耐压定子
     * @return 结果
     */
    public int updateSyncPressurizationDingzi(SyncPressurizationDingzi syncPressurizationDingzi);

    /**
     * 批量删除耐压定子
     * 
     * @param abhs 需要删除的耐压定子主键集合
     * @return 结果
     */
    public int deleteSyncPressurizationDingziByAbhs(List<Long> abhs);

    /**
     * 删除耐压定子信息
     * 
     * @param abh 耐压定子主键
     * @return 结果
     */
    public int deleteSyncPressurizationDingziByAbh(Long abh);
}
