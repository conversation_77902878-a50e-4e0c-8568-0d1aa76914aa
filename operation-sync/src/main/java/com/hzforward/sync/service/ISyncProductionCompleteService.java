package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.sync.domain.SyncProductionComplete;
import com.hzforward.sync.domain.vo.SyncProductionCompleteListReq;

/**
 * 排产完成记录Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-31
 */
public interface ISyncProductionCompleteService
{
    /**
     * 查询排产完成记录
     * 
     * @param id 排产完成记录主键
     * @return 排产完成记录
     */
    SyncProductionComplete selectSyncProductionCompleteById(Long id);

    /**
     * 查询排产完成记录列表
     * 
     * @param syncProductionComplete 排产完成记录
     * @return 排产完成记录集合
     */
    List<SyncProductionComplete> selectSyncProductionCompleteList(SyncProductionCompleteListReq syncProductionComplete);

    /**
     * 新增排产完成记录
     * 
     * @param syncProductionComplete 排产完成记录
     * @return 结果
     */
    int insertSyncProductionComplete(SyncProductionComplete syncProductionComplete);

    /**
     * 修改排产完成记录
     * 
     * @param syncProductionComplete 排产完成记录
     * @return 结果
     */
    int updateSyncProductionComplete(SyncProductionComplete syncProductionComplete);

    /**
     * 删除排产完成记录
     * 
     * @param ids 需要删除的排产完成记录主键集合
     * @return 结果
     */
    int deleteSyncProductionCompleteByIds(List<Long> ids);

}
