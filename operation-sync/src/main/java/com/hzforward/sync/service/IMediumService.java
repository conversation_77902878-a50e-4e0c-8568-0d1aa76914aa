package com.hzforward.sync.service;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

/**
 * 机座线PLC同步数据Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-30
 */
public interface IMediumService
{

    /**
     * 获取年销量列表
     * @return
     */
    JSONObject getYearSaleList();


    /**
     * 获取月销量数据
     * @return
     */
    JSONObject getMonthSaleList();

    /**
     * 获取区域销量统计
     * @return
     */
    JSONObject getSectionSaleList();

    /**
     * 获取订单销量多维度统计
     * @return
     */
    JSONObject getMultiDimensionalSaleList();

    /**
     * 获取装配线FPY
     * @return
     */
    JSONObject getFpy();

    /**
     * 获取FTB关闭率
     * @return
     */
    JSONArray getFtbCloseRate();

    /**
     * 获取月度安全天数列表
     * @return
     */
    JSONObject getMonthSecureDayList();

    /**
     * 获取质量柱状图
     * @return
     */
    JSONObject getQualityList();

    /**
     * 获取机加工排产完成率
     * @return
     */
    JSONObject getMachiningCompleteRate();

    /**
     * 获取设备状态
     * @return
     */
    JSONObject getDeviceStatus();

    /**
     * 获取隐患排查
     * @return
     */
    JSONArray getHazardInvestigation();
}
