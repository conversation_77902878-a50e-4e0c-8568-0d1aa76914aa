package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.sync.domain.SyncPressurizationDingzi;
import com.hzforward.sync.mapper.SyncPressurizationDingziMapper;
import com.hzforward.sync.service.ISyncPressurizationDingziService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 耐压定子Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncPressurizationDingziServiceImpl implements ISyncPressurizationDingziService
{
    @Resource
    private SyncPressurizationDingziMapper syncPressurizationDingziMapper;

    /**
     * 查询耐压定子
     *
     * @param abh 耐压定子主键
     * @return 耐压定子
     */
    @Override
    public SyncPressurizationDingzi selectSyncPressurizationDingziByAbh(Long abh)
    {
        return syncPressurizationDingziMapper.selectById(abh);
    }

    /**
     * 查询耐压定子列表
     *
     * @param syncPressurizationDingzi 耐压定子
     * @return 耐压定子
     */
    @Override
    public List<SyncPressurizationDingzi> selectSyncPressurizationDingziList(SyncPressurizationDingzi syncPressurizationDingzi)
    {
        return syncPressurizationDingziMapper.selectList(new LambdaQueryWrapper<>());
    }

    /**
     * 新增耐压定子
     *
     * @param syncPressurizationDingzi 耐压定子
     * @return 结果
     */
    @Override
    public int insertSyncPressurizationDingzi(SyncPressurizationDingzi syncPressurizationDingzi)
    {
        return syncPressurizationDingziMapper.insert(syncPressurizationDingzi);
    }

    /**
     * 修改耐压定子
     *
     * @param syncPressurizationDingzi 耐压定子
     * @return 结果
     */
    @Override
    public int updateSyncPressurizationDingzi(SyncPressurizationDingzi syncPressurizationDingzi)
    {
        return syncPressurizationDingziMapper.updateById(syncPressurizationDingzi);
    }

    /**
     * 批量删除耐压定子
     *
     * @param abhs 需要删除的耐压定子主键
     * @return 结果
     */
    @Override
    public int deleteSyncPressurizationDingziByAbhs(List<Long> abhs)
    {
        return syncPressurizationDingziMapper.deleteBatchIds(abhs);
    }

    /**
     * 删除耐压定子信息
     *
     * @param abh 耐压定子主键
     * @return 结果
     */
    @Override
    public int deleteSyncPressurizationDingziByAbh(Long abh)
    {
        return syncPressurizationDingziMapper.deleteById(abh);
    }
}
