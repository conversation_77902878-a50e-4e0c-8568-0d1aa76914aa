package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.cable.SheathExtruder;
import com.hzforward.oee.mapper.baseData.cable.SheathExtruderMapper;
import com.hzforward.sync.service.ISyncExtruderService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncExtruderServiceImpl implements ISyncExtruderService {

    @Resource
    private SheathExtruderMapper sheathExtruderMapper;


    /**
     * 查询分页列表
     *
     * @return
     */
    @Override
    public List<SheathExtruder> queryList() {
        return sheathExtruderMapper.selectList(new QueryWrapper<SheathExtruder>().orderByDesc("create_time"));
    }

    /**
     * 查询线速
     * @return
     */
    @Override
    public AjaxResult getLineSpeed() {
        List<SheathExtruder> list = sheathExtruderMapper.selectList(new LambdaQueryWrapper<SheathExtruder>().in(SheathExtruder::getEquipNo, "120挤塑机","150挤塑机").orderByDesc(SheathExtruder::getCreateTime).last(" limit 40"));
        List<String> timeList = new ArrayList<>();
        Map<String, Float> oneMap = new LinkedHashMap<>();
        Map<String, Float> twoMap = new LinkedHashMap<>();

        // 遍历每台设备，将数据分类存储到对应的映射中
        for (SheathExtruder machine : list) {
            String createTime = DateUtils.parseDateToStr("HH:mm",machine.getCreateTime());

            if (!timeList.contains(createTime)) {
                timeList.add(createTime);
            }

            switch (machine.getEquipNo()) {
                case "*********":
                    oneMap.put(createTime, machine.getLineSpeedReality());
                    break;
                case "*********":
                    twoMap.put(createTime, machine.getLineSpeedReality());
                    break;
            }
        }

        // 基于timeList创建对应的温度列表，确保时间和温度一一对应
        List<Float> oneList = new ArrayList<>();
        List<Float> twoList = new ArrayList<>();

        for (String time : timeList) {
            oneList.add(oneMap.getOrDefault(time, null));
            twoList.add(twoMap.getOrDefault(time, null));
        }
        JSONObject resObj = new JSONObject();
        resObj.put("timeList", timeList);
        resObj.put("oneList", oneList);
        resObj.put("twoList", twoList);
        return AjaxResult.success(resObj);
    }
}
