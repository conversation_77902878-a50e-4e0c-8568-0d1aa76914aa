package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.sync.domain.SyncPressurizationNaiya;
import com.hzforward.sync.domain.SyncProduction;
import com.hzforward.sync.domain.vo.SyncProductionQualified;
import com.hzforward.sync.domain.vo.SyncProductionQualifiedRes;
import com.hzforward.sync.mapper.SyncPressurizationNaiyaMapper;
import com.hzforward.sync.mapper.SyncProductionMapper;
import com.hzforward.sync.service.ISyncProductionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同步_排产数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
@Service
public class SyncProductionServiceImpl implements ISyncProductionService
{
    @Resource
    private SyncProductionMapper syncProductionMapper;
    @Resource
    private SyncPressurizationNaiyaMapper syncPressurizationNaiyaMapper;

    /**
     * 查询同步_排产数据
     *
     * @param id 同步_排产数据主键
     * @return 同步_排产数据
     */
    @Override
    public SyncProduction selectSyncProductionById(Long id)
    {
        return syncProductionMapper.selectById(id);
    }

    /**
     * 查询同步_排产数据列表
     *
     * @param syncProduction 同步_排产数据
     * @return 同步_排产数据
     */
    @Override
    public List<SyncProduction> selectSyncProductionList(SyncProduction syncProduction)
    {
        return syncProductionMapper.selectList(new LambdaQueryWrapper<>());
    }

    /**
     * 新增同步_排产数据
     *
     * @param syncProduction 同步_排产数据
     * @return 结果
     */
    @Override
    public int insertSyncProduction(SyncProduction syncProduction)
    {
        return syncProductionMapper.insert(syncProduction);
    }

    /**
     * 修改同步_排产数据
     *
     * @param syncProduction 同步_排产数据
     * @return 结果
     */
    @Override
    public int updateSyncProduction(SyncProduction syncProduction)
    {
        return syncProductionMapper.updateById(syncProduction);
    }

    /**
     * 批量删除同步_排产数据
     *
     * @param ids 需要删除的同步_排产数据主键
     * @return 结果
     */
    @Override
    public int deleteSyncProductionByIds(List<Long> ids)
    {
        return syncProductionMapper.deleteBatchIds(ids);
    }

    /**
     * 删除同步_排产数据信息
     *
     * @param id 同步_排产数据主键
     * @return 结果
     */
    @Override
    public int deleteSyncProductionById(Long id)
    {
        return syncProductionMapper.deleteById(id);
    }

    /**
     * 查询合格
     * @param syncProductionQualified
     * @return
     */
    @Override
    public List<SyncProductionQualifiedRes> queryQualified(SyncProductionQualified syncProductionQualified) {
        if (StringUtils.isEmpty(syncProductionQualified.getContractNo()) && StringUtils.isEmpty(syncProductionQualified.getOrderNo())) {
            throw new ServiceException("订单号、合同号必填一项");
        }
        if ((StringUtils.isNotEmpty(syncProductionQualified.getContractNo()) && syncProductionQualified.getContractNo().length() < 4)
                || (StringUtils.isNotEmpty(syncProductionQualified.getOrderNo()) && syncProductionQualified.getOrderNo().length() < 4)) {
            throw new ServiceException("订单号、合同号至少输入4位");
        }
        List<SyncProduction> list = syncProductionMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<SyncProduction>()
                .like(SyncProduction::getOrderNo, syncProductionQualified.getOrderNo())
                .like(SyncProduction::getContractNo, syncProductionQualified.getContractNo())
        );
        Map<String, SyncPressurizationNaiya> naiyaMap = new HashMap<>();
        if (StringUtils.isNotEmpty(list)) {
            List<String> serialNos = list.stream().map(SyncProduction::getSerialNo).collect(Collectors.toList());
            List<SyncPressurizationNaiya> naiyaList = syncPressurizationNaiyaMapper.selectList(new LambdaQueryWrapper<SyncPressurizationNaiya>().in(SyncPressurizationNaiya::getScbh, serialNos));
            naiyaMap = naiyaList.stream().collect(Collectors.toMap(SyncPressurizationNaiya::getScbh, syncPressurizationNaiya -> syncPressurizationNaiya, (k1, k2) -> {
                if (k1.getCszjg().equals("OK")) {
                    return k1;
                } else {
                    return k2;
                }
            }));
        }
        List<SyncProductionQualifiedRes> resList = new ArrayList<>();
        for (SyncProduction syncProduction : list) {
            SyncProductionQualifiedRes syncProductionQualifiedRes = BeanUtils.copyBeanPropAndReturn(syncProduction, SyncProductionQualifiedRes.class);
            SyncPressurizationNaiya naiya = naiyaMap.get(syncProduction.getSerialNo());
            if (StringUtils.isNotNull(naiya) && naiya.getCszjg().equals("OK")) {
                syncProductionQualifiedRes.setResult("已下线");
                syncProductionQualifiedRes.setOfflineTime(naiya.getCsrqsj());
            } else {
                syncProductionQualifiedRes.setResult("生产中");
            }
            resList.add(syncProductionQualifiedRes);
        }
        return resList;
    }
}
