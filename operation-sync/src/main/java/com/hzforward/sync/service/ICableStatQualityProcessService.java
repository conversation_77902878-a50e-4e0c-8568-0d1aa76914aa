package com.hzforward.sync.service;

import cn.hutool.json.JSONObject;
import com.hzforward.sync.domain.vo.TypeRatioRes;

import java.util.List;

/**
 * 同步_排产数据Service接口
 *
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ICableStatQualityProcessService {

    /**
     * 获取检验列表
     *
     * @return
     */
    List<String[]> getInspectionList(String createBy);

    /**
     *
     * @return
     */
    List<TypeRatioRes> getQuestionType(String createBy);

    /**
     * 或许工序合格率
     * @return
     */
    JSONObject getProductionProcessesQualified(String createBy);

    /**
     * 获取过程问题责任人统计
     * @return
     */
    JSONObject getQuestionPerson(String createBy);

    /**
     * 问题工序分类
     * @return
     */
    JSONObject getProductionProcessesClassify(String createBy);

}
