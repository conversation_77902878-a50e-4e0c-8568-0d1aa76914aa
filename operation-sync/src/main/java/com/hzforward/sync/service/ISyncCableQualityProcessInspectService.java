package com.hzforward.sync.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzforward.sync.domain.SyncCableQualityProcessInspect;

import java.util.List;

/**
 * 电缆车间排产完成Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ISyncCableQualityProcessInspectService extends IService<SyncCableQualityProcessInspect>
{
    /**
     * 查询电缆车间排产完成列表
     * 
     * @param syncCableQualityProcessInspect 电缆车间排产完成
     * @return 电缆车间排产完成集合
     */
    List<SyncCableQualityProcessInspect> selectSyncCableQualityProcessInspectList(SyncCableQualityProcessInspect syncCableQualityProcessInspect);

    /**
     * 导入
     * @param qualityProcessInspects
     * @return
     */
    String importData(List<SyncCableQualityProcessInspect> qualityProcessInspects);

    /**
     * 插入
     * @param syncCableQualityProcessInspect
     * @return
     */
    int insert(SyncCableQualityProcessInspect syncCableQualityProcessInspect);
}
