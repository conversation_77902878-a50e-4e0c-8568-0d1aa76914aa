package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.sync.domain.SyncCableQualityProcessInspect;
import com.hzforward.sync.mapper.SyncCableQualityProcessInspectMapper;
import com.hzforward.sync.service.ISyncCableQualityProcessInspectService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 电缆车间排产完成Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
public class SyncCableQualityProcessInspectServiceImpl extends ServiceImpl<SyncCableQualityProcessInspectMapper, SyncCableQualityProcessInspect> implements ISyncCableQualityProcessInspectService
{
    @Resource
    private SyncCableQualityProcessInspectMapper syncCableQualityProcessInspectMapper;

    /**
     * 查询电缆车间排产完成列表
     *
     * @param syncCableQualityProcessInspect 电缆车间排产完成
     * @return 电缆车间排产完成
     */
    @Override
    public List<SyncCableQualityProcessInspect> selectSyncCableQualityProcessInspectList(SyncCableQualityProcessInspect syncCableQualityProcessInspect)
    {
        return syncCableQualityProcessInspectMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<SyncCableQualityProcessInspect>()
                .eq(SyncCableQualityProcessInspect::getRecordDate, syncCableQualityProcessInspect.getRecordDate())
                .eq(SyncCableQualityProcessInspect::getProcedureName, syncCableQualityProcessInspect.getProcedureName())
                .eq(SyncCableQualityProcessInspect::getProduceNo, syncCableQualityProcessInspect.getProduceNo())
                .eq(SyncCableQualityProcessInspect::getUnqualifiedNo, syncCableQualityProcessInspect.getUnqualifiedNo())
                .eq(SyncCableQualityProcessInspect::getDutyBy, syncCableQualityProcessInspect.getDutyBy())
                .eq(SyncCableQualityProcessInspect::getProblemType, syncCableQualityProcessInspect.getProblemType())
                .eq(SyncCableQualityProcessInspect::getPassRate, syncCableQualityProcessInspect.getPassRate())
        );
    }

    /**
     * 导入
     * @param qualityProcessInspects
     * @return
     */
    @Override
    public String importData(List<SyncCableQualityProcessInspect> qualityProcessInspects) {
        for (SyncCableQualityProcessInspect qualityProcessInspect : qualityProcessInspects) {
            syncCableQualityProcessInspectMapper.insert(qualityProcessInspect);
        }
        return "success";
    }

    @Override
    public int insert(SyncCableQualityProcessInspect syncCableQualityProcessInspect) {
        return syncCableQualityProcessInspectMapper.insert(syncCableQualityProcessInspect);
    }
}
