package com.hzforward.sync.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.oee.domain.baseData.host.CleanRollerLine;
import com.hzforward.oee.mapper.baseData.host.CleanRollerLineMapper;
import com.hzforward.sync.service.IEquipmentRollerLineDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 辊道铁链PLC同步数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Service
@RequiredArgsConstructor
public class EquipmentRollerLineDataServiceImpl extends ServiceImpl<CleanRollerLineMapper, CleanRollerLine> implements IEquipmentRollerLineDataService
{
    private final CleanRollerLineMapper cleanRollerLineMapper;

    /**
     * 查询辊道铁链PLC同步数据列表
     *
     * @param cleanRollerLine 辊道铁链PLC同步数据
     * @return 辊道铁链PLC同步数据
     */
    @Override
    public List<CleanRollerLine> selectEquipmentRollerLineDataList(CleanRollerLine cleanRollerLine)
    {
        return cleanRollerLineMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<CleanRollerLine>()
                .eq(CleanRollerLine::getVd1124, cleanRollerLine.getVd1124())
                .eq(CleanRollerLine::getVd1128, cleanRollerLine.getVd1128())
                .eq(CleanRollerLine::getVd1132, cleanRollerLine.getVd1132())
                .eq(CleanRollerLine::getVd1136, cleanRollerLine.getVd1136())
                .eq(CleanRollerLine::getVd1140, cleanRollerLine.getVd1140())
                .eq(CleanRollerLine::getVd1144, cleanRollerLine.getVd1144())
                .eq(CleanRollerLine::getVd1148, cleanRollerLine.getVd1148())
                .eq(CleanRollerLine::getVd1152, cleanRollerLine.getVd1152())
                .eq(CleanRollerLine::getVd1156, cleanRollerLine.getVd1156())
                .eq(CleanRollerLine::getVd1160, cleanRollerLine.getVd1160())
                .eq(CleanRollerLine::getVd1164, cleanRollerLine.getVd1164())
                .eq(CleanRollerLine::getVd1168, cleanRollerLine.getVd1168())
                .eq(CleanRollerLine::getVd1172, cleanRollerLine.getVd1172())
                .eq(CleanRollerLine::getVd1176, cleanRollerLine.getVd1176())
                .eq(CleanRollerLine::getVd1180, cleanRollerLine.getVd1180())
                .eq(CleanRollerLine::getVd1184, cleanRollerLine.getVd1184())
                .eq(CleanRollerLine::getVd1188, cleanRollerLine.getVd1188())
                .eq(CleanRollerLine::getVd1192, cleanRollerLine.getVd1192())
                .eq(CleanRollerLine::getVd1196, cleanRollerLine.getVd1196())
                .eq(CleanRollerLine::getVd1200, cleanRollerLine.getVd1200())
                .eq(CleanRollerLine::getVd1204, cleanRollerLine.getVd1204())
                .eq(CleanRollerLine::getVd1208, cleanRollerLine.getVd1208())
                .eq(CleanRollerLine::getVd1212, cleanRollerLine.getVd1212())
                .eq(CleanRollerLine::getVd1216, cleanRollerLine.getVd1216())
                .eq(CleanRollerLine::getVd1220, cleanRollerLine.getVd1220())
                .eq(CleanRollerLine::getVd1224, cleanRollerLine.getVd1224())
                .eq(CleanRollerLine::getVd1228, cleanRollerLine.getVd1228())
                .eq(CleanRollerLine::getVd1232, cleanRollerLine.getVd1232())
                .eq(CleanRollerLine::getVd1236, cleanRollerLine.getVd1236())
                .eq(CleanRollerLine::getVd1240, cleanRollerLine.getVd1240())
                .eq(CleanRollerLine::getVd1244, cleanRollerLine.getVd1244())
                .eq(CleanRollerLine::getV12480, cleanRollerLine.getV12480())
                .eq(CleanRollerLine::getV12481, cleanRollerLine.getV12481())
                .eq(CleanRollerLine::getV12482, cleanRollerLine.getV12482())
                .eq(CleanRollerLine::getV12483, cleanRollerLine.getV12483())
                .eq(CleanRollerLine::getV12484, cleanRollerLine.getV12484())
                .eq(CleanRollerLine::getV12485, cleanRollerLine.getV12485())
                .eq(CleanRollerLine::getV12486, cleanRollerLine.getV12486())
                .eq(CleanRollerLine::getV12487, cleanRollerLine.getV12487())
                .eq(CleanRollerLine::getV12490, cleanRollerLine.getV12490())
                .eq(CleanRollerLine::getV12491, cleanRollerLine.getV12491())
                .eq(CleanRollerLine::getV12492, cleanRollerLine.getV12492())
                .eq(CleanRollerLine::getV12493, cleanRollerLine.getV12493())
                .eq(CleanRollerLine::getV12494, cleanRollerLine.getV12494())
                .eq(CleanRollerLine::getV12495, cleanRollerLine.getV12495())
                .eq(CleanRollerLine::getV12496, cleanRollerLine.getV12496())
                .eq(CleanRollerLine::getV12497, cleanRollerLine.getV12497())
                .eq(CleanRollerLine::getV12500, cleanRollerLine.getV12500())
                .eq(CleanRollerLine::getV12501, cleanRollerLine.getV12501())
                .eq(CleanRollerLine::getV12502, cleanRollerLine.getV12502())
                .eq(CleanRollerLine::getV12503, cleanRollerLine.getV12503())
                .eq(CleanRollerLine::getV12510, cleanRollerLine.getV12510())
                .eq(CleanRollerLine::getV12511, cleanRollerLine.getV12511())
                .eq(CleanRollerLine::getV12512, cleanRollerLine.getV12512())
                .eq(CleanRollerLine::getV12513, cleanRollerLine.getV12513())
                .eq(CleanRollerLine::getV12514, cleanRollerLine.getV12514())
                .eq(CleanRollerLine::getV12515, cleanRollerLine.getV12515())
                .eq(CleanRollerLine::getV12516, cleanRollerLine.getV12516())
                .eq(CleanRollerLine::getV12517, cleanRollerLine.getV12517())
                .eq(CleanRollerLine::getV12520, cleanRollerLine.getV12520())
                .eq(CleanRollerLine::getV12521, cleanRollerLine.getV12521())
                .eq(CleanRollerLine::getV12522, cleanRollerLine.getV12522())
                .eq(CleanRollerLine::getV12523, cleanRollerLine.getV12523())
                .eq(CleanRollerLine::getV12524, cleanRollerLine.getV12524())
                .eq(CleanRollerLine::getV12525, cleanRollerLine.getV12525())
                .eq(CleanRollerLine::getV12526, cleanRollerLine.getV12526())
                .eq(CleanRollerLine::getV12527, cleanRollerLine.getV12527())
                .eq(CleanRollerLine::getV12530, cleanRollerLine.getV12530())
                .eq(CleanRollerLine::getV12531, cleanRollerLine.getV12531())
                .eq(CleanRollerLine::getV12532, cleanRollerLine.getV12532())
                .eq(CleanRollerLine::getV12533, cleanRollerLine.getV12533())
                .eq(CleanRollerLine::getV12540, cleanRollerLine.getV12540())
                .eq(CleanRollerLine::getV12541, cleanRollerLine.getV12541())
                .eq(CleanRollerLine::getV12542, cleanRollerLine.getV12542())
                .eq(CleanRollerLine::getV12543, cleanRollerLine.getV12543())
                .eq(CleanRollerLine::getV12544, cleanRollerLine.getV12544())
                .eq(CleanRollerLine::getV12545, cleanRollerLine.getV12545())
                .eq(CleanRollerLine::getV12546, cleanRollerLine.getV12546())
                .eq(CleanRollerLine::getV12547, cleanRollerLine.getV12547())
                .eq(CleanRollerLine::getV12550, cleanRollerLine.getV12550())
                .eq(CleanRollerLine::getV12551, cleanRollerLine.getV12551())
                .eq(CleanRollerLine::getV12552, cleanRollerLine.getV12552())
                .eq(CleanRollerLine::getV12553, cleanRollerLine.getV12553())
                .eq(CleanRollerLine::getV12554, cleanRollerLine.getV12554())
                .eq(CleanRollerLine::getV12555, cleanRollerLine.getV12555())
                .eq(CleanRollerLine::getV12556, cleanRollerLine.getV12556())
                .eq(CleanRollerLine::getV12557, cleanRollerLine.getV12557())
                .eq(CleanRollerLine::getV12560, cleanRollerLine.getV12560())
                .eq(CleanRollerLine::getV12561, cleanRollerLine.getV12561())
                .eq(CleanRollerLine::getV12562, cleanRollerLine.getV12562())
                .eq(CleanRollerLine::getV12563, cleanRollerLine.getV12563())
                .eq(CleanRollerLine::getV12564, cleanRollerLine.getV12564())
                .eq(CleanRollerLine::getV12565, cleanRollerLine.getV12565())
        );
    }
}
