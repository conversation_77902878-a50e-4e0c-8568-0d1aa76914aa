package com.hzforward.sync.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.oee.domain.baseData.host.CleanRotorLine;
import com.hzforward.oee.mapper.baseData.host.CleanRotorLineMapper;
import com.hzforward.sync.service.IEquipmentRotorLineDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 转子线PLC同步数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Service
@RequiredArgsConstructor
public class EquipmentRotorLineDataServiceImpl extends ServiceImpl<CleanRotorLineMapper, CleanRotorLine> implements IEquipmentRotorLineDataService {

    private final CleanRotorLineMapper cleanRotorLineMapper;

    /**
     * 查询转子线PLC同步数据列表
     *
     * @param cleanRotorLine 转子线PLC同步数据
     * @return 转子线PLC同步数据
     */
    @Override
    public List<CleanRotorLine> selectEquipmentRotorLineDataList(CleanRotorLine cleanRotorLine) {
        return cleanRotorLineMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<CleanRotorLine>()
                .eq(CleanRotorLine::getVd50, cleanRotorLine.getVd50())
                .eq(CleanRotorLine::getVd54, cleanRotorLine.getVd54())
                .eq(CleanRotorLine::getVd58, cleanRotorLine.getVd58())
                .eq(CleanRotorLine::getVd62, cleanRotorLine.getVd62())
                .eq(CleanRotorLine::getVd66, cleanRotorLine.getVd66())
                .eq(CleanRotorLine::getVd70, cleanRotorLine.getVd70())
                .eq(CleanRotorLine::getVd74, cleanRotorLine.getVd74())
                .eq(CleanRotorLine::getVd78, cleanRotorLine.getVd78())
                .eq(CleanRotorLine::getVd82, cleanRotorLine.getVd82())
                .eq(CleanRotorLine::getVd86, cleanRotorLine.getVd86())
                .eq(CleanRotorLine::getVd90, cleanRotorLine.getVd90())
                .eq(CleanRotorLine::getVd94, cleanRotorLine.getVd94())
                .eq(CleanRotorLine::getVd98, cleanRotorLine.getVd98())
                .eq(CleanRotorLine::getVd102, cleanRotorLine.getVd102())
                .eq(CleanRotorLine::getVd106, cleanRotorLine.getVd106())
                .eq(CleanRotorLine::getVd110, cleanRotorLine.getVd110())
                .eq(CleanRotorLine::getVd114, cleanRotorLine.getVd114())
                .eq(CleanRotorLine::getVd118, cleanRotorLine.getVd118())
                .eq(CleanRotorLine::getVd122, cleanRotorLine.getVd122())
                .eq(CleanRotorLine::getVd126, cleanRotorLine.getVd126())
                .eq(CleanRotorLine::getVd130, cleanRotorLine.getVd130())
                .eq(CleanRotorLine::getVd134, cleanRotorLine.getVd134())
                .eq(CleanRotorLine::getVd138, cleanRotorLine.getVd138())
                .eq(CleanRotorLine::getVd142, cleanRotorLine.getVd142())
                .eq(CleanRotorLine::getVd146, cleanRotorLine.getVd146())
                .eq(CleanRotorLine::getVd150, cleanRotorLine.getVd150())
                .eq(CleanRotorLine::getVd154, cleanRotorLine.getVd154())
                .eq(CleanRotorLine::getVd158, cleanRotorLine.getVd158())
                .eq(CleanRotorLine::getVd162, cleanRotorLine.getVd162())
                .eq(CleanRotorLine::getVd166, cleanRotorLine.getVd166())
                .eq(CleanRotorLine::getVd820, cleanRotorLine.getVd820())
                .eq(CleanRotorLine::getVd824, cleanRotorLine.getVd824())
                .eq(CleanRotorLine::getVd828, cleanRotorLine.getVd828())
                .eq(CleanRotorLine::getVd832, cleanRotorLine.getVd832())
                .eq(CleanRotorLine::getVd836, cleanRotorLine.getVd836())
                .eq(CleanRotorLine::getVd840, cleanRotorLine.getVd840())
                .eq(CleanRotorLine::getVd844, cleanRotorLine.getVd844())
                .eq(CleanRotorLine::getVd848, cleanRotorLine.getVd848())
                .eq(CleanRotorLine::getVd852, cleanRotorLine.getVd852())
                .eq(CleanRotorLine::getVd856, cleanRotorLine.getVd856())
                .eq(CleanRotorLine::getVd860, cleanRotorLine.getVd860())
                .eq(CleanRotorLine::getVd864, cleanRotorLine.getVd864())
                .eq(CleanRotorLine::getVd868, cleanRotorLine.getVd868())
                .eq(CleanRotorLine::getVd872, cleanRotorLine.getVd872())
                .eq(CleanRotorLine::getVd876, cleanRotorLine.getVd876())
                .eq(CleanRotorLine::getVd880, cleanRotorLine.getVd880())
                .eq(CleanRotorLine::getVd884, cleanRotorLine.getVd884())
                .eq(CleanRotorLine::getVd888, cleanRotorLine.getVd888())
                .eq(CleanRotorLine::getVd892, cleanRotorLine.getVd892())
                .eq(CleanRotorLine::getVd896, cleanRotorLine.getVd896())
        );
    }

    /**
     * 查询粗洗PH值折线图
     *
     * @return
     */
    @Override
    public JSONObject roughWashingPh() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        List<String> frontPhs = new ArrayList<>();
        List<String> oppositePhs = new ArrayList<>();

        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            phs.add(String.format("%.2f", cleanRotorLine.getVd820()));
            frontPhs.add(String.format("%.2f", cleanRotorLine.getVd868()));
            oppositePhs.add(String.format("%.2f", cleanRotorLine.getVd884()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        jsonObject.put("twoList", frontPhs);
        jsonObject.put("threeList", oppositePhs);
        return jsonObject;
    }

    /**
     * 查询粗洗温度折线图
     *
     * @return
     */
    @Override
    public JSONObject roughWashingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanRotorLine.getVd82()));
            realitys.add(String.format("%.2f", cleanRotorLine.getVd50()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询精洗PH值折线图
     * @return
     */
    @Override
    public JSONObject fineWashingPh() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        List<String> frontPhs = new ArrayList<>();
        List<String> oppositePhs = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            phs.add(String.format("%.2f", cleanRotorLine.getVd824()));
            frontPhs.add(String.format("%.2f", cleanRotorLine.getVd872()));
            oppositePhs.add(String.format("%.2f", cleanRotorLine.getVd888()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        jsonObject.put("twoList", frontPhs);
        jsonObject.put("threeList", oppositePhs);
        return jsonObject;
    }

    /**
     * 查询精洗温度折线图
     * @return
     */
    @Override
    public JSONObject fineWashingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanRotorLine.getVd86()));
            realitys.add(String.format("%.2f", cleanRotorLine.getVd54()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询冷却预热炉温度
     * @return
     */
    @Override
    public JSONObject coolingPreheatingFurnaceTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanRotorLine.getVd102()));
            realitys.add(String.format("%.2f", cleanRotorLine.getVd70()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询脱水烘干温度
     * @return
     */
    @Override
    public JSONObject dehydrationDryingTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanRotorLine.getVd98()));
            realitys.add(String.format("%.2f", cleanRotorLine.getVd66()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询预防锈PH
     * @return
     */
    @Override
    public JSONObject preventingRustPh() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        List<String> frontPhs = new ArrayList<>();
        List<String> oppositePhs = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            phs.add(String.format("%.2f", cleanRotorLine.getVd828()));
            frontPhs.add(String.format("%.2f", cleanRotorLine.getVd876()));
            oppositePhs.add(String.format("%.2f", cleanRotorLine.getVd892()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        jsonObject.put("twoList", frontPhs);
        jsonObject.put("threeList", oppositePhs);
        return jsonObject;
    }

    /**
     * 查询预防锈温度
     * @return
     */
    @Override
    public JSONObject preventingRustTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanRotorLine.getVd90()));
            realitys.add(String.format("%.2f", cleanRotorLine.getVd58()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }

    /**
     * 查询主防锈PH
     * @return
     */
    @Override
    public JSONObject mainRustPreventionPh() {
        List<String> times = new ArrayList<>();
        List<String> phs = new ArrayList<>();
        List<String> frontPhs = new ArrayList<>();
        List<String> oppositePhs = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            phs.add(String.format("%.2f", cleanRotorLine.getVd832()));
            frontPhs.add(String.format("%.2f", cleanRotorLine.getVd880()));
            oppositePhs.add(String.format("%.2f", cleanRotorLine.getVd896()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", phs);
        jsonObject.put("twoList", frontPhs);
        jsonObject.put("threeList", oppositePhs);
        return jsonObject;
    }

    /**
     * 查询主防锈温度
     * @return
     */
    @Override
    public JSONObject mainRustPreventionTemperature() {
        List<String> times = new ArrayList<>();
        List<String> sets = new ArrayList<>();
        List<String> realitys = new ArrayList<>();
        // 获取最新一条数据时间
        List<CleanRotorLine> cleanRotorLineList = cleanRotorLineMapper.selectList(Page.of(0, 10),
                new LambdaQueryWrapper<CleanRotorLine>().orderByDesc(CleanRotorLine::getCreateTime));
        for (CleanRotorLine cleanRotorLine : cleanRotorLineList) {
            times.add(DateUtils.parseDateToStr(DateUtils.MM_SS, cleanRotorLine.getCreateTime()));
            sets.add(String.format("%.2f", cleanRotorLine.getVd94()));
            realitys.add(String.format("%.2f", cleanRotorLine.getVd62()));
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timeList", times);
        jsonObject.put("oneList", sets);
        jsonObject.put("twoList", realitys);
        return jsonObject;
    }


//    /**
//     * 查询粗细PH值折线图
//     *
//     * @return
//     */
//    @Override
//    public JSONObject roughWashingPh() {
//        List<String> times = new ArrayList<>();
//        List<String> phs = new ArrayList<>();
//        List<String> frontPhs = new ArrayList<>();
//        List<String> oppositePhs = new ArrayList<>();
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("timeList", times);
//        jsonObject.put("oneList", phs);
//        jsonObject.put("twoList", frontPhs);
//        jsonObject.put("threeList", oppositePhs);
//        // 获取最新一条数据时间
//        List<EquipmentRotorLineData> topList = equipmentRotorLineDataMapper.selectList(Page.of(0, 1),
//                new LambdaQueryWrapper<EquipmentRotorLineData>().orderByDesc(EquipmentRotorLineData::getCreateTime));
//        if (topList.isEmpty()) {
//            return jsonObject;
//        }
//        Date endTime = topList.get(0).getCreateTime();
//        // 获取最近60分钟数据
//        Date startTime = DateUtils.addMinutes(endTime, -60);
//        List<EquipmentRotorLineData> equipmentBaseLineDataList = equipmentRotorLineDataMapper.selectList(
//                new LambdaQueryWrapper<EquipmentRotorLineData>().le(EquipmentRotorLineData::getCreateTime,endTime)
//                        .ge(EquipmentRotorLineData::getCreateTime,startTime).orderByDesc(EquipmentRotorLineData::getCreateTime));
//        // 使用Map来分组数据
//        Map<Long, List<EquipmentRotorLineData>> dataMap = new HashMap<>();
//        for (EquipmentRotorLineData equipmentBaseLineData : equipmentBaseLineDataList) {
//            long minutesBucket = equipmentBaseLineData.getCreateTime().getTime() / (6 * 60 * 1000); // 每6分钟作为一个桶
//            dataMap.computeIfAbsent(minutesBucket, k -> new ArrayList<>()).add(equipmentBaseLineData);
//        }
//        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
//        // 按时间桶排序并计算平均值
//        dataMap.entrySet().stream()
//                .sorted(Map.Entry.comparingByKey()) // 先按时间桶升序排序
//                .forEach(entry -> {
//                    List<EquipmentRotorLineData> entries = entry.getValue();
//                    double totalVd820 = 0;
//                    double totalVd868 = 0;
//                    double totalVd884 = 0;
//                    for (EquipmentRotorLineData data : entries) {
//                        totalVd820 += data.getVd820();
//                        totalVd868 += data.getVd868();
//                        totalVd884 += data.getVd884();
//                    }
//                    // 获取6分钟桶的开始时间
//                    Date bucketStartTime = new Date(entry.getKey() * 6 * 60 * 1000);
//                    String timeBucketStart = timeFormat.format(bucketStartTime);
//                    times.add(timeBucketStart);
//                    int count = entries.size();
//                    if (count > 0) {
//                        phs.add(String.format("%.2f", totalVd820 / count)); // 计算平均值
//                        frontPhs.add(String.format("%.2f", totalVd868 / count));
//                        oppositePhs.add(String.format("%.2f", totalVd884 / count));
//                    } else {
//                        phs.add("0.00");
//                        frontPhs.add("0.00");
//                        oppositePhs.add("0.00");
//}
//                });
//                        // 将结果反转，使最新的时间排在最前
//                        Collections.
//
//reverse(times);
//        Collections.
//
//reverse(phs);
//        Collections.
//
//reverse(frontPhs);
//        Collections.
//
//reverse(oppositePhs);
//        return jsonObject;
//    }
}
