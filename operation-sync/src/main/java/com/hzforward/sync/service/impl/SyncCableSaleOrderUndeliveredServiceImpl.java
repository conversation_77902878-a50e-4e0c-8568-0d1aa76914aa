package com.hzforward.sync.service.impl;

import java.util.List;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import javax.annotation.Resource;

import com.hzforward.sync.domain.SyncCableProductionComplete;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.hzforward.sync.mapper.SyncCableSaleOrderUndeliveredMapper;
import com.hzforward.sync.domain.SyncCableSaleOrderUndelivered;
import com.hzforward.sync.service.ISyncCableSaleOrderUndeliveredService;

/**
 * 销售订单未交统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Service
public class SyncCableSaleOrderUndeliveredServiceImpl implements ISyncCableSaleOrderUndeliveredService
{
    @Resource
    private SyncCableSaleOrderUndeliveredMapper syncCableSaleOrderUndeliveredMapper;

    /**
     * 查询销售订单未交统计
     *
     * @param id 销售订单未交统计主键
     * @return 销售订单未交统计
     */
    @Override
    public SyncCableSaleOrderUndelivered selectSyncCableSaleOrderUndeliveredById(Long id)
    {
        return syncCableSaleOrderUndeliveredMapper.selectById(id);
    }

    /**
     * 查询销售订单未交统计列表
     *
     * @param syncCableSaleOrderUndelivered 销售订单未交统计
     * @return 销售订单未交统计
     */
    @Override
    public List<SyncCableSaleOrderUndelivered> selectSyncCableSaleOrderUndeliveredList(SyncCableSaleOrderUndelivered syncCableSaleOrderUndelivered)
    {
        return syncCableSaleOrderUndeliveredMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<SyncCableSaleOrderUndelivered>()
                .eq(SyncCableSaleOrderUndelivered::getMaterialCode, syncCableSaleOrderUndelivered.getMaterialCode())
                .eq(SyncCableSaleOrderUndelivered::getRef, syncCableSaleOrderUndelivered.getRef())
                .eq(SyncCableSaleOrderUndelivered::getSpecifications, syncCableSaleOrderUndelivered.getSpecifications())
                .eq(SyncCableSaleOrderUndelivered::getUnit, syncCableSaleOrderUndelivered.getUnit())
                .eq(SyncCableSaleOrderUndelivered::getOrderNo, syncCableSaleOrderUndelivered.getOrderNo())
                .eq(SyncCableSaleOrderUndelivered::getStoreNo, syncCableSaleOrderUndelivered.getStoreNo())
                .eq(SyncCableSaleOrderUndelivered::getDifferenceNo, syncCableSaleOrderUndelivered.getDifferenceNo())
        );
    }

    /**
     * 新增销售订单未交统计
     *
     * @param syncCableSaleOrderUndelivered 销售订单未交统计
     * @return 结果
     */
    @Override
    public int insertSyncCableSaleOrderUndelivered(SyncCableSaleOrderUndelivered syncCableSaleOrderUndelivered)
    {
        return syncCableSaleOrderUndeliveredMapper.insert(syncCableSaleOrderUndelivered);
    }

    /**
     * 修改销售订单未交统计
     *
     * @param syncCableSaleOrderUndelivered 销售订单未交统计
     * @return 结果
     */
    @Override
    public int updateSyncCableSaleOrderUndelivered(SyncCableSaleOrderUndelivered syncCableSaleOrderUndelivered)
    {
        return syncCableSaleOrderUndeliveredMapper.updateById(syncCableSaleOrderUndelivered);
    }

    /**
     * 删除销售订单未交统计
     *
     * @param ids 需要删除的销售订单未交统计主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteSyncCableSaleOrderUndeliveredByIds(List<Long> ids)
    {
        return syncCableSaleOrderUndeliveredMapper.deleteBatchIds(ids);
    }

    /**
     * 导入
     * @param cableProductionCompletes
     * @return
     */
    @Override
    public String importData(List<SyncCableSaleOrderUndelivered> cableProductionCompletes) {
        for (SyncCableSaleOrderUndelivered syncCableSaleOrderUndelivered : cableProductionCompletes) {
            syncCableSaleOrderUndeliveredMapper.insert(syncCableSaleOrderUndelivered);
        }
        return "";
    }

}
