package com.hzforward.sync.service;

import java.util.List;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.sync.domain.SyncMeasuringToolTorsion;

/**
 * 同步_扭力测试Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-15
 */
public interface ISyncMeasuringToolTorsionService 
{
    /**
     * 查询同步_扭力测试
     * 
     * @param id 同步_扭力测试主键
     * @return 同步_扭力测试
     */
    public SyncMeasuringToolTorsion selectSyncMeasuringToolTorsionById(Long id);

    /**
     * 查询同步_扭力测试列表
     * 
     * @param syncMeasuringToolTorsion 同步_扭力测试
     * @return 同步_扭力测试集合
     */
    public List<SyncMeasuringToolTorsion> selectSyncMeasuringToolTorsionList(SyncMeasuringToolTorsion syncMeasuringToolTorsion);

    /**
     * 新增同步_扭力测试
     * 
     * @param syncMeasuringToolTorsion 同步_扭力测试
     * @return 结果
     */
    public int insertSyncMeasuringToolTorsion(SyncMeasuringToolTorsion syncMeasuringToolTorsion);

    /**
     * 修改同步_扭力测试
     * 
     * @param syncMeasuringToolTorsion 同步_扭力测试
     * @return 结果
     */
    public int updateSyncMeasuringToolTorsion(SyncMeasuringToolTorsion syncMeasuringToolTorsion);

    /**
     * 批量删除同步_扭力测试
     * 
     * @param ids 需要删除的同步_扭力测试主键集合
     * @return 结果
     */
    public int deleteSyncMeasuringToolTorsionByIds(List<Long> ids);

    /**
     * 删除同步_扭力测试信息
     * 
     * @param id 同步_扭力测试主键
     * @return 结果
     */
    public int deleteSyncMeasuringToolTorsionById(Long id);
}
