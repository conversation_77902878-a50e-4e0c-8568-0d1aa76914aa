package com.hzforward.sync.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 条形T线测试数据实体类
 * 对应Oracle数据库中的ILA6U_ZS0001_V230922表
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Data
@TableName("ILA6U_ZS0001_V230922")
public class BarTLineTestData {

    /**
     * 测试时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdTime;

    /**
     * 出厂编号
     */
    private String shortText1695362815861;

    /**
     * 其他可能的字段，根据实际表结构添加
     */
    // 可以根据实际需要添加更多字段
}
