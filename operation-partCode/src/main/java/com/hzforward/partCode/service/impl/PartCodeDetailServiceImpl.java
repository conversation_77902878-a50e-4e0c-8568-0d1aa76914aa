package com.hzforward.partCode.service.impl;

import com.hzforward.common.config.OperationConfig;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.common.enums.ErrorEnum;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.SecurityUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.file.FileUploadUtils;
import com.hzforward.partCode.domain.PartCodeDetail;
import com.hzforward.partCode.domain.PartCodeOaApply;
import com.hzforward.partCode.domain.PartCodeSmallType;
import com.hzforward.partCode.mapper.PartCodeDetailMapper;
import com.hzforward.partCode.mapper.PartCodeSmallTypeMapper;
import com.hzforward.partCode.service.IPartCodeDetailService;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 件号详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-04-19
 */
@Service
public class PartCodeDetailServiceImpl implements IPartCodeDetailService
{
    @Resource
    private PartCodeDetailMapper partCodeDetailMapper;

    @Resource
    private PartCodeSmallTypeMapper partCodeSmallTypeMapper;

    /**
     * 查询件号详情
     *
     * @param partId 件号详情主键
     * @return 件号详情
     */
    @Override
    public PartCodeDetail selectPartCodeDetailByPartId(Long partId)
    {
        PartCodeDetail partCodeDetail = partCodeDetailMapper.selectPartCodeDetailByPartId(partId);
        if(partCodeDetail.getUploadFilePath()!=null && !partCodeDetail.getUploadFilePath().equals("")){
            List<String> uploadPathList = new ArrayList<>();
            File[] fileList = new File(partCodeDetail.getUploadFilePath()).listFiles();

            if(fileList!= null){
                for (File file : fileList) {
                    uploadPathList.add("/profile/partCode/"+partCodeDetail.getPartCode()+"/"+file.getName());
                }
            }

            partCodeDetail.setUploadFilePathList(uploadPathList);
        }
        return partCodeDetail;
    }

    /**
     * 查询件号详情列表
     *
     * @param partCodeDetail 件号详情
     * @return 件号详情
     */
    @Override
    public List<PartCodeDetail> selectPartCodeDetailList(PartCodeDetail partCodeDetail)
    {
        partCodeDetail.setDelFlag("0");
        return partCodeDetailMapper.selectPartCodeDetailList(partCodeDetail);
    }

    /**
     * 新增件号详情
     *
     * @param partCodeDetail 件号详情
     * @param fileList 文件列表
     * @return 结果
     */
    @Override
    public int insertPartCodeDetail(MultipartFile[] fileList,PartCodeDetail partCodeDetail)
    {
        //上传文件
        if(fileList != null && fileList.length > 0){
            String baseDir = OperationConfig.getProfile()  + "/partCode/" + partCodeDetail.getPartCode();
            partCodeDetail.setUploadFilePath(baseDir);

            for (MultipartFile file : fileList) {
                try{
                    FileUploadUtils.uploadPartCodeFile(baseDir,file);
                }catch (IOException e){
                    throw new ServiceException(ErrorEnum.FILE_WRITE_FAIL.getName());
                }
            }
        }

        if(partCodeDetail.getOfficialFlag() == null){
            //默认设置件号未出正式
            partCodeDetail.setOfficialFlag("0");
        }
        //默认设置件号试制未转正
        partCodeDetail.setTrialFlag("0");
        //默认设置未使用
        partCodeDetail.setUseFlag("0");
        //默认设置OA未申请
        partCodeDetail.setOaFlag("0");
        //设置申请人信息
        partCodeDetail.setApplicant(SecurityUtils.getLoginUser().getUsername());
        partCodeDetail.setApplicantName(SecurityUtils.getLoginUser().getUser().getNickName());
        //设置申请时间
        partCodeDetail.setApplicantTime(DateUtils.getNowDate());

        partCodeDetail.setDelFlag("0");
        partCodeDetail.setCreateTime(DateUtils.getNowDate());
        partCodeDetail.setCreateBy(SecurityUtils.getLoginUser().getUsername());

        return partCodeDetailMapper.insertPartCodeDetail(partCodeDetail);
    }

    /**
     * 件号生成
     *
     * @param fileList       文件列表
     * @param partCodeDetail 件号详情
     * @return 结果
     */
    @Override
    public int createPartCodeDetail(MultipartFile[] fileList, PartCodeDetail partCodeDetail) {
        switch (partCodeDetail.getBusiness()){
            case "generationPartCode":
                return generationPartCode(fileList,partCodeDetail);
            case "expandPartCode":
                return expandPartCode(fileList,partCodeDetail);
            case "nonStandardPartCode":
                return nonStandardPartCode(fileList,partCodeDetail);
            case "modulePartCode":
                return modulePartCode(fileList,partCodeDetail);
            case "officialPartCode":
            case "roughcastPartCode":
                return officialRoughcastPartCode(fileList,partCodeDetail);
            case "trialProductionEmployment":
                return trialProductionEmployment(fileList,partCodeDetail);
            default:
                throw new ServiceException(ErrorEnum.BUSINESS_ERROR.getName());
        }
    }

    /**
     * 件号生成
     */
    public int generationPartCode(MultipartFile[] fileList, PartCodeDetail partCodeDetail){
        String partCode = "";

        if(StringUtils.isNotEmpty(partCodeDetail.getPartCodeSegment())) {
            if(partCodeDetail.getPartCodeSegment().length() != 3) {
                throw new ServiceException(ErrorEnum.SEGMENT_LENGTH_ERROR.getName());
            }

            partCode = partCodeDetail.getSmallTypeCode() + partCodeDetail.getPartCodeSegment() + partCodeDetail.getPartType() + partCodeDetail.getPartCodeStatus();

            if(StringUtils.isNotNull(partCodeDetailMapper.selectPartCodeDetailByPartCode(partCode))) {
                throw new ServiceException(ErrorEnum.CODE_PART_EXIST.getName());
            }


            //若为毛胚/成品件号,查询对应的成品/毛胚件号是否已创建
            String queryPartCode = partCode;
            if(queryPartCode.startsWith("XF4") || queryPartCode.startsWith("XF7")){
                if(queryPartCode.startsWith("XF4")){
                    queryPartCode = "XF7" + queryPartCode.substring(3);
                }else if(queryPartCode.startsWith("XF7")){
                    queryPartCode = "XF4" + queryPartCode.substring(3);
                }

                if(StringUtils.isNotNull(partCodeDetailMapper.selectPartCodeDetailByPartCode(queryPartCode))) {
                    throw new ServiceException(ErrorEnum.CODE_CONVERT_EXIST.getName());
                }
            }

        } else {
            List<String> sequenceList = autoStrList(1000);

            List<PartCodeDetail> existencePartCodeDetailList = partCodeDetailMapper.selectPartCodeDetailListBySmallTypeCode(partCodeDetail.getSmallTypeCode());
            for (PartCodeDetail codeDetail : existencePartCodeDetailList) {
                if (codeDetail.getPartCode().length() >= 12) {
                    sequenceList.remove(codeDetail.getPartCode().substring(9, 12));
                }
            }

            //当生成件号大类为XF4/XF7时,新生成的件号代码在XF4/XF7对应相同小类下,序号不重复
            if(partCodeDetail.getSmallTypeCode().startsWith("XF4") || partCodeDetail.getSmallTypeCode().startsWith("XF7")){
                String smallTypeCode = partCodeDetail.getSmallTypeCode();
                if(smallTypeCode.startsWith("XF4")){
                    smallTypeCode = "XF7" + smallTypeCode.substring(3);
                }else if(smallTypeCode.startsWith("XF7")){
                    smallTypeCode = "XF4" + smallTypeCode.substring(3);
                }

                existencePartCodeDetailList = partCodeDetailMapper.selectPartCodeDetailListBySmallTypeCode(smallTypeCode);
                for (PartCodeDetail codeDetail : existencePartCodeDetailList) {
                    if (codeDetail.getPartCode().length() >= 12) {
                        sequenceList.remove(codeDetail.getPartCode().substring(9, 12));
                    }
                }
            }

            if(sequenceList.size() == 0){
                throw new ServiceException(ErrorEnum.CODE_LAZY_WEIGHT.getName());
            }

            partCode = partCodeDetail.getSmallTypeCode() + sequenceList.get(0) + partCodeDetail.getPartType() + partCodeDetail.getPartCodeStatus();
        }

        partCodeDetail.setPartCode(partCode);

        return insertPartCodeDetail(fileList, partCodeDetail);
    }

    /**
     * 拓展件号
     */
    public int expandPartCode(MultipartFile[] fileList,PartCodeDetail partCodeDetail){

        if(partCodeDetail.getSourcePartCode().length() == 14){
            partCodeDetail.setPartCode(partCodeDetail.getSourcePartCode().substring(0,13) + partCodeDetail.getPartCodeStatus());
        }else if(partCodeDetail.getSourcePartCode().length() == 12){
            //处理历史件号拓展件号
            partCodeDetail.setPartCode(partCodeDetail.getSourcePartCode() + partCodeDetail.getPartType() + partCodeDetail.getPartCodeStatus());
        }else{
            throw new ServiceException(ErrorEnum.CODE_SOURCE_NOT_SUPPORTED.getName());
        }

        //查询拓展件号代码是否已生成过
        if(StringUtils.isNotNull(partCodeDetailMapper.selectPartCodeDetailByPartCode(partCodeDetail.getPartCode()))){
            throw new ServiceException(ErrorEnum.STATUS_CODE_EXIST.getName());
        }

        return insertPartCodeDetail(fileList,partCodeDetail);
    }

    /**
     * 非标件号
     */
    public int nonStandardPartCode(MultipartFile[] fileList,PartCodeDetail partCodeDetail){
        List<String> sequenceList = autoStrList(1000);

        String sourcePartCode = partCodeDetail.getSourcePartCode();

        //查询类型为非标的件号列表
        List<PartCodeDetail> nonStandardPartCodeList = partCodeDetailMapper.selectPartCodeListBySourcePartCode(sourcePartCode,"F");

        for (PartCodeDetail codeDetail : nonStandardPartCodeList) {
            sequenceList.remove(codeDetail.getPartCode().substring(codeDetail.getPartCode().length()-3));
        }

        //处理试制件号非标和正式件号非标会生成相同件号的问题
        if(sourcePartCode.length() == 14){
            if(sourcePartCode.startsWith("S", 12)){
                nonStandardPartCodeList = partCodeDetailMapper.selectPartCodeListBySourcePartCode(sourcePartCode.replaceFirst("S","A"),"F");
            }else if(sourcePartCode.startsWith("A", 12)){
                nonStandardPartCodeList = partCodeDetailMapper.selectPartCodeListBySourcePartCode(sourcePartCode.replaceFirst("A","S"),"F");
            }
            for (PartCodeDetail codeDetail : nonStandardPartCodeList) {
                sequenceList.remove(codeDetail.getPartCode().substring(codeDetail.getPartCode().length()-3));
            }
        }


        if (sequenceList.size() == 0) {
            throw new ServiceException(ErrorEnum.CODE_LAZY_WEIGHT.getName());
        }

        //设置件号标识为非标
        partCodeDetail.setPartType("F");

        if(sourcePartCode.length() == 14){
            String partCode = sourcePartCode.substring(0,12) + partCodeDetail.getPartType() + partCodeDetail.getPartCodeStatus() + sequenceList.get(0);
            partCodeDetail.setPartCode(partCode);
        }else{
            partCodeDetail.setPartCode(sourcePartCode + partCodeDetail.getPartType() + sequenceList.get(0));
        }

        return insertPartCodeDetail(fileList,partCodeDetail);
    }

    /**
     * 零件件号
     */
    public int modulePartCode(MultipartFile[] fileList,PartCodeDetail partCodeDetail){
        List<String> sequenceList = autoStrList(100);

        //查询类型为零件的件号列表
        List<PartCodeDetail> modulePartCodeList = partCodeDetailMapper.selectPartCodeListBySourcePartCode(partCodeDetail.getSourcePartCode(),"L");

        for (PartCodeDetail codeDetail : modulePartCodeList) {
            sequenceList.remove(codeDetail.getPartCode().substring(codeDetail.getPartCode().length()-2));
        }

        if (sequenceList.size() == 0) {
            throw new ServiceException(ErrorEnum.CODE_LAZY_WEIGHT.getName());
        }

        //设置件号标识为零件
        partCodeDetail.setPartType("L");

        partCodeDetail.setPartCode(partCodeDetail.getSourcePartCode() + sequenceList.get(0));

        return insertPartCodeDetail(fileList,partCodeDetail);
    }

    /**
     * 生成毛坯/正式件号
     */
    @Transactional
    public int officialRoughcastPartCode(MultipartFile[] fileList,PartCodeDetail partCodeDetail){

        if(partCodeDetail.getSourcePartCode().startsWith("XF4")){
            partCodeDetail.setPartCode("XF7" + partCodeDetail.getSourcePartCode().substring(3));
        }else if(partCodeDetail.getSourcePartCode().startsWith("XF7")){
            partCodeDetail.setPartCode("XF4" + partCodeDetail.getSourcePartCode().substring(3));
        }else{
            throw new ServiceException(ErrorEnum.PARAMETER_ERROR.getName());
        }

        partCodeDetail.setSmallTypeCode(partCodeDetail.getPartCode().substring(0, 9));

        //查询小类代码是否存在
        if(StringUtils.isNull(partCodeSmallTypeMapper.selectPartCodeSmallTypeBySmallTypeCode(partCodeDetail.getSmallTypeCode()))){
            throw new ServiceException(ErrorEnum.SMALL_PART_OFFICIAL_EXIST.getName());
        }

        //查询正式件号是否已存在
        if(StringUtils.isNotNull(partCodeDetailMapper.selectPartCodeDetailByPartCode(partCodeDetail.getPartCode()))){
            throw new ServiceException(ErrorEnum.CODE_PART_EXIST.getName());
        }

        //设置生成的毛坯/成品件号已有对应的毛坯/成品件号
        partCodeDetail.setOfficialFlag("1");

        //更新源件号是否已出正式状态
        partCodeDetailMapper.updatePartCodeOfficial(partCodeDetail.getSourcePartCode());

        return insertPartCodeDetail(fileList,partCodeDetail);
    }

    /**
     * 试制转正
     */
    @Transactional
    public int trialProductionEmployment(MultipartFile[] fileList,PartCodeDetail partCodeDetail){

        partCodeDetail.setPartType("A");

        String partCode = partCodeDetail.getSourcePartCode().substring(0,12) + partCodeDetail.getPartType() + partCodeDetail.getPartCodeStatus();

        partCodeDetail.setPartCode(partCode);

        //查询件号是否已存在
        if(StringUtils.isNotNull(partCodeDetailMapper.selectPartCodeDetailByPartCode(partCodeDetail.getPartCode()))){
            throw new ServiceException(ErrorEnum.CODE_PART_EXIST.getName());
        }

        //更新源件号为已转正件号
        partCodeDetailMapper.updatePartCodeTrial(partCodeDetail.getSourcePartCode());

        return insertPartCodeDetail(fileList,partCodeDetail);
    }


    /**
     * 修改件号详情
     *
     * @param partCodeDetail 件号详情
     * @return 结果
     */
    @Override
    public int updatePartCodeDetail(MultipartFile[] fileList, PartCodeDetail partCodeDetail)
    {
        String baseDir = OperationConfig.getProfile()  + "/partCode/" + partCodeDetail.getPartCode();

        //上传文件
        if(fileList != null && fileList.length > 0){
            partCodeDetail.setUploadFilePath(baseDir);

            for (MultipartFile file : fileList) {
                try{
                    FileUploadUtils.uploadPartCodeFile(baseDir,file);
                }catch (IOException e){
                    throw new ServiceException(ErrorEnum.FILE_WRITE_FAIL.getName());
                }
            }
        }


        if(partCodeDetail.getRemoveFileNameList() != null && partCodeDetail.getRemoveFileNameList().size() > 0){
            //删除文件
            for(String fileName:partCodeDetail.getRemoveFileNameList()){
                new File(baseDir + "/" + fileName).delete();
            }

            //删除文件后,若件号的附件数为0,则删除件号的附件文件夹,并设置上传路径为空
            File[] finalFileList = new File(baseDir).listFiles();

            if(finalFileList == null || finalFileList.length == 0){
                new File(baseDir).delete();
                partCodeDetail.setUploadFilePath(null);
            }
        }

        partCodeDetail.setUpdateTime(DateUtils.getNowDate());
        partCodeDetail.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        return partCodeDetailMapper.updatePartCodeDetail(partCodeDetail);
    }

    /**
     * 批量删除件号详情
     *
     * @param partIds 需要删除的件号详情主键
     * @return 结果
     */
    @Override
    public int deletePartCodeDetailByPartIds(Long[] partIds)
    {
        return partCodeDetailMapper.deletePartCodeDetailByPartIds(partIds,SecurityUtils.getLoginUser().getUsername());
    }

    /**
     * 删除件号详情信息
     *
     * @param partId 件号详情主键
     * @return 结果
     */
    @Override
    public int deletePartCodeDetailByPartId(Long partId)
    {
        return partCodeDetailMapper.deletePartCodeDetailByPartId(partId,SecurityUtils.getLoginUser().getUsername());
    }


    /**
     * 件号详情导出
     *
     * @param response 响应
     * @param partId 件号详情主键
     */
    public void exportPartCodeDetailByPartId(Long partId, HttpServletResponse response)
    {

        PartCodeDetail partCodeDetail = selectPartCodeDetailByPartId(partId);

        XSSFWorkbook workbook = null;
        ByteArrayOutputStream byteArrayOut = null;

        try {
            InputStream fileInput = this.getClass().getClassLoader().getResourceAsStream("template/materialCardTemplate.xlsx");

            assert fileInput != null;

            workbook = new XSSFWorkbook(fileInput);
            XSSFSheet sheet = workbook.getSheetAt(0);

            //设置申请时间
            sheet.getRow(2).getCell(6).setCellValue("制定日期：" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(partCodeDetail.getApplicantTime()));
            //设置物料编号
            sheet.getRow(4).getCell(1).setCellValue(partCodeDetail.getPartCode());
            //设置物料名称
            sheet.getRow(5).getCell(1).setCellValue(partCodeDetail.getPartName());
            //设置物料规格
            sheet.getRow(6).getCell(1).setCellValue(partCodeDetail.getCharacteristicParameter());
            //设置物料品牌
            sheet.getRow(7).getCell(1).setCellValue(partCodeDetail.getBrand());

            if(partCodeDetail.getUploadFilePath()!=null){

                byteArrayOut = new ByteArrayOutputStream();

                //这里前段限制了只能上传一张图片,所以默认取第一张就行,方便以后拓展上传多图片情况的功能
                File[] fileList = new File(partCodeDetail.getUploadFilePath()).listFiles();
                if(fileList!=null && fileList.length > 0){
                    BufferedImage bufferImg = ImageIO.read(new File(fileList[0].getPath()));

                    ImageIO.write(bufferImg, "png", byteArrayOut);
                    // 画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）
                    XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                    // anchor主要用于设置图片的属性
                    XSSFClientAnchor anchor = new XSSFClientAnchor(5* Units.EMU_PER_PIXEL, 5*Units.EMU_PER_PIXEL, -5*Units.EMU_PER_PIXEL, -5*Units.EMU_PER_PIXEL, (short) 5, 5, (short) 9, 12);
                    // 插入图片
                    patriarch.createPicture(anchor, workbook.addPicture(byteArrayOut.toByteArray(), XSSFWorkbook.PICTURE_TYPE_PNG));
                }
            }

            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            workbook.write(response.getOutputStream());

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try{
                if(workbook!=null){
                    workbook.close();
                }
                if(byteArrayOut!=null){
                    byteArrayOut.close();
                }
            }catch(Exception e){
                e.printStackTrace();
            }
        }


    }



    /**
     * OA件号申请
     *
     * @param partCodeOaApply OA申请详情
     * @return 结果
     */
    @Override
    public AjaxResult oaApply(PartCodeOaApply partCodeOaApply) {

        //设置webDriver.get页面时,线程不阻塞
        ChromeOptions options = new ChromeOptions();
        options.setPageLoadStrategy(PageLoadStrategy.NONE);

        WebDriver webDriver = null;

        try {

            System.getProperties().setProperty("webdriver.chrome.driver", "C:\\chromedriver\\chromedriver.exe");
            webDriver = new ChromeDriver(options);
            webDriver.manage().window().maximize();

            // 显示等待控制对象,超时时间15秒
            WebDriverWait webDriverWait = new WebDriverWait(webDriver, 15);

            //根据公司设置创建流程
            String flowPath = "";
            String requestMark = "";
            if ("FWDDJ".equals(partCodeOaApply.getApplyCompany())||"FWDDL".equals(partCodeOaApply.getApplyCompany())) {
                //设定网址
                flowPath = "http://oa.xizixic.com:8080/spa/workflow/static4form/index.html?_rdm=1609306873683#/main/workflow/req?iscreate=1&workflowid=33025&isagent=0&beagenter=0&f_weaver_belongto_userid=&f_weaver_belongto_usertype=0&menuIds=1,12&menuPathIds=1,12&_key=hmbgrh";
                requestMark = "SAPWL";
            } else {
                //设定网址
                flowPath = "http://oa.xizixic.com:8080/spa/workflow/static4form/index.html?_rdm=1610583542739#/main/workflow/req?iscreate=1&workflowid=10063&isagent=0&beagenter=0&f_weaver_belongto_userid=&f_weaver_belongto_usertype=0&menuIds=1,12&menuPathIds=1,12&_key=bk90l0";
                requestMark = "PHSQ";
            }

            //打开流程页面
            webDriver.get(flowPath);

            //登录
            String errorMessage = login(webDriver,webDriverWait);
            if(errorMessage != null){
                throw new ServiceException(ErrorEnum.OA_PASSWORD_ERROR.getName() + ":" +errorMessage);
            }

            //填写表单
            try{
                writeFormAndSubmit(webDriver,webDriverWait,partCodeOaApply);
            }catch(NoSuchElementException e){
                //有些时候可能由于输入参数过快,导致单元格还没完全加载的时候，就开始录入数据，导致报错找不到单元格
                try{
                    writeFormAndSubmit(webDriver,webDriverWait,partCodeOaApply);
                }catch(NoSuchElementException noSuchElementException){
                    throw new ServiceException(ErrorEnum.OA_FILL_FORM_ERROR.getName());
                }catch (Exception ignore){
                    throw new ServiceException(ErrorEnum.OA_FORM_ERROR.getName());
                }
            }catch (Exception e){
                throw new ServiceException(ErrorEnum.OA_FORM_ERROR.getName());
            }

            try{
                //点击提交按钮
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[1]/div/div[3]/div/div[2]/div/span[1]/button")).click();

                //等待页面提交完成
                webDriverWait.until(ExpectedConditions.presenceOfElementLocated(By.id("requestmarkSpan")));
            }catch (Exception e){
                throw new ServiceException(ErrorEnum.OA_SUBMIT_ERROR.getName());
            }

            //提交成功后尝试获取流水号
            for(int times = 0;times <= 30;times++){
                try{
                    String oaSerialNumber = webDriver.findElement(By.id("requestmarkSpan")).getText();
                    if (oaSerialNumber != null && oaSerialNumber.contains(requestMark)) {
                        //修改件号状态
                        for (int i = 0; i < partCodeOaApply.getApplyParts().size(); i++) {
                            partCodeDetailMapper.updatePartCodeOaFlag(partCodeOaApply.getApplyParts().get(i),oaSerialNumber);

                            if(partCodeOaApply.getPlotFlag().equals("T") && partCodeOaApply.getPlotUploadFlag().equals("T")){
                                partCodeDetailMapper.updatePartCodePlotUploadFlag(partCodeOaApply.getApplyParts().get(i));
                            }
                        }
                        return AjaxResult.success();
                    }else{
                        Thread.sleep(1000);
                    }
                }catch (Exception e){
                    Thread.sleep(1000);
                }
            }
            throw new ServiceException(ErrorEnum.OA_SUBMIT_SUCCESS_ERROR.getName());

        }catch(Exception e){
            return AjaxResult.error(e.toString());
        }finally{
            if (webDriver != null) {
                webDriver.quit();
            }
        }
    }

    /**
     * 查询曳引轮是否符合规则
     *
     * @param partCodeDetail 曳引轮详情
     */
    @Override
    public void validationTractionWheel(PartCodeDetail partCodeDetail) {
        PartCodeSmallType partCodeSmallType = new PartCodeSmallType();
        partCodeSmallType.setMaximumPitchDiameter(partCodeDetail.getPartCodeDetailTractionWheel().getMaximumPitchDiameter());
        partCodeSmallType.setSeriesModelCode(partCodeDetail.getSeriesModelCode());
        partCodeSmallType.setSmallTypeCode(partCodeDetail.getSmallTypeCode());
        partCodeSmallType.setThickness(partCodeDetail.getPartCodeDetailTractionWheel().getThickness());

        if(partCodeSmallTypeMapper.validationTractionWheelRules(partCodeSmallType) == 0){
            throw new ServiceException(ErrorEnum.TRACTION_WHEEL_RULE_MISMATCH.getName());
        }

        if(partCodeDetail.getBusiness().equals("nonStandardPartCode")){
            partCodeDetail.setPartType("F");
        }

        if(partCodeDetail.getBusiness().equals("trialProductionEmployment")){
            partCodeDetail.setPartType("A");
        }

        PartCodeDetail checkPartCodeDetail = partCodeDetailMapper.checkCharacteristicParameterUnique(partCodeDetail);
        if(StringUtils.isNotNull(checkPartCodeDetail)){
            if(partCodeDetail.getBusiness().equals("updateForm")){
                if(!checkPartCodeDetail.getPartId().equals(partCodeDetail.getPartId())){
                    throw new ServiceException(ErrorEnum.SAME_SPECIFICATION_TRACTION_WHEEL_EXIST.getName()+ ":" + checkPartCodeDetail.getPartCode());
                }
            }else{
                throw new ServiceException(ErrorEnum.SAME_SPECIFICATION_TRACTION_WHEEL_EXIST.getName()+ ":" + checkPartCodeDetail.getPartCode());
            }
        }

    }

    //填写表单并提交表单
    private void writeFormAndSubmit(WebDriver webDriver,WebDriverWait webDriverWait,PartCodeOaApply partCodeOaApply){

        StringBuilder title = new StringBuilder("品号：");
        for (int i = 0; i < partCodeOaApply.getApplyParts().size(); i++) {
            title.append(partCodeOaApply.getApplyParts().get(i));
            if(title.length()>100){
                break;
            }
            if (partCodeOaApply.getApplyParts().size() - 1 != i) {
                title.append(",");
            }
        }

        if ("FWDDJ".equals(partCodeOaApply.getApplyCompany())||"FWDDL".equals(partCodeOaApply.getApplyCompany())) {

            //等待页面刷新
            webDriverWait.until(ExpectedConditions.elementToBeClickable(By.id("field27741")));

            //设置标题
            WebElement titleElement = webDriver.findElement(By.id("field27741"));
            if(titleElement.getText() == null || titleElement.getText().isEmpty()){
                titleElement.sendKeys(title);
            }

            //选择申请单位
            WebElement companyElement = webDriver.findElement(By.id("field114013"));
            if("FWDDJ".equals(partCodeOaApply.getApplyCompany())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[7]/td[4]/div/div/div/label[1]/span[1]")).click();
                if(companyElement.getText() == null || companyElement.getText().isEmpty()){
                    companyElement.sendKeys("2002");
                }
            }else if("FWDDL".equals(partCodeOaApply.getApplyCompany())){
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[7]/td[4]/div/div/div/label[2]/span[1]")).click();
                if(companyElement.getText() == null || companyElement.getText().isEmpty()){
                    companyElement.sendKeys("2020");
                }
            }

            //物料状态
            if ("A".equals(partCodeOaApply.getPartCodeType())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[9]/td[4]/div/div/div/label[1]/span[1]")).click();
            } else if ("S".equals(partCodeOaApply.getPartCodeType())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[9]/td[4]/div/div/div/label[2]/span[1]")).click();
            }

            //是否包含参考零件
            if ("T".equals(partCodeOaApply.getIncludeParts())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[9]/td[8]/div/div/div/label[1]/span[1]")).click();
            } else if ("F".equals(partCodeOaApply.getIncludeParts())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[9]/td[8]/div/div/div/label[2]/span[1]")).click();
            }

            //物料分类
            if ("1".equals(partCodeOaApply.getPartCodeItem())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[10]/td[4]/div/div/div/label[1]/span[1]")).click();
            } else if ("2".equals(partCodeOaApply.getPartCodeItem())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[10]/td[4]/div/div/div/label[2]/span[1]")).click();
            } else if ("3".equals(partCodeOaApply.getPartCodeItem())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[10]/td[4]/div/div/div/label[3]/span[1]")).click();
            } else if ("4".equals(partCodeOaApply.getPartCodeItem())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[10]/td[4]/div/div/div/label[4]/span[1]")).click();
            }

            //采购件需要出图
            if ("T".equals(partCodeOaApply.getPlotFlag())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[10]/td[8]/div/div/div/label[1]/span[1]")).click();
            } else if ("F".equals(partCodeOaApply.getPlotFlag())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[10]/td[8]/div/div/div/label[2]/span[1]")).click();
            }

            WebElement we = webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[19]/td[2]/div/div/div[1]/div/div[2]/table/tbody/tr[2]/td[23]/div/div/i[1]"));

            for (int i = 1; i < partCodeOaApply.getApplyParts().size(); i++) {
                we.click();
            }

            for (int i = 0; i < partCodeOaApply.getApplyParts().size(); i++) {
                PartCodeDetail partCodeDetail;
                try{
                    partCodeDetail = partCodeDetailMapper.selectPartCodeDetailByPartCode(partCodeOaApply.getApplyParts().get(i));
                }catch (Exception e){
                    throw new ServiceException("件号重复,请联系系统管理员");
                }

                //物料编号
                WebElement partCodeElement = webDriver.findElement((By.name("field27749_" + i)));
                if(partCodeElement.getText() == null || partCodeElement.getText().isEmpty()){
                    partCodeElement.sendKeys(partCodeDetail.getPartCode());
                }

                StringBuilder materialDescribeBuilder = new StringBuilder();
                materialDescribeBuilder.append(partCodeDetail.getPartName()).append("&&").append(partCodeDetail.getCharacteristicParameter());

                //物料长文本
                String materialText = null;
                //物料描述
                String materialDescribe;

                if(materialDescribeBuilder.length() >= 40){
                    materialText = materialDescribeBuilder.toString();
                    materialDescribe = materialDescribeBuilder.substring(0,39) + "?";
                }else{
                    materialDescribe = materialDescribeBuilder.toString();
                }

                WebElement materialDescribeElement = webDriver.findElement((By.name("field37013_" + i)));
                if(materialDescribeElement.getText() == null || materialDescribeElement.getText().isEmpty()){
                    materialDescribeElement.sendKeys(materialDescribe);
                }

                //物料类型
                WebElement materialTypeCodeElement = webDriver.findElement((By.name("field27755_" + i)));
                if(materialTypeCodeElement.getText() == null || materialTypeCodeElement.getText().isEmpty()){
                    materialTypeCodeElement.sendKeys(partCodeDetail.getMaterialTypeCode());
                }

                //物料组
                WebElement materialGroupCodeElement = webDriver.findElement((By.name("field27756_" + i)));
                if(materialGroupCodeElement.getText() == null || materialGroupCodeElement.getText().isEmpty()){
                    materialGroupCodeElement.sendKeys(partCodeDetail.getMaterialGroupCode());
                }

                //外部物料组
                WebElement outsideMaterialGroupCodeElement = webDriver.findElement((By.name("field27757_" + i)));
                if(outsideMaterialGroupCodeElement.getText() == null || outsideMaterialGroupCodeElement.getText().isEmpty()){
                    outsideMaterialGroupCodeElement.sendKeys(partCodeDetail.getOutsideMaterialGroupCode());
                }

                //基本计量单位
                WebElement basicUnitElement = webDriver.findElement((By.name("field27751_" + i)));
                if(basicUnitElement.getText() == null || basicUnitElement.getText().isEmpty()){
                    basicUnitElement.sendKeys(partCodeDetail.getBasicUnit());
                }

                //参考的零件
                if (partCodeDetail.getReferencePart() != null && !partCodeDetail.getReferencePart().isEmpty()) {
                    WebElement referencePartElement = webDriver.findElement((By.name("field27758_" + i)));
                    if(referencePartElement.getText() == null || referencePartElement.getText().isEmpty()){
                        referencePartElement.sendKeys(partCodeDetail.getReferencePart());
                    }
                }

                //旧物料号
                if (partCodeDetail.getOldMaterialCode() != null && !partCodeDetail.getOldMaterialCode().isEmpty()) {
                    WebElement oldMaterialCodeElement = webDriver.findElement((By.name("field27765_" + i)));
                    if(oldMaterialCodeElement.getText() == null || oldMaterialCodeElement.getText().isEmpty()){
                        oldMaterialCodeElement.sendKeys(partCodeDetail.getOldMaterialCode());
                    }
                }

                //系列大类
                if (partCodeDetail.getSeriesBigTypeCode() != null && !partCodeDetail.getSeriesBigTypeCode().isEmpty()) {
                    WebElement seriesBigTypeCodeElement = webDriver.findElement((By.name("field27762_" + i)));
                    if(seriesBigTypeCodeElement.getText() == null || seriesBigTypeCodeElement.getText().isEmpty()){
                        seriesBigTypeCodeElement.sendKeys(partCodeDetail.getSeriesBigTypeCode());
                    }
                }

                //系列型号
                if (partCodeDetail.getSeriesModelCode() != null && !partCodeDetail.getSeriesModelCode().isEmpty()) {
                    WebElement seriesModelCodeElement = webDriver.findElement((By.name("field27763_" + i)));
                    if(seriesModelCodeElement.getText() == null || seriesModelCodeElement.getText().isEmpty()){
                        seriesModelCodeElement.sendKeys(partCodeDetail.getSeriesModelCode());
                    }
                }

                //标准配置机型(能效)
                if (partCodeDetail.getSeriesTypeCode() != null && !partCodeDetail.getSeriesTypeCode().isEmpty()) {
                    WebElement seriesTypeCodeElement = webDriver.findElement((By.name("field27764_" + i)));
                    if(seriesTypeCodeElement.getText() == null || seriesTypeCodeElement.getText().isEmpty()){
                        seriesTypeCodeElement.sendKeys(partCodeDetail.getSeriesTypeCode());
                    }
                }

                //毛重
                WebElement roughWeightElement = webDriver.findElement((By.name("field27759_" + i)));
                if(roughWeightElement.getText() == null || roughWeightElement.getText().isEmpty()){
                    roughWeightElement.sendKeys(partCodeDetail.getRoughWeight());
                }

                //净重
                WebElement netWeightElement = webDriver.findElement((By.name("field27760_" + i)));
                if(netWeightElement.getText() == null || netWeightElement.getText().isEmpty()){
                    netWeightElement.sendKeys(partCodeDetail.getNetWeight());
                }

                //重量单位
                WebElement weightUnitElement = webDriver.findElement((By.name("field27761_" + i)));
                if(weightUnitElement.getText() == null || weightUnitElement.getText().isEmpty()){
                    weightUnitElement.sendKeys(partCodeDetail.getWeightUnit());
                }

                //物料长文本
                if (materialText != null) {
                    WebElement materialTextElement = webDriver.findElement((By.name("field37015_" + i)));
                    if(materialTextElement.getText() == null || materialTextElement.getText().isEmpty()){
                        materialTextElement.sendKeys(materialText);
                    }
                }

                //分母
                if (partCodeDetail.getDenominator() != null && !partCodeDetail.getDenominator().isEmpty()) {
                    WebElement denominatorElement = webDriver.findElement((By.name("field27753_" + i)));
                    if(denominatorElement.getText() == null || denominatorElement.getText().isEmpty()){
                        denominatorElement.sendKeys(partCodeDetail.getDenominator());
                    }
                }

                //分子
                if (partCodeDetail.getMolecule() != null && !partCodeDetail.getMolecule().isEmpty()) {
                    WebElement moleculeElement = webDriver.findElement((By.name("field27754_" + i)));
                    if(moleculeElement.getText() == null || moleculeElement.getText().isEmpty()){
                        moleculeElement.sendKeys(partCodeDetail.getMolecule());
                    }
                }

                //可选计量单位
                if (partCodeDetail.getSelectUnit() != null && !partCodeDetail.getSelectUnit().isEmpty()) {
                    WebElement selectUnitElement = webDriver.findElement((By.name("field27752_" + i)));
                    if(selectUnitElement.getText() == null || selectUnitElement.getText().isEmpty()){
                        selectUnitElement.sendKeys(partCodeDetail.getSelectUnit());
                    }
                }

                //备注
                if (partCodeDetail.getPartRemark() != null && !partCodeDetail.getPartRemark().isEmpty()) {
                    WebElement partRemarkElement = webDriver.findElement((By.name("field37014_" + i)));
                    if(partRemarkElement.getText() == null || partRemarkElement.getText().isEmpty()){
                        partRemarkElement.sendKeys(partCodeDetail.getPartRemark());
                    }
                }

                //虚拟件
                if (partCodeDetail.getVirtualFlag().equals("0")) {
                    webDriver.findElement((By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[19]/td[2]/div/div/div[1]/div/div[2]/table/tbody/tr[" + (i % 10 + 4) + "]/td[22]/div/div/div/label[2]/span[1]"))).click();
                } else if (partCodeDetail.getVirtualFlag().equals("1")){
                    webDriver.findElement((By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[19]/td[2]/div/div/div[1]/div/div[2]/table/tbody/tr[" + (i % 10 + 4) + "]/td[22]/div/div/div/label[1]/span[1]"))).click();
                }

                //自制件
                if (partCodeDetail.getSelfFlag().equals("0")) {
                    webDriver.findElement((By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[19]/td[2]/div/div/div[1]/div/div[2]/table/tbody/tr[" + (i % 10 + 4) + "]/td[23]/div/div/div/label[2]/span[1]"))).click();
                } else if (partCodeDetail.getSelfFlag().equals("1")){
                    webDriver.findElement((By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[19]/td[2]/div/div/div[1]/div/div[2]/table/tbody/tr[" + (i % 10 + 4) + "]/td[23]/div/div/div/label[1]/span[1]"))).click();
                }
            }

        }else {

            //等待页面刷新
            webDriverWait.until(ExpectedConditions.elementToBeClickable(By.id("field24429")));

            //设置标题
            WebElement titleElement = webDriver.findElement((By.id("field24429")));
            if(titleElement.getText() == null || titleElement.getText().isEmpty()){
                titleElement.sendKeys(title);
            }

            if ("A".equals(partCodeOaApply.getPartCodeType())) {
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[8]/td[4]/div/div/div/label[1]/span[1]")).click();
            } else if ("S".equals(partCodeOaApply.getPartCodeType())){
                webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[8]/td[4]/div/div/div/label[2]/span[1]")).click();
            }

            WebElement we = webDriver.findElement(By.xpath("/html/body/div[1]/div/div/div[2]/div[1]/div[2]/div[1]/div/div/div[2]/div[1]/div/table/tbody/tr[13]/td[2]/div/div/div[1]/div[1]/table/tbody/tr[2]/td[3]/div/div/i[1]"));

            for (int i = 1; i < partCodeOaApply.getApplyParts().size(); i++) {
                we.click();
            }

            for (int i = 0; i < partCodeOaApply.getApplyParts().size(); i++) {

                PartCodeDetail partCodeDetail = partCodeDetailMapper.selectPartCodeDetailByPartCode(partCodeOaApply.getApplyParts().get(i));

                //输入件号
                WebElement partCodeElement = webDriver.findElement((By.name("field24524_" + i)));
                if(partCodeElement.getText() == null || partCodeElement.getText().isEmpty()){
                    partCodeElement.sendKeys(partCodeDetail.getPartCode());
                }

                //输入品名
                WebElement partNameElement = webDriver.findElement((By.name("field24525_" + i)));
                if(partNameElement.getText() == null || partNameElement.getText().isEmpty()){
                    partNameElement.sendKeys(partCodeDetail.getPartName());
                }

                //输入规格
                WebElement characteristicParameterElement = webDriver.findElement((By.name("field24526_" + i)));
                if(characteristicParameterElement.getText() == null || characteristicParameterElement.getText().isEmpty()){
                    characteristicParameterElement.sendKeys(partCodeDetail.getCharacteristicParameter());
                }

                //输入品牌
                if(partCodeDetail.getMaterials()!=null&&!partCodeDetail.getMaterials().isEmpty()){
                    WebElement materialsElement = webDriver.findElement((By.name("field24527_" + i)));
                    if(materialsElement.getText() == null || materialsElement.getText().isEmpty()){
                        materialsElement.sendKeys(partCodeDetail.getMaterials());
                    }
                }

            }
        }
    }



    private String login(WebDriver webDriver,WebDriverWait webDriverWait){
        //等待账号登录页面可点击登录
        webDriverWait.until(ExpectedConditions.elementToBeClickable(By.name("loginid")));

        //输入用户名 密码
        webDriver.findElement(By.name("loginid")).sendKeys(SecurityUtils.getUsername());
        webDriver.findElement(By.name("userpassword")).sendKeys(SecurityUtils.getLoginUser().getUser().getOaPassword());
        //点击登录按钮
        webDriver.findElement(By.id("submit")).click();

        try {
            //尝试获取刷新页面按钮,获取到则登录成功,否则登录失败
            webDriverWait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[10]/div/div[2]/div/div[1]/div/div/div[2]/button[1]")));
        } catch (Exception e) {
            //找不到刷新按钮,说明登录失败,再次点击登录,获取登录失败错误信息
            //点击登录按钮
            webDriver.findElement(By.id("submit")).click();

            WebElement ele = webDriverWait.until(ExpectedConditions.elementToBeClickable(By.xpath("/html/body/div[9]/div/span/div/div/div/span/span")));
            return ele.getText();
        }
        //点击刷新按钮
        webDriver.findElement(By.xpath("/html/body/div[10]/div/div[2]/div/div[1]/div/div/div[2]/button[1]")).click();

        return null;
    }


    public List<String> autoStrList(int size) {
        List<String> strList = new ArrayList<>();

        if(size<=100){
            for (int i = 1; i < size; i++) {
                if (i < 10) {
                    strList.add("0" + i);
                } else {
                    strList.add(String.valueOf(i));
                }
            }
        }else{
            for (int i = 1; i < size; i++) {
                if (i < 10) {
                    strList.add("00" + i);
                } else if (i < 100) {
                    strList.add("0" + i);
                } else {
                    strList.add(String.valueOf(i));
                }
            }
        }

        return strList;
    }
}
