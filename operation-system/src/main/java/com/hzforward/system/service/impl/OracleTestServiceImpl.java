package com.hzforward.system.service.impl;

import com.hzforward.common.annotation.DataSource;
import com.hzforward.common.enums.DataSourceType;
import com.hzforward.framework.datasource.DynamicDataSourceContextHolder;
import com.hzforward.system.service.IOracleTestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Oracle数据库测试服务实现类
 * 演示如何使用Oracle数据源进行数据库操作
 *
 * <AUTHOR>
 */
@Service
public class OracleTestServiceImpl implements IOracleTestService {

    private static final Logger log = LoggerFactory.getLogger(OracleTestServiceImpl.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 使用注解方式切换到Oracle数据源
     * 测试Oracle数据库连接
     *
     * @return 测试结果
     */
    @DataSource(DataSourceType.SLAVE9)
    public String testOracleConnectionWithAnnotation() {
        try {
            // 执行简单的查询测试连接
            String result = jdbcTemplate.queryForObject("SELECT 'Oracle连接成功' FROM DUAL", String.class);
            log.info("Oracle数据库连接测试成功: {}", result);
            return result;
        } catch (Exception e) {
            log.error("Oracle数据库连接测试失败", e);
            return "Oracle连接失败: " + e.getMessage();
        }
    }

    /**
     * 使用手动方式切换到Oracle数据源
     * 获取Oracle数据库时间
     *
     * @return 数据库时间
     */
    public String getOracleDatabaseTime() {
        try {
            // 手动切换到Oracle数据源
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE9.name());
            
            String currentTime = jdbcTemplate.queryForObject(
                "SELECT TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') FROM DUAL", 
                String.class
            );
            
            log.info("Oracle数据库当前时间: {}", currentTime);
            return currentTime;
            
        } catch (Exception e) {
            log.error("获取Oracle数据库时间失败", e);
            return "获取时间失败: " + e.getMessage();
        } finally {
            // 清除数据源设置
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }

    /**
     * 查询Oracle数据库版本信息
     *
     * @return 版本信息
     */
    @DataSource(DataSourceType.SLAVE9)
    public String getOracleVersion() {
        try {
            String version = jdbcTemplate.queryForObject(
                "SELECT BANNER FROM V$VERSION WHERE ROWNUM = 1", 
                String.class
            );
            log.info("Oracle数据库版本: {}", version);
            return version;
        } catch (Exception e) {
            log.error("获取Oracle数据库版本失败", e);
            return "获取版本失败: " + e.getMessage();
        }
    }

    /**
     * 查询Oracle数据库中的表空间信息
     *
     * @return 表空间列表
     */
    @DataSource(DataSourceType.SLAVE9)
    public List<Map<String, Object>> getTablespaces() {
        try {
            List<Map<String, Object>> tablespaces = jdbcTemplate.queryForList(
                "SELECT TABLESPACE_NAME, STATUS, CONTENTS FROM DBA_TABLESPACES ORDER BY TABLESPACE_NAME"
            );
            log.info("查询到 {} 个表空间", tablespaces.size());
            return tablespaces;
        } catch (Exception e) {
            log.error("查询表空间信息失败", e);
            throw new RuntimeException("查询表空间失败: " + e.getMessage());
        }
    }

    /**
     * 查询当前用户的表列表
     *
     * @return 表列表
     */
    @DataSource(DataSourceType.SLAVE9)
    public List<Map<String, Object>> getUserTables() {
        try {
            List<Map<String, Object>> tables = jdbcTemplate.queryForList(
                "SELECT TABLE_NAME, NUM_ROWS, LAST_ANALYZED FROM USER_TABLES ORDER BY TABLE_NAME"
            );
            log.info("当前用户拥有 {} 个表", tables.size());
            return tables;
        } catch (Exception e) {
            log.error("查询用户表列表失败", e);
            throw new RuntimeException("查询用户表失败: " + e.getMessage());
        }
    }

    /**
     * 执行自定义SQL查询
     *
     * @param sql SQL语句
     * @return 查询结果
     */
    @DataSource(DataSourceType.SLAVE9)
    public List<Map<String, Object>> executeCustomQuery(String sql) {
        try {
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
            log.info("自定义查询执行成功，返回 {} 条记录", result.size());
            return result;
        } catch (Exception e) {
            log.error("执行自定义查询失败: {}", sql, e);
            throw new RuntimeException("查询执行失败: " + e.getMessage());
        }
    }

    /**
     * 测试Oracle数据库连接池状态
     *
     * @return 连接池信息
     */
    @DataSource(DataSourceType.SLAVE9)
    public Map<String, Object> getConnectionPoolStatus() {
        try {
            // 查询当前会话信息
            Map<String, Object> sessionInfo = jdbcTemplate.queryForMap(
                "SELECT SID, SERIAL#, USERNAME, STATUS, MACHINE, PROGRAM FROM V$SESSION WHERE SID = (SELECT DISTINCT SID FROM V$MYSTAT WHERE ROWNUM = 1)"
            );
            log.info("Oracle连接池状态查询成功");
            return sessionInfo;
        } catch (Exception e) {
            log.error("查询Oracle连接池状态失败", e);
            throw new RuntimeException("查询连接池状态失败: " + e.getMessage());
        }
    }
}
