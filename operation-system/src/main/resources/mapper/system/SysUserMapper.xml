<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.system.mapper.SysUserMapper">

	<resultMap type="SysUser" id="SysUserResult">
		<id     property="userId"       column="user_id"      />
		<result property="deptId"       column="dept_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="userType"     column="user_type"        />
		<result property="email"        column="email"        />
		<result property="phoneNumber"  column="phone_number"  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="status"       column="status"       />
		<result property="delFlag"      column="del_flag"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="jobNo"        column="job_no"       />
		<collection  property="roles"   javaType="java.util.List" resultMap="RoleResult" />
	</resultMap>

	<resultMap id="RoleResult" type="SysRole">
		<id     property="roleId"       column="role_id"        />
		<result property="roleName"     column="role_name"      />
		<result property="roleKey"      column="role_key"       />
		<result property="roleSort"     column="role_sort"      />
	</resultMap>

	<select id="selectAllocatedList" parameterType="SysUser" resultMap="SysUserResult">
	    select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phone_number, u.status, u.create_time
	    from sys_user u
			 left join sys_user_role ur on u.user_id = ur.user_id
			 left join sys_role r on r.role_id = ur.role_id
	    where u.del_flag = '0' and r.role_id = #{roleId}
	    <if test="userName != null and userName != ''">
			AND (u.user_name like concat('%', #{userName}, '%') or u.nick_name like concat('%', #{userName}, '%'))
		</if>
		<if test="userType != null and userType != ''">
			AND u.user_type = #{userType}
		</if>
	</select>

	<select id="selectUnallocatedList" parameterType="SysUser" resultMap="SysUserResult">
		select distinct u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.phone_number, u.status, u.create_time
		from sys_user u
		left join sys_user_role ur on u.user_id = ur.user_id
		left join sys_role r on r.role_id = ur.role_id
		where u.del_flag = '0' and (r.role_id != #{roleId} or r.role_id IS NULL) and u.status = '0'
		and u.user_id not in (select u.user_id from sys_user u inner join sys_user_role ur on u.user_id = ur.user_id and ur.role_id = #{roleId})
		<if test="userName != null and userName != ''">
			AND (u.user_name like concat('%', #{userName}, '%') or u.nick_name like concat('%', #{userName}, '%'))
		</if>
		<if test="userType != null and userType != ''">
			AND u.user_type = #{userType}
		</if>
	</select>

	<select id="selectAllocatedListByRollKey" parameterType="SysUser" resultMap="SysUserResult">
		select distinct u.user_id, u.user_name, u.nick_name, U.user_type
		from sys_user u
			left join sys_user_role ur on u.user_id = ur.user_id
			left join sys_role r on r.role_id = ur.role_id
			where u.del_flag = '0' and u.status = '0' and r.role_key = #{roleKey} and r.del_flag = '0'
	</select>

</mapper>
