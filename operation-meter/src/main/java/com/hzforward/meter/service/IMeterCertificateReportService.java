package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterCertificateReport;

import javax.servlet.http.HttpServletResponse;

/**
 * 量具证书报告Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterCertificateReportService 
{
    /**
     * 查询量具证书报告
     * 
     * @param id 量具证书报告主键
     * @return 量具证书报告
     */
    public MeterCertificateReport selectMeterCertificateReportById(Long id);

    /**
     * 查询量具证书报告列表
     * 
     * @param meterCertificateReport 量具证书报告
     * @return 量具证书报告集合
     */
    public List<MeterCertificateReport> selectMeterCertificateReportList(MeterCertificateReport meterCertificateReport);

    /**
     * 新增量具证书报告
     * 
     * @param meterCertificateReport 量具证书报告
     * @return 结果
     */
    public int insertMeterCertificateReport(MeterCertificateReport meterCertificateReport);

    /**
     * 修改量具证书报告
     * 
     * @param meterCertificateReport 量具证书报告
     * @return 结果
     */
    public int updateMeterCertificateReport(MeterCertificateReport meterCertificateReport);

    /**
     * 批量删除量具证书报告
     * 
     * @param ids 需要删除的量具证书报告主键集合
     * @return 结果
     */
    public int deleteMeterCertificateReportByIds(List<Long> ids);

    /**
     * 删除量具证书报告信息
     * 
     * @param id 量具证书报告主键
     * @return 结果
     */
    public int deleteMeterCertificateReportById(Long id);

    /**
     * 下载文件
     * @param response
     * @param id
     */
    void downLoad(HttpServletResponse response, Long id);
}
