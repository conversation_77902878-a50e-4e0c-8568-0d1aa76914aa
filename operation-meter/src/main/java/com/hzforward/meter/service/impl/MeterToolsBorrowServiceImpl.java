package com.hzforward.meter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.core.domain.entity.SysDept;
import com.hzforward.common.core.domain.entity.SysUser;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.meter.domain.MeterTool;
import com.hzforward.meter.domain.MeterToolsBorrow;
import com.hzforward.meter.domain.req.MeterToolsBorrowReq;
import com.hzforward.meter.domain.res.MeterToolsBorrowRes;
import com.hzforward.meter.mapper.MeterToolMapper;
import com.hzforward.meter.mapper.MeterToolsBorrowMapper;
import com.hzforward.meter.service.IMeterToolsBorrowService;
import com.hzforward.system.mapper.SysDeptMapper;
import com.hzforward.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * 量具借用报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-15
 */
@Service
@RequiredArgsConstructor
public class MeterToolsBorrowServiceImpl implements IMeterToolsBorrowService {

    private final MeterToolsBorrowMapper meterToolsBorrowMapper;
    private final MeterToolMapper meterToolMapper;
    private final SysDeptMapper sysDeptMapper;
    private final ISysUserService sysUserService;

    /**
     * 查询量具借用报告
     *
     * @param id 量具借用报告主键
     * @return 量具借用报告
     */
    @Override
    public MeterToolsBorrow selectMeterToolsBorrowById(Long id) {
        return meterToolsBorrowMapper.selectById(id);
    }

    /**
     * 查询量具借用报告列表
     *
     * @param meterToolsBorrow 量具借用报告
     * @return 量具借用报告
     */
    @Override
    public List<MeterToolsBorrow> selectMeterToolsBorrowList(MeterToolsBorrow meterToolsBorrow) {
        return meterToolsBorrowMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<MeterToolsBorrow>()
                .eq(MeterToolsBorrow::getCode, meterToolsBorrow.getCode())
                .eq(MeterToolsBorrow::getName, meterToolsBorrow.getName())
                .eq(MeterToolsBorrow::getLendingStatus, meterToolsBorrow.getLendingStatus())
                .eq(MeterToolsBorrow::getBorrower, meterToolsBorrow.getBorrower())
                .eq(MeterToolsBorrow::getReturnedBy, meterToolsBorrow.getReturnedBy())
                .eq(MeterToolsBorrow::getBorrowedDept, meterToolsBorrow.getBorrowedDept())
                .eq(MeterToolsBorrow::getRemark, meterToolsBorrow.getRemark())
                .between(MeterToolsBorrow::getBorrowedTime, StringUtils.isNotNull(meterToolsBorrow.getBorrowedTime()) ? DateUtils.getTodayStartTime(meterToolsBorrow.getBorrowedTime()) : null,
                        StringUtils.isNotNull(meterToolsBorrow.getBorrowedTime()) ? DateUtils.getTodayLastTime(meterToolsBorrow.getBorrowedTime()) : null)
                .between(MeterToolsBorrow::getReturnTime, StringUtils.isNotNull(meterToolsBorrow.getReturnTime()) ? DateUtils.getTodayStartTime(meterToolsBorrow.getReturnTime()) : null,
                        StringUtils.isNotNull(meterToolsBorrow.getReturnTime()) ? DateUtils.getTodayLastTime(meterToolsBorrow.getReturnTime()) : null)
                .eq(MeterToolsBorrow::getSecretKey, meterToolsBorrow.getSecretKey())
                .orderByDesc(MeterToolsBorrow::getCreateTime)
        );
    }

    /**
     * 新增量具借用报告
     *
     * @param meterToolsBorrow 量具借用报告
     * @return 结果
     */
    @Override
    public int insertMeterToolsBorrow(MeterToolsBorrow meterToolsBorrow) {
        return meterToolsBorrowMapper.insert(meterToolsBorrow);
    }

    /**
     * 修改量具借用报告
     *
     * @param meterToolsBorrow 量具借用报告
     * @return 结果
     */
    @Override
    public int updateMeterToolsBorrow(MeterToolsBorrow meterToolsBorrow) {
        return meterToolsBorrowMapper.updateById(meterToolsBorrow);
    }

    /**
     * 删除量具借用报告
     *
     * @param ids 需要删除的量具借用报告主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMeterToolsBorrowByIds(List<Long> ids) {
        return meterToolsBorrowMapper.deleteBatchIds(ids);
    }

    @Override
    public MeterToolsBorrowRes getMeterInfo(String code, String jobNo) {
        MeterTool meterInfo = meterToolMapper.getMeterInfo(code);
        if (meterInfo == null) {
            return null;
        }
        MeterToolsBorrowRes meterToolsBorrowRes = new MeterToolsBorrowRes();
        meterToolsBorrowRes.setName(meterInfo.getName());
        meterToolsBorrowRes.setCode(meterInfo.getCode());
        //获取保管人相关信息
        SysUser user = sysUserService.selectUserListByJobNo(meterInfo.getSafeguardPerson());
        meterToolsBorrowRes.setSafeguardPerson(user.getNickName());
        //获取保管人所属部门
        SysDept sysDept = sysDeptMapper.selectOne(
                new LambdaQueryWrapper<SysDept>()
                        .eq(SysDept::getDeptId, user.getDeptId())
        );
        meterToolsBorrowRes.setCustodyDept(sysDept.getDeptName());
        //查询借阅记录数据
        List<MeterToolsBorrow> meterToolsBorrows = meterToolsBorrowMapper.selectList(new LambdaQueryWrapper<MeterToolsBorrow>()
                .eq(MeterToolsBorrow::getCode, code)
                .orderByDesc(MeterToolsBorrow::getCreateTime));
        //借用记录里无数据或者最新的借用记录状态为归还
        if (meterToolsBorrows.size() == 0 || meterToolsBorrows.get(0).getLendingStatus().equals(1)) {
            meterToolsBorrowRes.setLendingStatus(1);
        }else if (meterToolsBorrows.get(0).getLendingStatus().equals(0)){
            MeterToolsBorrow latestMeterToolsBorrow = meterToolsBorrows.get(0);
            BeanUtils.copyProperties(latestMeterToolsBorrow,meterToolsBorrowRes);
        }
        return meterToolsBorrowRes;
    }

    /**
     * 处理移动端量具借阅请求
     */
    @Override
    public int borrowToolSubmit(MeterToolsBorrowReq meterToolsBorrowReq)
    {
        MeterToolsBorrow meterToolsBorrow = new MeterToolsBorrow();
        BeanUtils.copyProperties(meterToolsBorrowReq,meterToolsBorrow);
        SysUser borrowUser = sysUserService.selectDingUserByJobNo(meterToolsBorrowReq.getUserName());
        SysDept borrowDept = sysDeptMapper.selectOne(new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDeptId, borrowUser.getDeptId()));
        Date borrowDate = new Date();
        meterToolsBorrow.setBorrowedTime(borrowDate);
        meterToolsBorrow.setBorrower(borrowUser.getNickName());
        meterToolsBorrow.setBorrowedDept(borrowDept.getDeptName());
        meterToolsBorrow.setSecretKey(generateCaptcha(6));
        meterToolsBorrow.setLendingStatus(0);
        meterToolsBorrowMapper.insert(meterToolsBorrow);
        return 1;
    }


    /**
     * 处理移动端量具归还请求
     */
    @Override
    public int returnSubmit(MeterToolsBorrowReq meterToolsBorrowReq)
    {
        List<MeterToolsBorrow> meterToolsBorrows = meterToolsBorrowMapper.selectList(new LambdaQueryWrapper<MeterToolsBorrow>()
                .eq(MeterToolsBorrow::getCode, meterToolsBorrowReq.getCode())
                .eq(MeterToolsBorrow::getLendingStatus,0)
                .orderByDesc(MeterToolsBorrow::getCreateTime));
        MeterToolsBorrow meterToolsBorrow = meterToolsBorrows.get(0);
        if (!meterToolsBorrow.getSecretKey().equals(meterToolsBorrowReq.getSecretKey())){
            return 0;
        }
        SysUser borrowUser = sysUserService.selectDingUserByJobNo(meterToolsBorrowReq.getUserName());
        Date returnDate = new Date();
        meterToolsBorrow.setReturnTime(returnDate);
        meterToolsBorrow.setReturnedBy(borrowUser.getNickName());
        meterToolsBorrow.setLendingStatus(1);
        meterToolsBorrowMapper.updateById(meterToolsBorrow);
        return 1;
    }

    /**
     * 更新所有未归还的量具验证码
     */
    @Override
    public int updateSecretKey()
    {
        List<MeterToolsBorrow> meterToolsBorrows = meterToolsBorrowMapper.selectList(new LambdaQueryWrapper<MeterToolsBorrow>()
                .eq(MeterToolsBorrow::getLendingStatus, 0));
        for (MeterToolsBorrow meterToolsBorrow : meterToolsBorrows){
            meterToolsBorrow.setSecretKey(generateCaptcha(6));
            meterToolsBorrowMapper.updateById(meterToolsBorrow);
        }
        return 1;
    }

    /**
     * 生成生成随机验证码
     * @param length
     * @return
     */
    public static String generateCaptcha(int length)
    {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder captcha = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length());
            captcha.append(chars.charAt(index));
        }
        return captcha.toString();
    }

}
