package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterInspection;

/**
 * 量具点检内容Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-20
 */
public interface IMeterInspectionService 
{
    /**
     * 查询量具点检内容
     * 
     * @param inspectionId 量具点检内容主键
     * @return 量具点检内容
     */
    public MeterInspection selectMeterInspectionByInspectionId(Long inspectionId);

    /**
     * 查询量具点检内容列表
     * 
     * @param meterInspection 量具点检内容
     * @return 量具点检内容集合
     */
    public List<MeterInspection> selectMeterInspectionList(MeterInspection meterInspection);

    /**
     * 新增量具点检内容
     * 
     * @param meterInspection 量具点检内容
     * @return 结果
     */
    public int insertMeterInspection(MeterInspection meterInspection);

    /**
     * 修改量具点检内容
     * 
     * @param meterInspection 量具点检内容
     * @return 结果
     */
    public int updateMeterInspection(MeterInspection meterInspection);

    /**
     * 删除量具点检内容
     * 
     * @param inspectionIds 需要删除的量具点检内容主键集合
     * @return 结果
     */
    public int deleteMeterInspectionByInspectionIds(List<Long> inspectionIds);

}
