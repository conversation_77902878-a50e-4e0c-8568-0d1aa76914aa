package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.common.core.domain.AjaxResult;
import com.hzforward.meter.domain.MeterInspectionReport;
import com.hzforward.meter.domain.MeterInspectionReportDetail;
import com.hzforward.meter.domain.req.MeterDocimasyForm;
import com.hzforward.meter.domain.req.MeterInspectionListReq;

/**
 * 量具点检报告Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-22
 */
public interface IMeterInspectionReportService 
{
    /**
     * 查询量具点检报告
     * 
     * @param reportId 量具点检报告主键
     * @return 量具点检报告
     */
    public MeterInspectionReport selectMeterInspectionReportByReportId(Long reportId);

    /**
     * 查询量具点检报告列表
     * 
     * @param
     * @return 量具点检报告集合
     */
    public List<MeterInspectionReportDetail> selectMeterInspectionReportList(MeterInspectionListReq meterInspectionListReq);

    /**
     * 查询量具点检项目
     * @param meterInspectionReport
     * @return
     */
    public MeterInspectionReportDetail selectMeterInspectionReportDetilList(MeterInspectionReport meterInspectionReport);
    /**
     * 查询量具点检报告
     *
     * @param meterInspectionReport 量具点检报告
     * @return 量具点检报告
     */
    public MeterInspectionReport getMeterInspectionReportByDate(MeterInspectionReport meterInspectionReport);

    /**
     * 新增量具点检报告
     * 
     * @param meterInspectionReport 量具点检报告
     * @return 结果
     */
    public int insertMeterInspectionReport(MeterInspectionReport meterInspectionReport);

    /**
     * 修改量具点检报告
     * 
     * @param meterInspectionReport 量具点检报告
     * @return 结果
     */
    public int updateMeterInspectionReport(MeterInspectionReport meterInspectionReport);

    /**
     * 删除量具点检报告
     * 
     * @param reportIds 需要删除的量具点检报告主键集合
     * @return 结果
     */
    public int deleteMeterInspectionReportByReportIds(Long[] reportIds);

    public int meterInspectionReportSubmit(MeterDocimasyForm meterDocimasyForm);

    public List<MeterInspectionReportDetail> selectMeterInspectionReportDetailList(MeterInspectionReport meterInspectionReport);
}
