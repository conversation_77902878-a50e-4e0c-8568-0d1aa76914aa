package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterCheck;

/**
 * 量具盘点Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterCheckService 
{
    /**
     * 查询量具盘点
     * 
     * @param id 量具盘点主键
     * @return 量具盘点
     */
    public MeterCheck selectMeterCheckById(Long id);

    /**
     * 查询量具盘点列表
     * 
     * @param meterCheck 量具盘点
     * @return 量具盘点集合
     */
    public List<MeterCheck> selectMeterCheckList(MeterCheck meterCheck);

    /**
     * 新增量具盘点
     * 
     * @param meterCheck 量具盘点
     * @return 结果
     */
    public int insertMeterCheck(MeterCheck meterCheck);

    /**
     * 修改量具盘点
     * 
     * @param meterCheck 量具盘点
     * @return 结果
     */
    public int updateMeterCheck(MeterCheck meterCheck);

    /**
     * 删除量具盘点信息
     * 
     * @param id 量具盘点主键
     * @return 结果
     */
    public int deleteMeterCheckById(Long id);

    int deleteMeterCheckByIds(List<Long> ids);
}
