package com.hzforward.meter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.core.domain.entity.SysUser;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.meter.domain.MeterChangeHistory;
import com.hzforward.meter.domain.MeterTool;
import com.hzforward.meter.domain.res.MeterChangeHistoryListRes;
import com.hzforward.meter.mapper.MeterChangeHistoryMapper;
import com.hzforward.meter.mapper.MeterToolMapper;
import com.hzforward.meter.service.IMeterChangeHistoryService;
import com.hzforward.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 量具变更历史Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Service
@RequiredArgsConstructor
public class MeterChangeHistoryServiceImpl implements IMeterChangeHistoryService {

    private final MeterChangeHistoryMapper meterChangeHistoryMapper;
    private final MeterToolMapper meterToolMapper;
    private final ISysUserService sysUserService;

    /**
     * 查询量具变更历史
     *
     * @param id 量具变更历史主键
     * @return 量具变更历史
     */
    @Override
    public MeterChangeHistoryListRes selectMeterChangeHistoryById(Long id) {
        MeterChangeHistory detail = meterChangeHistoryMapper.selectById(id);
        MeterChangeHistoryListRes resDetail = BeanUtils.copyBeanPropAndReturn(detail, MeterChangeHistoryListRes.class);
        MeterTool tool = meterToolMapper.selectOne(new LambdaQueryWrapper<MeterTool>()
                .eq(MeterTool::getCode, detail.getToolCode())
                .eq(MeterTool::getDelFlag, '0'));
        resDetail.setCode(tool.getCode());
        resDetail.setName(tool.getName());
        return resDetail;
    }

    /**
     * 查询量具变更历史列表
     *
     * @param meterChangeHistory 量具变更历史
     * @return 量具变更历史
     */
    @Override
    public PageInfo<MeterChangeHistoryListRes> selectMeterChangeHistoryList(MeterChangeHistory meterChangeHistory) {
        List<MeterChangeHistory> list = meterChangeHistoryMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<MeterChangeHistory>()
                .eq(MeterChangeHistory::getDelFlag, '0')
                .eq(MeterChangeHistory::getToolCode, meterChangeHistory.getToolCode()));
        if (StringUtils.isEmpty(list)) {
            return new PageInfo<>();
        }
        List<String> toolIds = list.stream().map(MeterChangeHistory::getToolCode).collect(Collectors.toList());
        List<String> custodians = list.stream().map(MeterChangeHistory::getCustodian).collect(Collectors.toList());
        List<SysUser> userList = sysUserService.selectUserListByJobNos(custodians);
        Map<String, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getJobNo, sysUser -> sysUser));

        List<MeterTool> toolList = meterToolMapper.selectList(new LambdaQueryWrapper<MeterTool>()
                .in(MeterTool::getCode, toolIds)
                .eq(MeterTool::getDelFlag, '0'));
        Map<String, MeterTool> toolMap = toolList.stream().collect(Collectors.toMap(MeterTool::getCode, meterTool -> meterTool));

        PageInfo<MeterChangeHistoryListRes> res = BeanUtils.copyBeanPropAndReturnPage(list, MeterChangeHistoryListRes.class);
        for (MeterChangeHistoryListRes meterChangeHistoryListRes : res.getList()) {
            meterChangeHistoryListRes.setCode(StringUtils.isNotNull(toolMap.get(meterChangeHistoryListRes.getToolCode())) ? toolMap.get(meterChangeHistoryListRes.getToolCode()).getCode() : null);
            meterChangeHistoryListRes.setName(StringUtils.isNotNull(toolMap.get(meterChangeHistoryListRes.getToolCode())) ? toolMap.get(meterChangeHistoryListRes.getToolCode()).getName() : null);
            meterChangeHistoryListRes.setCustodianName(StringUtils.isNotNull(userMap.get(meterChangeHistoryListRes.getCustodian())) ? userMap.get(meterChangeHistoryListRes.getCustodian()).getNickName() : null);
        }
        return res;
    }

    /**
     * 新增量具变更历史
     *
     * @param meterChangeHistory 量具变更历史
     * @return 结果
     */
    @Override
    public int insertMeterChangeHistory(MeterChangeHistory meterChangeHistory) {
        return meterChangeHistoryMapper.insert(meterChangeHistory);
    }

    /**
     * 修改量具变更历史
     *
     * @param meterChangeHistory 量具变更历史
     * @return 结果
     */
    @Override
    public int updateMeterChangeHistory(MeterChangeHistory meterChangeHistory) {
        return meterChangeHistoryMapper.updateById(meterChangeHistory);
    }

    /**
     * 批量删除量具变更历史
     *
     * @param ids 需要删除的量具变更历史主键
     * @return 结果
     */
    @Override
    public int deleteMeterChangeHistoryByIds(List<Long> ids) {
        return meterChangeHistoryMapper.deleteBatchIds(ids);
    }

    /**
     * 删除量具变更历史信息
     *
     * @param id 量具变更历史主键
     * @return 结果
     */
    @Override
    public int deleteMeterChangeHistoryById(Long id) {
        return meterChangeHistoryMapper.deleteById(id);
    }
}
