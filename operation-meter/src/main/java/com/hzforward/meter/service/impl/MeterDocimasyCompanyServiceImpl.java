package com.hzforward.meter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.meter.domain.MeterDocimasyCompany;
import com.hzforward.meter.mapper.MeterDocimasyCompanyMapper;
import com.hzforward.meter.service.IMeterDocimasyCompanyService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 检定单位Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Service
public class MeterDocimasyCompanyServiceImpl implements IMeterDocimasyCompanyService {
    @Resource
    private MeterDocimasyCompanyMapper meterDocimasyCompanyMapper;

    /**
     * 查询检定单位
     *
     * @param id 检定单位主键
     * @return 检定单位
     */
    @Override
    public MeterDocimasyCompany selectMeterDocimasyCompanyById(Long id) {
        return meterDocimasyCompanyMapper.selectById(id);
    }

    /**
     * 查询检定单位列表
     *
     * @param meterDocimasyCompany 检定单位
     * @return 检定单位
     */
    @Override
    public List<MeterDocimasyCompany> selectMeterDocimasyCompanyList(MeterDocimasyCompany meterDocimasyCompany) {
        return meterDocimasyCompanyMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<MeterDocimasyCompany>()
                .like(MeterDocimasyCompany::getDocimasyCompanyName, meterDocimasyCompany.getDocimasyCompanyName()));
    }

    /**
     * 新增检定单位
     *
     * @param meterDocimasyCompany 检定单位
     * @return 结果
     */
    @Override
    public int insertMeterDocimasyCompany(MeterDocimasyCompany meterDocimasyCompany) {
        LambdaQueryWrapper<MeterDocimasyCompany> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.orderByDesc(MeterDocimasyCompany::getCreateTime);
        List<MeterDocimasyCompany> docimasyCompanyList = meterDocimasyCompanyMapper.selectList(lambdaQueryWrapper);
        Long code =0L;
        for (int i = 0;i< docimasyCompanyList.size();i++){
            String largeCode = docimasyCompanyList.get(i).getCode();
            int p = largeCode.indexOf('R');
            String largeString = largeCode.substring(p + 1);
            long largeNum = Long.parseLong(largeString)+1;
            if (largeNum>code){
                code = largeNum;
            }
        }
        String mouldCode = docimasyCompanyList.get(0).getCode();
        int p = mouldCode.indexOf('R');
        String left = mouldCode.substring(0, p+1);
        long rightNum = code;
        String newRight = String.valueOf(rightNum);
        int length =mouldCode.length()-String.valueOf(rightNum).length()-left.length();
        StringBuffer middle = new StringBuffer();
        for (int i = 0;i < length;i++){
            middle.append("0");
        }
        String newCode = left+middle+newRight;
        meterDocimasyCompany.setCode(newCode);
        return meterDocimasyCompanyMapper.insert(meterDocimasyCompany);
    }

    /**
     * 修改检定单位
     *
     * @param meterDocimasyCompany 检定单位
     * @return 结果
     */
    @Override
    public int updateMeterDocimasyCompany(MeterDocimasyCompany meterDocimasyCompany) {
        return meterDocimasyCompanyMapper.updateById(meterDocimasyCompany);
    }


    /**
     * 删除检定单位信息
     *
     * @param id 检定单位主键
     * @return 结果
     */
    @Override
    public int deleteMeterDocimasyCompanyById(Long id) {
        return meterDocimasyCompanyMapper.deleteById(id);
    }

    @Override
    public int deleteMeterDocimasyCompanyByIds(List<Long> ids) {
        return meterDocimasyCompanyMapper.deleteBatchIds(ids);
    }
}
