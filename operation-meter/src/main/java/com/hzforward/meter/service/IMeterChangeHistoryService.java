package com.hzforward.meter.service;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.hzforward.meter.domain.MeterChangeHistory;
import com.hzforward.meter.domain.res.MeterChangeHistoryListRes;

/**
 * 量具变更历史Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterChangeHistoryService 
{
    /**
     * 查询量具变更历史
     * 
     * @param id 量具变更历史主键
     * @return 量具变更历史
     */
    public MeterChangeHistoryListRes selectMeterChangeHistoryById(Long id);

    /**
     * 查询量具变更历史列表
     * 
     * @param meterChangeHistory 量具变更历史
     * @return 量具变更历史集合
     */
    public PageInfo<MeterChangeHistoryListRes> selectMeterChangeHistoryList(MeterChangeHistory meterChangeHistory);

    /**
     * 新增量具变更历史
     * 
     * @param meterChangeHistory 量具变更历史
     * @return 结果
     */
    public int insertMeterChangeHistory(MeterChangeHistory meterChangeHistory);

    /**
     * 修改量具变更历史
     * 
     * @param meterChangeHistory 量具变更历史
     * @return 结果
     */
    public int updateMeterChangeHistory(MeterChangeHistory meterChangeHistory);

    /**
     * 批量删除量具变更历史
     * 
     * @param ids 需要删除的量具变更历史主键集合
     * @return 结果
     */
    public int deleteMeterChangeHistoryByIds(List<Long> ids);

    /**
     * 删除量具变更历史信息
     * 
     * @param id 量具变更历史主键
     * @return 结果
     */
    public int deleteMeterChangeHistoryById(Long id);
}
