package com.hzforward.meter.service;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.hzforward.meter.domain.MeterDocimasy;
import com.hzforward.meter.domain.req.MeterDocimasyListReq;
import com.hzforward.meter.domain.res.MeterDocimasyListRes;
import org.springframework.web.multipart.MultipartFile;

/**
 * 量具检定Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterDocimasyService 
{
    /**
     * 查询量具检定
     * 
     * @param id 量具检定主键
     * @return 量具检定
     */
    public MeterDocimasy selectMeterDocimasyById(Long id);

    /**
     * 查询量具检定列表
     * 
     * @param meterDocimasyListReq 量具检定
     * @return 量具检定集合
     */
    public PageInfo<MeterDocimasyListRes> selectMeterDocimasyList(MeterDocimasyListReq meterDocimasyListReq);

    /**
     * 新增量具检定
     * 
     * @param meterDocimasy 量具检定
     * @return 结果
     */
    public int insertMeterDocimasy(MeterDocimasy meterDocimasy);

    /**
     * 修改量具检定
     * 
     * @param meterDocimasy 量具检定
     * @return 结果
     */
    public int updateMeterDocimasy(MeterDocimasy meterDocimasy);


    /**
     * 删除量具检定信息
     * 
     * @param id 量具检定主键
     * @return 结果
     */
    public int deleteMeterDocimasyById(Long id);

    int deleteMeterDocimasyByIds(List<Long> ids);

    int uploadFile(MultipartFile[] fileList, Long toolId);
}
