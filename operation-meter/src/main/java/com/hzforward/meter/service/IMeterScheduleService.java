package com.hzforward.meter.service;

import java.util.List;

import com.hzforward.meter.domain.MeterSchedule;

/**
 * 定时任务_量具Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-07
 */
public interface IMeterScheduleService 
{
    /**
     * 查询定时任务_量具
     * 
     * @param id 定时任务_量具主键
     * @return 定时任务_量具
     */
    public MeterSchedule selectMeterScheduleById(Long id);

    /**
     * 查询定时任务_量具列表
     * 
     * @param meterSchedule 定时任务_量具
     * @return 定时任务_量具集合
     */
    public List<MeterSchedule> selectMeterScheduleList(MeterSchedule meterSchedule);

    /**
     * 新增定时任务_量具
     * 
     * @param meterSchedule 定时任务_量具
     * @return 结果
     */
    public int insertMeterSchedule(MeterSchedule meterSchedule);

    /**
     * 修改定时任务_量具
     * 
     * @param meterSchedule 定时任务_量具
     * @return 结果
     */
    public int updateMeterSchedule(MeterSchedule meterSchedule);

    /**
     * 删除定时任务_量具
     * 
     * @param ids 需要删除的定时任务_量具主键集合
     * @return 结果
     */
    public int deleteMeterScheduleByIds(List<Long> ids);

    /**
     * 删除定时任务_量具
     *
     * @return 结果
     */
    public void sendMail();

}
