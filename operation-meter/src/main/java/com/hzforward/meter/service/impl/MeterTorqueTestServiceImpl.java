package com.hzforward.meter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.hzforward.common.constant.UserConstants;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.core.domain.entity.SysDept;
import com.hzforward.common.core.domain.entity.SysUser;
import com.hzforward.common.core.domain.mail.MailRequest;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.*;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.meter.domain.MeterDailyTorqueUntest;
import com.hzforward.meter.domain.MeterTool;
import com.hzforward.meter.domain.MeterTorqueTest;
import com.hzforward.meter.domain.req.MeterTorqueTestListReq;
import com.hzforward.meter.domain.res.MeterNotTestListRes;
import com.hzforward.meter.domain.res.MeterTorqueTestListRes;
import com.hzforward.meter.domain.res.MeterTorqueUnTestListRes;
import com.hzforward.meter.mapper.MeterDailyTorqueUntestMapper;
import com.hzforward.meter.mapper.MeterToolMapper;
import com.hzforward.meter.mapper.MeterTorqueTestMapper;
import com.hzforward.meter.service.IMeterTorqueTestService;
import com.hzforward.system.mapper.SysDeptMapper;
import com.hzforward.system.mapper.SysUserMapper;
import com.hzforward.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 扭力测试Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Service
@RequiredArgsConstructor
public class MeterTorqueTestServiceImpl implements IMeterTorqueTestService {

    private static final Logger log = LoggerFactory.getLogger(MeterTorqueTestServiceImpl.class);

    private final MeterTorqueTestMapper meterTorqueTestMapper;
    private final MeterToolMapper meterToolMapper;
    private final SysUserMapper sysUserMapper;
    private final ISysUserService sysUserService;
    private final SysDeptMapper sysDeptMapper;
    private final MailUtils mailUtils;
    private final MeterDailyTorqueUntestMapper meterDailyTorqueUntestMapper;

    /**
     * 查询扭力测试
     *
     * @param id 扭力测试主键
     * @return 扭力测试
     */
    @Override
    public MeterTorqueTest selectMeterTorqueTestById(Long id) {
        return meterTorqueTestMapper.selectById(id);
    }

    /**
     * 查询扭力测试列表
     *
     * @param meterTorqueTestListReq 扭力测试
     * @return 扭力测试
     */
    @Override
    public PageInfo<MeterTorqueTestListRes> selectMeterTorqueTestList(MeterTorqueTestListReq meterTorqueTestListReq, Integer pageNum, Integer pageSize) {
        List<MeterTorqueTest> list = meterTorqueTestMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<MeterTorqueTest>()
                .between(MeterTorqueTest::getCheckTime, StringUtils.isNotNull(meterTorqueTestListReq.getCheckDateRange()) ? DateUtils.getTodayStartTime(meterTorqueTestListReq.getCheckDateRange().get(0)) : null,
                        StringUtils.isNotNull(meterTorqueTestListReq.getCheckDateRange()) ? DateUtils.getTodayLastTime(meterTorqueTestListReq.getCheckDateRange().get(1)) : null)
                .eq(MeterTorqueTest::getCode, meterTorqueTestListReq.getCode())
                .orderByDesc(MeterTorqueTest::getCheckTime));
        PageUtils.startPage(pageNum, pageSize);
        if (StringUtils.isEmpty(list)) {
            return new PageInfo<>();
        }
        PageInfo<MeterTorqueTestListRes> resList = BeanUtils.copyBeanPropAndReturnPage(list, MeterTorqueTestListRes.class);
        List<String> codes = list.stream().map(MeterTorqueTest::getCode).collect(Collectors.toList());
        List<String> persons = list.stream().map(MeterTorqueTest::getCheckPerson).collect(Collectors.toList());
        Map<String, MeterTool> meterToolMap = new HashMap<>();
        if (StringUtils.isNotEmpty(codes)) {
            List<MeterTool> meterToolList = meterToolMapper.selectList(new LambdaQueryWrapper<MeterTool>().in(MeterTool::getCode, codes));
            meterToolMap = meterToolList.stream().collect(Collectors.toMap(MeterTool::getCode, meterTool -> meterTool));
        }
        for (MeterTorqueTestListRes meterTorqueTestListRes : resList.getList()) {
            MeterTool meterTool = meterToolMap.get(meterTorqueTestListRes.getCode());
            if (StringUtils.isNotNull(meterTool)) {
                meterTorqueTestListRes.setName(meterTool.getName());
                meterTorqueTestListRes.setMinRangeOfApplication(meterTool.getMinRangeOfApplication());
                meterTorqueTestListRes.setMaxRangeOfApplication(meterTool.getMaxRangeOfApplication());
                meterTorqueTestListRes.setUnit(meterTool.getUnit());
            }
        }
        List<SysUser> userList = sysUserService.selectUserListByJobNos(persons);
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(SysUser::getUserName, SysUser::getNickName));
        for (MeterTorqueTestListRes meterTorqueTestListRes : resList.getList()) {
            meterTorqueTestListRes.setCheckPersonName(userMap.get(meterTorqueTestListRes.getCheckPerson()));
        }
        return resList;
    }

    /**
     * 新增扭力测试
     *
     * @param meterTorqueTest 扭力测试
     * @return 结果
     */
    @Override
    public int insertMeterTorqueTest(MeterTorqueTest meterTorqueTest) {

        return meterTorqueTestMapper.insert(meterTorqueTest);
    }

    /**
     * 修改扭力测试
     *
     * @param meterTorqueTest 扭力测试
     * @return 结果
     */
    @Override
    public int updateMeterTorqueTest(MeterTorqueTest meterTorqueTest) {
        return meterTorqueTestMapper.updateById(meterTorqueTest);
    }

    /**
     * 删除扭力测试信息
     *
     * @param id 扭力测试主键
     * @return 结果
     */
    @Override
    public int deleteMeterTorqueTestById(Long id) {
        return meterTorqueTestMapper.deleteById(id);
    }

    @Override
    public int deleteMeterTorqueTestByIds(List<Long> ids) {
        return meterTorqueTestMapper.deleteBatchIds(ids);
    }

    /**
     * 查询未测试列表
     *
     * @param meterTorqueTestListReq
     * @return
     */
    @Override
    public PageInfo<MeterNotTestListRes> testList(MeterTorqueTestListReq meterTorqueTestListReq) {
        if (StringUtils.isNull(meterTorqueTestListReq.getCheckDateRange())) {
            throw new ServiceException("请选择测试日期");
        }
        LambdaQueryWrapper<MeterTorqueTest> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(MeterTorqueTest::getCheckTime, DateUtils.getTodayStartTime(meterTorqueTestListReq.getCheckDateRange().get(0)))
                .le(MeterTorqueTest::getCheckTime, DateUtils.getTodayLastTime(meterTorqueTestListReq.getCheckDateRange().get(1)));
        // 如果 code 不为空，则添加 code 条件
        if (meterTorqueTestListReq.getCode() != null && !meterTorqueTestListReq.getCode().isEmpty()) {
            queryWrapper.eq(MeterTorqueTest::getCode, meterTorqueTestListReq.getCode());
        }
        List<MeterTorqueTest> testList = meterTorqueTestMapper.selectList(queryWrapper);
        if (meterTorqueTestListReq.getTestFlag().equals(1)) {
            List<String> codes = testList.stream().map(MeterTorqueTest::getCode).collect(Collectors.toList());
            List<MeterDailyTorqueUntest> meterDailyTorqueUntests = meterDailyTorqueUntestMapper.selectList(new LambdaQueryWrapper<MeterDailyTorqueUntest>()
                    .ge(MeterDailyTorqueUntest::getCreateTime, DateUtils.getTodayStartTime(meterTorqueTestListReq.getCheckDateRange().get(0)))
                    .le(MeterDailyTorqueUntest::getCreateTime, DateUtils.getTodayLastTime(meterTorqueTestListReq.getCheckDateRange().get(1))));
            List<String> dailyTestCodes = meterDailyTorqueUntests.stream().map(MeterDailyTorqueUntest::getCode).collect(Collectors.toList());
            for (String code : codes) {
                if (dailyTestCodes.contains(code)) {
                    dailyTestCodes.remove(code);
                }
            }
            if (dailyTestCodes.size() == 0) {
                return new PageInfo<>();
            } else {
                PageUtils.startPage();
                List<MeterDailyTorqueUntest> untestList = meterDailyTorqueUntestMapper.selectList(new LambdaQueryWrapper<MeterDailyTorqueUntest>()
                        .in(MeterDailyTorqueUntest::getCode, dailyTestCodes)
                        .ge(MeterDailyTorqueUntest::getCreateTime, DateUtils.getTodayStartTime(meterTorqueTestListReq.getCheckDateRange().get(0)))
                        .le(MeterDailyTorqueUntest::getCreateTime, DateUtils.getTodayLastTime(meterTorqueTestListReq.getCheckDateRange().get(1))));
                PageInfo<MeterNotTestListRes> listRes = BeanUtils.copyBeanPropAndReturnPage(untestList, MeterNotTestListRes.class);
                return listRes;
            }
        }
        List<String> codes = testList.stream().map(MeterTorqueTest::getCode).collect(Collectors.toList());
        LambdaQueryWrapper<MeterTool> lqw = new LambdaQueryWrapper<MeterTool>()
                .eq(MeterTool::getDelFlag, "0")
                .eq(MeterTool::getDocimasyStatus, 2)
                .in(MeterTool::getTorqueForceTestScope, Arrays.asList(1, 7, 15, 30));
        if (StringUtils.isEmpty(codes) && meterTorqueTestListReq.getTestFlag() == 2) {
            return new PageInfo<>();
        } else if (StringUtils.isNotEmpty(codes) && meterTorqueTestListReq.getTestFlag() == 1) {
            lqw.notIn(MeterTool::getCode, codes);
        } else if (StringUtils.isNotEmpty(codes) && meterTorqueTestListReq.getTestFlag() == 2) {
            lqw.in(MeterTool::getCode, codes);
        }
        PageUtils.startPage();
        List<MeterTool> list = meterToolMapper.selectList(lqw);
        if (StringUtils.isEmpty(list)) {
            return new PageInfo<>();
        }
        PageInfo<MeterNotTestListRes> listRes = BeanUtils.copyBeanPropAndReturnPage(list, MeterNotTestListRes.class);
        List<String> safeguardPersons = list.stream().map(MeterTool::getSafeguardPerson).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(safeguardPersons)) {
            List<SysUser> userList = sysUserService.selectUserListByJobNos(safeguardPersons);
            List<Long> deptIds = userList.stream().map(SysUser::getDeptId).collect(Collectors.toList());
            //处理部门数据
            Long deptId = Long.parseLong(DictUtils.getDictValue("measuring_tool_config", "dept_id"));
            List<SysDept> sysDeptList = new ArrayList<>();
            for (int i = deptIds.size() - 1; i >= 0; i--) {
                if (deptIds.get(i).equals(deptId)) {
                    deptIds.remove(i);
                }
            }
            List<SysDept> deptList = sysDeptMapper.selectBatchIds(deptIds);
            while (StringUtils.isNotEmpty(deptIds)) {
                deptIds = deptList.stream().map(SysDept::getParentId).collect(Collectors.toList());
                for (int i = deptIds.size() - 1; i >= 0; i--) {
                    if (deptIds.get(i).equals(deptId)) {
                        deptIds.remove(i);
                    }
                }
                sysDeptList.addAll(deptList);
                if (StringUtils.isNotEmpty(deptIds)) {
                    deptList = sysDeptMapper.selectBatchIds(deptIds);
                }
            }
            Map<Long, SysDept> deptMap = sysDeptList.stream().collect(Collectors.toMap(SysDept::getDeptId, sysDept -> sysDept, (k1, k2) -> k1));
            Map<String, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getJobNo, sysUser -> sysUser, (k1, k2) -> k1));
            for (MeterNotTestListRes meterNotTestListRes : listRes.getList()) {
                SysUser user = userMap.get(meterNotTestListRes.getSafeguardPerson());
                if (StringUtils.isNotNull(user)) {
                    meterNotTestListRes.setSafeguardPersonName(user.getNickName());
                    SysDept dept = deptMap.get(user.getDeptId());
                    String deptName = StringUtils.isNotNull(dept) ? dept.getDeptName() : null;
//                    while (StringUtils.isNotNull(dept)) {
//                        dept = deptMap.get(dept.getParentId());
//                        if (StringUtils.isNotNull(dept)) {
//                            deptName = dept.getDeptName() + " - " + deptName;
//                        }
//                    }
                    meterNotTestListRes.setStashDept(deptName);
                }
            }
        }
        return listRes;
    }

    /**
     * 导入扭力测试数据
     * @param meterTorqueTestList
     * @return
     */
    @Override
    public String importMeterTorqueTest(List<MeterTorqueTest> meterTorqueTestList) {
        if (StringUtils.isNull(meterTorqueTestList) || meterTorqueTestList.size() == 0) {
            throw new ServiceException("导入数据不能为空！");
        }
        List<String> checkPersonList = meterTorqueTestList.stream().map(MeterTorqueTest::getCheckPerson).collect(Collectors.toList());
        List<SysUser> userList = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getDelFlag, "0")
                .in(SysUser::getNickName, checkPersonList)
                .eq(SysUser::getUserType, UserConstants.METER_TYPE)
        );
        Map<String, String> userMap = userList.stream().collect(Collectors.toMap(SysUser::getNickName, sysUser -> sysUser.getJobNo()));
        for (MeterTorqueTest meterTorqueTest : meterTorqueTestList) {
            meterTorqueTest.setCheckPerson(userMap.get(meterTorqueTest.getCheckPerson()));
            meterTorqueTest.setDelFlag("0");
            meterTorqueTest.setCreateBy(SecurityUtils.getUsername());
            meterTorqueTest.setCreateTime(DateUtils.getNowDate());
        }
        meterTorqueTestMapper.insertBatchSomeColumn(meterTorqueTestList);
        return "导入成功" + meterTorqueTestList.size() + "条数据";
    }

    /**
     * 导出扭力未测试数据
     *
     * @param meterTorqueTestListReq
     * @return
     */
    @Override
    public List<MeterTorqueUnTestListRes> exportMeterNotTorqueTest(MeterTorqueTestListReq meterTorqueTestListReq, Integer pageNum, Integer pageSize) {
        List<MeterTorqueUnTestListRes> meterTorqueUnTestList = new ArrayList<>();
        List<MeterTool> tools = meterToolMapper.selectList(new LambdaQueryWrapper<MeterTool>()
                .in(MeterTool::getTorqueForceTestScope, Arrays.asList(1, 7, 15, 30))
                .eq(MeterTool::getDocimasyStatus, 2));
        List<String> codes = tools.stream().map(MeterTool::getCode).collect(Collectors.toList());
        List<Date> allDatesBetween = getAllDatesBetween(meterTorqueTestListReq.getCheckDateRange().get(0), meterTorqueTestListReq.getCheckDateRange().get(1));
        //循环checkDateRange，并根据当天日期进行扭力未测数据筛选
        for (int i = 0; i < allDatesBetween.size(); i++) {
            Date checkTime = allDatesBetween.get(i);
            List<MeterTorqueTest> torqueTests = meterTorqueTestMapper.selectList(new LambdaQueryWrapper<MeterTorqueTest>()
                    .in(MeterTorqueTest::getCode, codes)
                    .ge(MeterTorqueTest::getCheckTime, DateUtils.getTodayStartTime(checkTime))
                    .le(MeterTorqueTest::getCheckTime, DateUtils.getTodayLastTime(checkTime))
            );
            List<String> testCodes = torqueTests.stream().map(MeterTorqueTest::getCode).collect(Collectors.toList());
            List<String> noTestCodes = codes.stream().filter(code -> !testCodes.contains(code)).collect(Collectors.toList());
            for (int j = noTestCodes.size() - 1; j >= 0; j--) {
                MeterTool meterTool = meterToolMapper.selectOne(new LambdaQueryWrapper<MeterTool>()
                        .eq(MeterTool::getCode, noTestCodes.get(j)));
                if (meterTool == null) {
                    noTestCodes.remove(noTestCodes.get(j));
                    continue;
                }
                MeterTorqueTest torqueTest = meterTorqueTestMapper.selectOne(new LambdaQueryWrapper<MeterTorqueTest>()
                        .eq(MeterTorqueTest::getCode, noTestCodes.get(j))
                        .orderByDesc(MeterTorqueTest::getCheckTime)
                        .last("LIMIT 1")
                );
                if (torqueTest != null) {
                    Long timeDiff = getTimeDiff(checkTime, torqueTest.getCheckTime());
                    if (timeDiff < meterTool.getTorqueForceTestScope()) {
                        noTestCodes.remove(noTestCodes.get(j));
                    }
                }
            }
            if (noTestCodes.size() == 0) {
                return meterTorqueUnTestList;
            }
            List<MeterTool> noTestTools = meterToolMapper.selectList(new LambdaQueryWrapper<MeterTool>().in(MeterTool::getCode, noTestCodes));
            for (int index = 0; index < noTestTools.size(); index++) {
                MeterTorqueUnTestListRes meterTorqueUnTestListRes = new MeterTorqueUnTestListRes();
                MeterTool tool = noTestTools.get(index);

                SysUser safeguardPerson = sysUserService.selectUserListByJobNo(tool.getSafeguardPerson());

                SysDept dept = sysDeptMapper.selectOne(new LambdaQueryWrapper<SysDept>()
                        .eq(SysDept::getDeptId, safeguardPerson.getDeptId()));
//                if (safeguardPerson.getUserName().equals("")) {
//                    meterTorqueUnTestListRes.setStashDept("");
//                } else if (dept.getParentId().equals(13244L)) {
//                    meterTorqueUnTestListRes.setStashDept(dept.getDeptName());
//                } else {
//                    SysDept parentDept = sysDeptMapper.selectOne(new LambdaQueryWrapper<SysDept>()
//                            .eq(SysDept::getDeptId, dept.getParentId()));
//                    meterTorqueUnTestListRes.setStashDept(parentDept.getDeptName() + "-" + dept.getDeptName());
//                }
                if (safeguardPerson.getUserName().equals("meterOperator")) {
                    meterTorqueUnTestListRes.setStashDept("计量室");
                }else if(safeguardPerson.getUserName().equals("yaoGuo")){
                    meterTorqueUnTestListRes.setStashDept("集团服务");
                }else if(safeguardPerson.getUserName().equals("已离职")){
                    meterTorqueUnTestListRes.setStashDept("");
                }else {
                    meterTorqueUnTestListRes.setStashDept(dept.getDeptName());
                }
                // 获取当前日期
                meterTorqueUnTestListRes.setDate(checkTime);
                meterTorqueUnTestListRes.setCode(tool.getCode());
                meterTorqueUnTestListRes.setName(tool.getName());
                meterTorqueUnTestListRes.setTorqueForceTestScope(tool.getTorqueForceTestScope());
                meterTorqueUnTestListRes.setFirstProofPeriod(tool.getFirstProofPeriod());
                meterTorqueUnTestListRes.setSafeguardPersonName(safeguardPerson.getNickName());
                meterTorqueUnTestList.add(meterTorqueUnTestListRes);
            }
        }
        return meterTorqueUnTestList;
    }

    /**
     * 导出扭力测试数据
     *
     * @param meterTorqueTestListReq
     * @return
     */
    @Override
    public List<MeterTorqueTestListRes> exportMeterTorqueTest(MeterTorqueTestListReq meterTorqueTestListReq) {
        List<MeterTorqueTestListRes> meterTorqueUnTestList = new ArrayList<>();
        //循环checkDateRange，并根据当天日期进行扭力未测数据筛选
        List<Date> allDatesBetween = getAllDatesBetween(meterTorqueTestListReq.getCheckDateRange().get(0), meterTorqueTestListReq.getCheckDateRange().get(1));
        outerLoop:
        // 给最外层循环一个标签
        for (int i = 0; i < allDatesBetween.size(); i++) {
            MeterTorqueTestListReq testListReq = new MeterTorqueTestListReq();
            Date checkTime = allDatesBetween.get(i);
            testListReq.setCheckDateRange(Arrays.asList(checkTime, checkTime));
            testListReq.setTestFlag(meterTorqueTestListReq.getTestFlag());
            PageInfo<MeterTorqueTestListRes> testedResPageInfo = selectMeterTorqueTestList(testListReq, 1, 10000);
            List<MeterTorqueTestListRes> meterTorqueTests = testedResPageInfo.getList();
            if (meterTorqueTests == null) {
                continue;
            }
            for (MeterTorqueTestListRes meterTorqueTest : meterTorqueTests) {
                meterTorqueTest.setCheckPersonEmpId(meterTorqueTest.getCheckPerson());
                meterTorqueUnTestList.add(meterTorqueTest);
            }
            if (i < meterTorqueTestListReq.getCheckDateRange().size() - 1 && meterTorqueTestListReq.getCheckDateRange().get(i).equals(meterTorqueTestListReq.getCheckDateRange().get(i + 1))) {
                break outerLoop; // 使用标签来退出最外层循环
            }
        }
        return meterTorqueUnTestList;
    }


    /**
     * 定时发送扭力测试未测试邮件
     */
    @Override
    public void sendUnMeterTorqueTestMail() {
        // 创建一个要追加的数组
        List<Long> receiveIds = new ArrayList<>();
        //91L,434L,118L,25L,375L,207L,107L,536L,212L,17L,247L,78L,
            Long[] elementsToAdd = {1315L,1009L,999L,1451L,1003L,1314L,1313L,1447L,1004L,695L,1455L,1106L,2274L};
        List<Long> elementsList = Arrays.asList(elementsToAdd);
        // 使用addAll方法一次性追加多个元素
        receiveIds.addAll(elementsList);
        // 获取当前日期
        LocalDate today = LocalDate.now();
        String todayDate = today.getYear() + "年" + today.getMonthValue() + "月" + today.getDayOfMonth() + "日";
        List<MeterTorqueTest> torqueTests = meterTorqueTestMapper.selectList(new LambdaQueryWrapper<MeterTorqueTest>()
                .ge(MeterTorqueTest::getCheckTime, DateUtils.getTodayStartTime(DateUtils.getNowDate()))
                .le(MeterTorqueTest::getCheckTime, DateUtils.getTodayLastTime(DateUtils.getNowDate()))
        );

        List<String> codes = torqueTests.stream().map(MeterTorqueTest::getCode).collect(Collectors.toList());
        List<MeterDailyTorqueUntest> meterDailyTorqueUntests = meterDailyTorqueUntestMapper.selectList(new LambdaQueryWrapper<MeterDailyTorqueUntest>()
                .ge(MeterDailyTorqueUntest::getCreateTime, DateUtils.getTodayStartTime(DateUtils.getNowDate()))
                .le(MeterDailyTorqueUntest::getCreateTime, DateUtils.getTodayLastTime(DateUtils.getNowDate())));
        List<String> dailyTestCodes = meterDailyTorqueUntests.stream().map(MeterDailyTorqueUntest::getCode).collect(Collectors.toList());
        for (String code : codes) {
            if (dailyTestCodes.contains(code)) {
                dailyTestCodes.remove(code);
            }
        }
        List<MeterDailyTorqueUntest> untestList;
        if (dailyTestCodes.size() == 0) {
            untestList = new ArrayList<>();
        } else {
            untestList = meterDailyTorqueUntestMapper.selectList(new LambdaQueryWrapper<MeterDailyTorqueUntest>()
                    .in(MeterDailyTorqueUntest::getCode, dailyTestCodes)
                    .ge(MeterDailyTorqueUntest::getCreateTime, DateUtils.getTodayStartTime(DateUtils.getNowDate()))
                    .le(MeterDailyTorqueUntest::getCreateTime, DateUtils.getTodayLastTime(DateUtils.getNowDate()))
            );
        }
        // 创建一个新的工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个新的工作表
        Sheet sheet = workbook.createSheet("扭力测试未测");
        // 定义表头
        String[] headers = {"日期", "量具编号", "量具名称", "保管人", "存放部门"};
        // 创建表头行
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < headers.length; i++) {
            Cell headerCell = headerRow.createCell(i);
            headerCell.setCellValue(headers[i]);
            sheet.autoSizeColumn(i);
        }

        // 创建数据行
        for (int rowIndex = 0; rowIndex < untestList.size(); rowIndex++) {
            MeterDailyTorqueUntest rowData = untestList.get(rowIndex);
            Row row = sheet.createRow(rowIndex + 1); // 行号从1开始，因为表头占据了第0行
            row.createCell(0).setCellValue(todayDate);
            row.createCell(1).setCellValue(rowData.getCode());
            row.createCell(2).setCellValue(rowData.getName());
            row.createCell(3).setCellValue(rowData.getSafeguardPersonName());
            row.createCell(4).setCellValue(rowData.getStashDept());
        }
        // 创建一个新的工作表
        Sheet sheetTo = workbook.createSheet("不合格明细");
        // 定义表头
        String[] headersTo = {"量具编号", "量具名称", "最小扭力", "最大扭力", "扭力单位", "实测值1", "实测值2", "实测值3", "平均值", "结果", "检测人", "检测日期", "检测人名称"};
        // 创建表头行
        Row headerRowTo = sheetTo.createRow(0);
        for (int i = 0; i < headersTo.length; i++) {
            Cell headerCellTo = headerRowTo.createCell(i);
            headerCellTo.setCellValue(headersTo[i]);
            sheetTo.autoSizeColumn(i);
        }
        MeterTorqueTestListReq testListReq = new MeterTorqueTestListReq();
        testListReq.setCheckDateRange(Arrays.asList(new Date(), new Date()));
        testListReq.setTestFlag(1);
        PageInfo<MeterTorqueTestListRes> testedResPageInfo = selectMeterTorqueTestList(testListReq, 1, 10000);
        List<MeterTorqueTestListRes> meterTorqueTests = testedResPageInfo.getList();
        //重新设置一个不合格的列表作为循环插入到表格中
        ArrayList<MeterTorqueTestListRes> unQuaMeterTorqueTests = new ArrayList<>();
        if (meterTorqueTests != null) {
            for (MeterTorqueTestListRes meterTorqueTestListRes : meterTorqueTests) {
                if (meterTorqueTestListRes.getResult().equals("不合格")) {
                    unQuaMeterTorqueTests.add(meterTorqueTestListRes);
                }
            }
            for (int rowIndex = 0; rowIndex < unQuaMeterTorqueTests.size(); rowIndex++) {
                MeterTorqueTestListRes rowData = unQuaMeterTorqueTests.get(rowIndex);
                Row rowTo = sheetTo.createRow(rowIndex + 1); // 行号从1开始，因为表头占据了第0行
                rowTo.createCell(0).setCellValue(rowData.getCode());
                rowTo.createCell(1).setCellValue(rowData.getName());
                rowTo.createCell(2).setCellValue(rowData.getMinRangeOfApplication());
                rowTo.createCell(3).setCellValue(rowData.getMaxRangeOfApplication());
                rowTo.createCell(4).setCellValue(rowData.getUnit());
                rowTo.createCell(5).setCellValue(rowData.getValOne());
                rowTo.createCell(6).setCellValue(rowData.getValTwo());
                rowTo.createCell(7).setCellValue(rowData.getValThree());
                rowTo.createCell(8).setCellValue(rowData.getAverageVal());
                rowTo.createCell(9).setCellValue(rowData.getResult());
                rowTo.createCell(10).setCellValue(rowData.getCheckPerson());
                rowTo.createCell(11).setCellValue(rowData.getCheckTime());
                rowTo.createCell(12).setCellValue(rowData.getCheckPersonName());
            }
        }
        // 将工作簿写入文件
        String fileName = "扭力测试反馈_" + System.currentTimeMillis() + ".xlsx";
        
        // 使用项目统一的上传路径
        String uploadPath = com.hzforward.common.config.OperationConfig.getUploadPath() + "/torque_test_reports";
        File uploadDir = new File(uploadPath);
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        
        String filePath = uploadPath + File.separator + fileName;
        File excelFile = null;
        
        try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
            workbook.write(outputStream);
            excelFile = new File(filePath);
        } catch (IOException e) {
            log.error("创建扭力测试反馈文件失败", e);
            return;
        } finally {
            // 关闭工作簿
            try {
                workbook.close();
            } catch (IOException e) {
                log.warn("关闭工作簿失败", e);
            }
        }
        
        List<SysUser> receives = sysUserMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserId, receiveIds));
        MailRequest mailRequest = new MailRequest();
        mailRequest.setSubject("扭力测试未测、不合格通知");
        mailRequest.setSendTo(receives.stream().map(SysUser::getEmail).collect(Collectors.joining(",")));
        int total = torqueTests.size() + meterDailyTorqueUntests.size();
        if (untestList.size() > 0 || unQuaMeterTorqueTests.size() > 0) {
            mailRequest.setText(todayDate + "应校" + total + "个，其中应校未校" + untestList.size() + "个，不合格" + unQuaMeterTorqueTests.size() + "个，具体见附件。");
            mailRequest.setFilePath(excelFile.getAbsolutePath());
            
            try {
                mailUtils.sendAttachmentsMail(mailRequest);
                log.info("扭力测试反馈邮件发送成功");
            } finally {
                // 无论邮件发送成功还是失败，都清理临时文件
                if (excelFile != null && excelFile.exists()) {
                    boolean deleted = excelFile.delete();
                    if (deleted) {
                        log.debug("扭力测试反馈临时文件已清理: {}", excelFile.getAbsolutePath());
                    } else {
                        log.warn("扭力测试反馈临时文件清理失败: {}", excelFile.getAbsolutePath());
                    }
                }
            }
        }
    }

    public void updateDailyUnMeterTorqueTest() {
        Date nowDate = new Date();
        //查询需要扭力测试的量具
        List<MeterTool> tools = meterToolMapper.selectList(new LambdaQueryWrapper<MeterTool>()
                .in(MeterTool::getTorqueForceTestScope, Arrays.asList(1, 7, 15, 30))
                .eq(MeterTool::getDocimasyStatus, 2));
        List<String> codes = tools.stream().map(MeterTool::getCode).collect(Collectors.toList());
        //for循环量具，查询最新测试的数据
        for (String code : codes) {
            MeterDailyTorqueUntest meterTorqueUnTestListRes = new MeterDailyTorqueUntest();
            MeterTorqueTest torqueTest = meterTorqueTestMapper.selectOne(new LambdaQueryWrapper<MeterTorqueTest>()
                    .eq(MeterTorqueTest::getCode, code)
                    .orderByDesc(MeterTorqueTest::getCheckTime)
                    .last("LIMIT 1")
            );
            MeterTool tool = meterToolMapper.selectOne(new LambdaQueryWrapper<MeterTool>()
                    .eq(MeterTool::getCode, code));
            Date checkTime;
            if (torqueTest != null) {
                checkTime = DateUtils.getTodayStartTime(torqueTest.getCheckTime());
            } else {
                checkTime = null;
            }
//            // 使用Calendar类来操作日期
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(nowDate); // 设置时间为当前时间
//            calendar.add(Calendar.DAY_OF_MONTH, 1); // 在当前日期上增加一天
//            // 获取后一天的日期
//            Date nextDay = calendar.getTime();
            Long timeDiff = getTimeDiff(DateUtils.getTodayStartTime(nowDate), checkTime);
            if (timeDiff > tool.getTorqueForceTestScope()) {

                SysUser user = sysUserService.selectUserListByJobNo(tool.getSafeguardPerson());
                SysUser safeguardPerson = sysUserService.selectUserListByJobNo(tool.getSafeguardPerson());

                SysDept dept = sysDeptMapper.selectOne(new LambdaQueryWrapper<SysDept>()
                        .eq(SysDept::getDeptId, safeguardPerson.getDeptId()));
                if (safeguardPerson.getDeptId().equals(13244L)) {
                    meterTorqueUnTestListRes.setStashDept("");
                } else if (dept.getParentId().equals(13244L)) {
                    meterTorqueUnTestListRes.setStashDept(dept.getDeptName());
                } else {
                    SysDept parentDept = sysDeptMapper.selectOne(new LambdaQueryWrapper<SysDept>()
                            .eq(SysDept::getDeptId, dept.getParentId()));
                    meterTorqueUnTestListRes.setStashDept(parentDept.getDeptName() + "-" + dept.getDeptName());
                }
                meterTorqueUnTestListRes.setDate(nowDate);
                meterTorqueUnTestListRes.setCode(tool.getCode());
                meterTorqueUnTestListRes.setName(tool.getName());
                meterTorqueUnTestListRes.setTorqueForceTestScope(tool.getTorqueForceTestScope());
                meterTorqueUnTestListRes.setFirstProofPeriod(tool.getFirstProofPeriod());
                meterTorqueUnTestListRes.setSafeguardPersonName(user.getNickName());
                meterTorqueUnTestListRes.setCreateBy("admin");
                meterTorqueUnTestListRes.setCreateTime(DateUtils.getNowDate());
                meterDailyTorqueUntestMapper.insert(meterTorqueUnTestListRes);
            }
        }
    }

    /**
     * 获取时间差
     *
     * @param dateOne,dateTo
     * @return
     */
    public static Long getTimeDiff(Date dateOne, Date dateTo) {
        if (dateTo == null) {
            return 9999L;
        }
        // 使用Calendar类来计算天数差
        Calendar calendar1 = Calendar.getInstance();
        calendar1.setTime(dateOne);
        calendar1.set(Calendar.HOUR_OF_DAY, 0);
        calendar1.set(Calendar.MINUTE, 0);
        calendar1.set(Calendar.SECOND, 0);
        calendar1.set(Calendar.MILLISECOND, 0);

        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(dateTo);
        calendar2.set(Calendar.HOUR_OF_DAY, 0);
        calendar2.set(Calendar.MINUTE, 0);
        calendar2.set(Calendar.SECOND, 0);
        calendar2.set(Calendar.MILLISECOND, 0);

        // 计算相差的毫秒数并转换为天数
        long diffInMillis = Math.abs(calendar2.getTimeInMillis() - calendar1.getTimeInMillis());
        long diffInDays = diffInMillis / (1000 * 60 * 60 * 24);

        return diffInDays;
    }

    public static List<Date> getAllDatesBetween(Date startDate, Date endDate) {
        List<Date> dates = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        // 注意：包含起始日期，不包含结束日期
        Date currentDate = calendar.getTime();
        while (currentDate.before(endDate) || currentDate.equals(endDate)) {
            dates.add((Date) currentDate.clone()); // 添加当前日期并克隆，避免后续修改影响列表中的元素
            calendar.add(Calendar.DATE, 1); // 增加一天
            currentDate = calendar.getTime(); // 获取新的当前日期
        }

        return dates;
    }

}
