package com.hzforward.meter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.config.OperationConfig;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.file.FileUtils;
import com.hzforward.meter.domain.MeterCertificateReport;
import com.hzforward.meter.mapper.MeterCertificateReportMapper;
import com.hzforward.meter.service.IMeterCertificateReportService;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 量具证书报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Service
public class MeterCertificateReportServiceImpl implements IMeterCertificateReportService {
    @Resource
    private MeterCertificateReportMapper meterCertificateReportMapper;

    /**
     * 查询量具证书报告
     *
     * @param id 量具证书报告主键
     * @return 量具证书报告
     */
    @Override
    public MeterCertificateReport selectMeterCertificateReportById(Long id) {
        return meterCertificateReportMapper.selectById(id);
    }

    /**
     * 查询量具证书报告列表
     *
     * @param meterCertificateReport 量具证书报告
     * @return 量具证书报告
     */
    @Override
    public List<MeterCertificateReport> selectMeterCertificateReportList(MeterCertificateReport meterCertificateReport) {
        return meterCertificateReportMapper.selectList(new LambdaQueryWrapper<MeterCertificateReport>()
                .eq(MeterCertificateReport::getToolId, meterCertificateReport.getToolId())
                .eq(MeterCertificateReport::getDelFlag,0));
    }

    /**
     * 新增量具证书报告
     *
     * @param meterCertificateReport 量具证书报告
     * @return 结果
     */
    @Override
    public int insertMeterCertificateReport(MeterCertificateReport meterCertificateReport) {
        return meterCertificateReportMapper.insert(meterCertificateReport);
    }

    /**
     * 修改量具证书报告
     *
     * @param meterCertificateReport 量具证书报告
     * @return 结果
     */
    @Override
    public int updateMeterCertificateReport(MeterCertificateReport meterCertificateReport) {
        return meterCertificateReportMapper.updateById(meterCertificateReport);
    }

    /**
     * 批量删除量具证书报告
     *
     * @param ids 需要删除的量具证书报告主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMeterCertificateReportByIds(List<Long> ids) {
        List<MeterCertificateReport> list = meterCertificateReportMapper.selectBatchIds(ids);
        for (MeterCertificateReport meterCertificateReport : list) {
            File file = new File(OperationConfig.getProfile() + meterCertificateReport.getPath());
            if (file.exists()) {
                file.delete();
            }
        }
        return meterCertificateReportMapper.deleteBatchIds(ids);
    }

    /**
     * 删除量具证书报告信息
     *
     * @param id 量具证书报告主键
     * @return 结果
     */
    @Override
    public int deleteMeterCertificateReportById(Long id) {
        return meterCertificateReportMapper.deleteById(id);
    }

    /**
     * 下载文件
     * @param response
     * @param id
     */
    @Override
    public void downLoad(HttpServletResponse response, Long id) {
        try {
            MeterCertificateReport report = meterCertificateReportMapper.selectById(id);
            String filePath = OperationConfig.getProfile()  + report.getPath();
            File file = new File(filePath);

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, file.getName());
            FileUtils.writeBytes(filePath, response.getOutputStream());
        } catch (IOException e) {
            throw new ServiceException();
        }
    }
}
