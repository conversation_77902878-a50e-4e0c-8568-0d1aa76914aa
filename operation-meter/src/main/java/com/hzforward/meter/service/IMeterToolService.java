package com.hzforward.meter.service;

import java.util.Date;
import java.util.List;

import com.github.pagehelper.PageInfo;
import com.hzforward.meter.domain.MeterChangeHistory;
import com.hzforward.meter.domain.MeterDocimasy;
import com.hzforward.meter.domain.MeterMaintainHistory;
import com.hzforward.meter.domain.MeterTool;
import com.hzforward.meter.domain.excel.MeterToolExcel;
import com.hzforward.meter.domain.req.MeterToolListReq;
import com.hzforward.meter.domain.res.MeterToolListRes;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

/**
 * 量具管理Service接口
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterToolService
{
    /**
     * 查询量具管理
     *
     * @param id 量具管理主键
     * @return 量具管理
     */
    public MeterTool selectMeterToolById(Long id);

    /**
     * 查询所有量具
     * @param meterToolListReq
     * @return
     */
    public List<MeterTool> selectAllTool(MeterToolListReq meterToolListReq);
    /**
     * 查询量具管理列表
     *
     * @param meterToolListReq 量具管理
     * @return 量具管理集合
     */
    public PageInfo<MeterToolListRes> selectMeterToolList(MeterToolListReq meterToolListReq);

    /**
     * 新增量具管理
     *
     * @param meterTool 量具管理
     * @return 结果
     */
    public int insertMeterTool(MeterTool meterTool);

    /**
     * 修改量具管理
     *
     * @param meterTool 量具管理
     * @return 结果
     */
    public int updateMeterTool(MeterTool meterTool);


    /**
     * 删除量具管理信息
     *
     * @param id 量具管理主键
     * @return 结果
     */
    public int deleteMeterToolById(Long id);

    int deleteMeterToolByIds(List<Long> ids);

    /**
     * 量具变更
     * @param meterChangeHistory
     * @return
     */
    int change(MeterChangeHistory meterChangeHistory);

    /**
     * 量具检定
     * @param meterDocimasy
     * @return
     */
    int docimasy(MeterDocimasy meterDocimasy);

    /**
     * 确认
     * @param meterTool
     * @return
     */
    int confirm(MeterTool meterTool);

    /**
     * 量具次数修改
     * @param meterTool
     * @return
     */
    int number(MeterTool meterTool);

    /**
     * 证书报告上传
     * @param fileList
     * @param toolId
     * @return
     */
    int uploadFile(MultipartFile[] fileList, Long toolId);

    /**
     * 量具维修记录
     * @param meterMaintainHistory
     * @return
     */
    int maintain(MeterMaintainHistory meterMaintainHistory);

    String importMaintainInfo(List<MeterToolExcel> importMaintainInfoList);

    /**
     * 批量检定量具
     * @param importMeterInfoList
     * @return
     */
    String docimasyTools(List<MeterToolExcel> importMeterInfoList);


    /**
     * 批量删除量具管理
     * @param time 需要查询的数据
     * @return 结果
     */
    List<MeterDocimasy> getInventory(Date time);

    /**
     * 获取量具基础信息
     * @param code 需要查询的量具编号
     * @return 结果
     */
    MeterTool getMeterInfo(@Param("code")String code,@Param("jobNo")String jobNo);

    /**
     * 删除时同步扭力未测数据
     * @param ids
     */
    void syncDailyTorqueUntest(List<Long> ids);

    /**
     * 修改量具时
     * @param meterTool
     */
    void syncDailyTorqueUntest(MeterTool meterTool);
}
