package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterType;

/**
 * 量具类型Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterTypeService 
{
    /**
     * 查询量具类型
     * 
     * @param id 量具类型主键
     * @return 量具类型
     */
    public MeterType selectMeterTypeById(Long id);

    /**
     * 查询量具类型列表
     * 
     * @param meterType 量具类型
     * @return 量具类型集合
     */
    public List<MeterType> selectMeterTypeList(MeterType meterType);

    /**
     * 新增量具类型
     * 
     * @param meterType 量具类型
     * @return 结果
     */
    public int insertMeterType(MeterType meterType);

    /**
     * 修改量具类型
     * 
     * @param meterType 量具类型
     * @return 结果
     */
    public int updateMeterType(MeterType meterType);


    /**
     * 删除量具类型信息
     * 
     * @param id 量具类型主键
     * @return 结果
     */
    public int deleteMeterTypeById(Long id);

    int deleteMeterTypeByIds(List<Long> ids);
}
