package com.hzforward.meter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import com.hzforward.common.core.domain.entity.SysDept;
import com.hzforward.common.core.domain.entity.SysUser;
import com.hzforward.common.utils.DateUtils;
import com.hzforward.common.utils.SecurityUtils;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.meter.domain.MeterInspection;
import com.hzforward.meter.domain.MeterInspectionReport;
import com.hzforward.meter.domain.MeterInspectionReportDetail;
import com.hzforward.meter.domain.req.MeterDocimasyForm;
import com.hzforward.meter.domain.req.MeterInspectionListReq;
import com.hzforward.meter.mapper.MeterInspectionMapper;
import com.hzforward.meter.mapper.MeterInspectionReportDetailMapper;
import com.hzforward.meter.mapper.MeterInspectionReportMapper;
import com.hzforward.meter.service.IMeterInspectionReportService;
import com.hzforward.system.mapper.SysDeptMapper;
import com.hzforward.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 量具点检报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-22
 */
@Service
@RequiredArgsConstructor
public class MeterInspectionReportServiceImpl implements IMeterInspectionReportService {

    private final MeterInspectionReportMapper meterInspectionReportMapper;
    private final MeterInspectionReportDetailMapper meterInspectionReportDetailMapper;
    private final MeterInspectionMapper meterInspectionMapper;
    private final ISysUserService sysUserService;
    private final SysDeptMapper sysDeptMapper;

    /**
     * 查询量具点检报告
     *
     * @param reportId 量具点检报告主键
     * @return 量具点检报告
     */
    @Override
    public MeterInspectionReport selectMeterInspectionReportByReportId(Long reportId) {
        return meterInspectionReportMapper.selectById(reportId);
    }

    @Override
    public List<MeterInspectionReportDetail> selectMeterInspectionReportList(MeterInspectionListReq meterInspectionListReq) {
        //定义点检报告列表
        List<MeterInspectionReportDetail> meterInspectionReportDetails = meterInspectionReportDetailMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<MeterInspectionReportDetail>()
                .eq(MeterInspectionReportDetail::getCode, meterInspectionListReq.getCode())
                .between(MeterInspectionReportDetail::getInspectionDate, StringUtils.isNotEmpty(meterInspectionListReq.getInspectionDateRange()) ? meterInspectionListReq.getInspectionDateRange().get(0) : null,
                        StringUtils.isNotEmpty(meterInspectionListReq.getInspectionDateRange()) ? DateUtils.getTodayLastTime(meterInspectionListReq.getInspectionDateRange().get(1)) : null)
                .eq(MeterInspectionReportDetail::getInspectionPerson, meterInspectionListReq.getInspectionPerson())
                .eq(MeterInspectionReportDetail::getInspectionReport, meterInspectionListReq.getInspectionReport())
                .eq(MeterInspectionReportDetail::getUsePost, meterInspectionListReq.getUsePost())
                .eq(MeterInspectionReportDetail::getDelFlag, 0)
                .orderByDesc(MeterInspectionReportDetail::getInspectionDate)
        );
        return meterInspectionReportDetails;
    }

    /**
     * 查询量具点检报告列表
     *
     * @param meterInspectionReport 量具点检报告
     * @return 量具点检报告
     */
    @Override
    public MeterInspectionReportDetail selectMeterInspectionReportDetilList(MeterInspectionReport meterInspectionReport) {
        //定义点检报告列表
        MeterInspectionReportDetail meterInspectionReportDetail = new MeterInspectionReportDetail();
        //查询点检项目列表
        List<MeterInspection> meterInspectionList = meterInspectionMapper.selectList(new LambdaQueryWrapper<MeterInspection>()
                .eq(MeterInspection::getToolId, meterInspectionReport.getToolId())
                .orderByAsc(MeterInspection::getInspectionNumber)
        );

        return meterInspectionReportDetail;
    }

    @Override
    public MeterInspectionReport getMeterInspectionReportByDate(MeterInspectionReport meterInspectionReport) {
//        MeterInspectionReport returnInspectionReport = meterInspectionReportMapper.selectOne(new LambdaQueryWrapper<MeterInspectionReport>().
//                eq(MeterInspectionReport::getToolId,meterInspectionReport.getToolId())
//                .eq(MeterInspectionReport::getInspectionDate,meterInspectionReport.getInspectionDate())
//        );
//
//        if (StringUtils.isNull(returnInspectionReport)){
//            returnInspectionReport = new MeterInspectionReport();
//        }else{
//            meterInspectionReport.setReportId(returnInspectionReport.getReportId());
//        }
//
//        returnInspectionReport.setMeterInspectionReportDetails(selectMeterInspectionReportDetilList(meterInspectionReport));
//
        return null;
    }


    /**
     * 新增量具点检报告
     *
     * @param meterInspectionReport 量具点检报告
     * @return 结果
     */
    @Override
    public int insertMeterInspectionReport(MeterInspectionReport meterInspectionReport) {
        SysUser meterUser = sysUserService.selectUserListByJobNo(meterInspectionReport.getUserName());

        SysDept dept = sysDeptMapper.selectOne(
                new LambdaQueryWrapper<SysDept>()
                        .eq(SysDept::getDeptId, meterUser.getDeptId())
        );
        meterInspectionReport.setUsePost(dept.getDeptName());
        return meterInspectionReportMapper.insert(meterInspectionReport);
    }

    /**
     * 修改量具点检报告
     *
     * @param meterInspectionReport 量具点检报告
     * @return 结果
     */
    @Override
    public int updateMeterInspectionReport(MeterInspectionReport meterInspectionReport) {
        return meterInspectionReportMapper.updateById(meterInspectionReport);
    }

    /**
     * 删除量具点检报告
     *
     * @param reportIds 需要删除的量具点检报告主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMeterInspectionReportByReportIds(Long[] reportIds) {
        for (Long reportId : reportIds) {
            MeterInspectionReportDetail meterInspectionReport = new MeterInspectionReportDetail();
            meterInspectionReport.setReportDetailId(reportId);
            meterInspectionReport.setDeleteTime(DateUtils.getNowDate());
            meterInspectionReport.setDeleteBy(SecurityUtils.getLoginUser().getUsername());
            meterInspectionReportDetailMapper.updateById(meterInspectionReport);
            meterInspectionReportDetailMapper.deleteById(meterInspectionReport);
        }
        return 1;
    }

    /**
     * 新增设备点检报告详情
     *
     * @param meterInspectionReportDetail 量具点检报告
     */
    public void insertMeterInspectionReportDetail(MeterInspectionReportDetail meterInspectionReportDetail) {
        meterInspectionReportDetail.setInspectionDate(DateUtils.getNowDate());
        meterInspectionReportDetailMapper.insert(meterInspectionReportDetail);
    }

    /**
     * 修改量具点检报告详情
     *
     * @param meterInspectionReportDetail 设备点检报告
     */
    public void updateMeterInspectionReportDetail(MeterInspectionReportDetail meterInspectionReportDetail) {
        meterInspectionReportDetailMapper.updateById(meterInspectionReportDetail);
    }

    /**
     * 处理移动端点检请求
     *
     * @param meterDocimasyForm 量具点检报告表单
     * @return 结果
     */
    @Override
    public int meterInspectionReportSubmit(MeterDocimasyForm meterDocimasyForm) {
        MeterInspectionReportDetail meterInspectionReportDetail = new MeterInspectionReportDetail();
        BeanUtils.copyProperties(meterDocimasyForm, meterInspectionReportDetail);
        SysUser meterUser = sysUserService.selectDingUserByJobNo(meterInspectionReportDetail.getUserName());
        SysDept dept = sysDeptMapper.selectOne(
                new LambdaQueryWrapper<SysDept>()
                        .eq(SysDept::getDeptId, meterUser.getDeptId())
        );
        meterInspectionReportDetail.setUsePost(dept.getDeptName());
        meterInspectionReportDetail.setInspectionPerson(meterUser.getNickName());
        insertMeterInspectionReportDetail(meterInspectionReportDetail);
        return 1;
    }

    @Override
    public List<MeterInspectionReportDetail> selectMeterInspectionReportDetailList(MeterInspectionReport meterInspectionReport) {
//        //定义点检报告列表
//        ArrayList<MeterInspectionReportDetail> meterInspectionReportDetailList = new ArrayList<>();
//        //查询点检项目列表
//        List<MeterInspection> meterInspectionList = meterInspectionMapper.selectList(new LambdaQueryWrapper<MeterInspection>()
//                .eq(MeterInspection::getToolId,meterInspectionReport.getToolId())
//                .orderByAsc(MeterInspection::getInspectionNumber)
//        );
//        for(MeterInspection meterInspection : meterInspectionList){
//            //定义每条项目的结果
//            MeterInspectionReportDetail meterInspectionReportDetail = null;
//            if(StringUtils.isNotNull(meterInspectionReport.getReportId())) {
//                //查询报告详情
//                meterInspectionReportDetail = meterInspectionReportDetailMapper.selectOne(new LambdaQueryWrapper<MeterInspectionReportDetail>()
//                        .eq(MeterInspectionReportDetail::getInspectionId, meterInspection.getInspectionId())
//                        .eq(MeterInspectionReportDetail::getReportId, meterInspectionReport.getReportId())
//                );
//                //将字符串形式储存的图片地址转换为List返回前端
//                if (StringUtils.isNotNull(meterInspectionReportDetail) && StringUtils.isNotEmpty(meterInspectionReportDetail.getUploadImagePaths())) {
//                    List<Map<String, String>> imagePathMapList = new ArrayList<>();
//                    for (String imagePath : meterInspectionReportDetail.getUploadImagePaths().split(",")) {
//                        Map<String, String> imagePathMap = new HashMap<>();
//                        imagePathMap.put("path", imagePath);
//                        imagePathMap.put("status", "success");
//                        imagePathMapList.add(imagePathMap);
//                    }
//                    meterInspectionReportDetail.setUploadImagePathsList(imagePathMapList);
//                }
//            }
//            if(StringUtils.isNull(meterInspectionReportDetail)){
//                meterInspectionReportDetail = new MeterInspectionReportDetail();
//            }
//            //设置点检项目和点检内容详情
//            meterInspectionReportDetail.setMeterInspectionInfo(meterInspection);
//            meterInspectionReportDetailList.add(meterInspectionReportDetail);
//        }
//        return meterInspectionReportDetailList;
        return null;
    }
}
