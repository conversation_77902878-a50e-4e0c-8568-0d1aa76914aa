package com.hzforward.meter.service.impl;


import java.util.List;
import com.hzforward.common.core.dao.exclude.ExcludeEmptyLambdaQueryWrapper;
import javax.annotation.Resource;

import com.hzforward.meter.domain.MeterDocimasyChange;
import com.hzforward.meter.mapper.MeterDocimasyChangeMapper;
import com.hzforward.meter.service.IMeterDocimasyChangeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 量具检定履历记录修改 Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Service
public class MeterDocimasyChangeServiceImpl implements IMeterDocimasyChangeService
{
    @Resource
    private MeterDocimasyChangeMapper meterDocimasyChangeMapper;

    /**
     * 查询量具检定
     *
     * @param id 量具检定主键
     * @return 量具检定
     */
    @Override
    public MeterDocimasyChange selectMeterDocimasyChangeById(Long id)
    {
        return meterDocimasyChangeMapper.selectById(id);
    }

    /**
     * 查询量具检定列表
     *
     * @param meterDocimasyChange 量具检定
     * @return 量具检定
     */
    @Override
    public List<MeterDocimasyChange> selectMeterDocimasyChangeList(MeterDocimasyChange meterDocimasyChange)
    {
        return meterDocimasyChangeMapper.selectList(new ExcludeEmptyLambdaQueryWrapper<MeterDocimasyChange>()
                .eq(MeterDocimasyChange::getId,meterDocimasyChange.getId())
                .eq(MeterDocimasyChange::getToolCode,meterDocimasyChange.getToolCode())
        );
    }

    /**
     * 新增量具检定
     *
     * @param meterDocimasyChange 量具检定
     * @return 结果
     */
    @Override
    public int insertMeterDocimasyChange(MeterDocimasyChange meterDocimasyChange)
    {
        return meterDocimasyChangeMapper.insert(meterDocimasyChange);
    }

    /**
     * 修改量具检定
     *
     * @param meterDocimasyChange 量具检定
     * @return 结果
     */
    @Override
    public int updateMeterDocimasyChange(MeterDocimasyChange meterDocimasyChange)
    {
        return meterDocimasyChangeMapper.updateById(meterDocimasyChange);
    }

    /**
     * 删除量具检定
     *
     * @param ids 需要删除的量具检定主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMeterDocimasyChangeByIds(List<Long> ids)
    {
        return meterDocimasyChangeMapper.deleteBatchIds(ids);
    }

}
