package com.hzforward.meter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzforward.common.config.OperationConfig;
import com.hzforward.common.exception.ServiceException;
import com.hzforward.common.utils.file.FileUtils;
import com.hzforward.meter.domain.MeterDocimasyCertificateReport;
import com.hzforward.meter.mapper.MeterDocimasyCertificateReportMapper;
import com.hzforward.meter.service.IMeterDocimasyCertificateReportService;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 量具检定证书报告Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Service
public class MeterDocimasyCertificateReportServiceImpl implements IMeterDocimasyCertificateReportService {
    @Resource
    private MeterDocimasyCertificateReportMapper meterDocimasyCertificateReportMapper;

    /**
     * 查询量具检定证书报告
     *
     * @param id 量具检定证书报告主键
     * @return 量具检定证书报告
     */
    @Override
    public MeterDocimasyCertificateReport selectMeterDocimasyCertificateReportById(Long id) {
        return meterDocimasyCertificateReportMapper.selectById(id);
    }

    /**
     * 查询量具检定证书报告列表
     *
     * @param meterDocimasyCertificateReport 量具检定证书报告
     * @return 量具检定证书报告
     */
    @Override
    public List<MeterDocimasyCertificateReport> selectMeterDocimasyCertificateReportList(MeterDocimasyCertificateReport meterDocimasyCertificateReport) {
        return meterDocimasyCertificateReportMapper.selectList(new LambdaQueryWrapper<MeterDocimasyCertificateReport>()
                .eq(MeterDocimasyCertificateReport::getDocimasyId, meterDocimasyCertificateReport.getDocimasyId()));
    }

    /**
     * 新增量具检定证书报告
     *
     * @param meterDocimasyCertificateReport 量具检定证书报告
     * @return 结果
     */
    @Override
    public int insertMeterDocimasyCertificateReport(MeterDocimasyCertificateReport meterDocimasyCertificateReport) {
        return meterDocimasyCertificateReportMapper.insert(meterDocimasyCertificateReport);
    }

    /**
     * 修改量具检定证书报告
     *
     * @param meterDocimasyCertificateReport 量具检定证书报告
     * @return 结果
     */
    @Override
    public int updateMeterDocimasyCertificateReport(MeterDocimasyCertificateReport meterDocimasyCertificateReport) {
        return meterDocimasyCertificateReportMapper.updateById(meterDocimasyCertificateReport);
    }

    /**
     * 删除量具检定证书报告信息
     *
     * @param id 量具检定证书报告主键
     * @return 结果
     */
    @Override
    public int deleteMeterDocimasyCertificateReportById(Long id) {
        return meterDocimasyCertificateReportMapper.deleteById(id);
    }

    @Override
    public int deleteMeterDocimasyCertificateReportByIds(List<Long> ids) {
        return meterDocimasyCertificateReportMapper.deleteBatchIds(ids);
    }

    /**
     * 证书报告下载
     * @param response
     * @param id
     */
    @Override
    public void downLoad(HttpServletResponse response, Long id) {
        try {
            MeterDocimasyCertificateReport report = meterDocimasyCertificateReportMapper.selectById(id);
            String filePath = OperationConfig.getProfile()  + report.getPath();
            File file = new File(filePath);

            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, file.getName());
            FileUtils.writeBytes(filePath, response.getOutputStream());
        } catch (IOException e) {
            throw new ServiceException();
        }
    }
}
