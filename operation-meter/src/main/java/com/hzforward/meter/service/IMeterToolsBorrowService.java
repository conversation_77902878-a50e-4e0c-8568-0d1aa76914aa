package com.hzforward.meter.service;

import java.util.List;

import com.hzforward.meter.domain.MeterToolsBorrow;
import com.hzforward.meter.domain.req.MeterToolsBorrowReq;
import com.hzforward.meter.domain.res.MeterToolsBorrowRes;
import org.apache.ibatis.annotations.Param;

/**
 * 量具借用报告Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-15
 */
public interface IMeterToolsBorrowService 
{
    /**
     * 查询量具借用报告
     * 
     * @param id 量具借用报告主键
     * @return 量具借用报告
     */
    public MeterToolsBorrow selectMeterToolsBorrowById(Long id);

    /**
     * 查询量具借用报告列表
     * 
     * @param meterToolsBorrow 量具借用报告
     * @return 量具借用报告集合
     */
    public List<MeterToolsBorrow> selectMeterToolsBorrowList(MeterToolsBorrow meterToolsBorrow);

    /**
     * 新增量具借用报告
     * 
     * @param meterToolsBorrow 量具借用报告
     * @return 结果
     */
    public int insertMeterToolsBorrow(MeterToolsBorrow meterToolsBorrow);

    /**
     * 修改量具借用报告
     * 
     * @param meterToolsBorrow 量具借用报告
     * @return 结果
     */
    public int updateMeterToolsBorrow(MeterToolsBorrow meterToolsBorrow);

    /**
     * 删除量具借用报告
     * 
     * @param ids 需要删除的量具借用报告主键集合
     * @return 结果
     */
    public int deleteMeterToolsBorrowByIds(List<Long> ids);

    /**
     * 获取量具基础信息
     * @param code 需要查询的量具编号
     * @return 结果
     */
    MeterToolsBorrowRes getMeterInfo(@Param("code")String code, @Param("jobNo")String jobNo);

    /**
     * 处理量具借阅客户端借用功能
     * @param meterToolsBorrowReq
     * @return
     */
    int borrowToolSubmit(MeterToolsBorrowReq meterToolsBorrowReq);
    /**
     * 处理量具借阅客户端归还功能
     * @param meterToolsBorrowReq
     * @return
     */
    int returnSubmit(MeterToolsBorrowReq meterToolsBorrowReq);

    /**
     * 更新未归还的量具密钥
     * @return
     */
    int updateSecretKey();

}
