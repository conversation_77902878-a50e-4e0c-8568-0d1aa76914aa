package com.hzforward.meter.service;

import com.github.pagehelper.PageInfo;
import com.hzforward.meter.domain.MeterMaintainHistory;
import com.hzforward.meter.domain.req.MeterMaintainHistoryListReq;
import com.hzforward.meter.domain.res.MeterMaintainHistoryListRes;

import java.util.List;

/**
 * 量具维修记录Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-10
 */
public interface IMeterMaintainHistoryService 
{
    /**
     * 查询量具维修记录
     * 
     * @param id 量具维修记录主键
     * @return 量具维修记录
     */
    public MeterMaintainHistory selectMeterMaintainHistoryById(Long id);

    /**
     * 查询量具维修记录列表
     * 
     * @param meterMaintainHistoryListReq 量具维修记录
     * @return 量具维修记录集合
     */
    public PageInfo<MeterMaintainHistoryListRes> selectMeterMaintainHistoryList(MeterMaintainHistoryListReq meterMaintainHistoryListReq);

    /**
     * 新增量具维修记录
     * 
     * @param meterMaintainHistory 量具维修记录
     * @return 结果
     */
    public int insertMeterMaintainHistory(MeterMaintainHistory meterMaintainHistory);

    /**
     * 修改量具维修记录
     * 
     * @param meterMaintainHistory 量具维修记录
     * @return 结果
     */
    public int updateMeterMaintainHistory(MeterMaintainHistory meterMaintainHistory);

    /**
     * 批量删除量具维修记录
     * 
     * @param ids 需要删除的量具维修记录主键集合
     * @return 结果
     */
    public int deleteMeterMaintainHistoryByIds(List<Long> ids);

    /**
     * 删除量具维修记录信息
     * 
     * @param id 量具维修记录主键
     * @return 结果
     */
    public int deleteMeterMaintainHistoryById(Long id);
}
