package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterDocimasyCertificateReport;

import javax.servlet.http.HttpServletResponse;

/**
 * 量具检定证书报告Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterDocimasyCertificateReportService 
{
    /**
     * 查询量具检定证书报告
     * 
     * @param id 量具检定证书报告主键
     * @return 量具检定证书报告
     */
    public MeterDocimasyCertificateReport selectMeterDocimasyCertificateReportById(Long id);

    /**
     * 查询量具检定证书报告列表
     * 
     * @param meterDocimasyCertificateReport 量具检定证书报告
     * @return 量具检定证书报告集合
     */
    public List<MeterDocimasyCertificateReport> selectMeterDocimasyCertificateReportList(MeterDocimasyCertificateReport meterDocimasyCertificateReport);

    /**
     * 新增量具检定证书报告
     * 
     * @param meterDocimasyCertificateReport 量具检定证书报告
     * @return 结果
     */
    public int insertMeterDocimasyCertificateReport(MeterDocimasyCertificateReport meterDocimasyCertificateReport);

    /**
     * 修改量具检定证书报告
     * 
     * @param meterDocimasyCertificateReport 量具检定证书报告
     * @return 结果
     */
    public int updateMeterDocimasyCertificateReport(MeterDocimasyCertificateReport meterDocimasyCertificateReport);

    /**
     * 删除量具检定证书报告信息
     * 
     * @param id 量具检定证书报告主键
     * @return 结果
     */
    public int deleteMeterDocimasyCertificateReportById(Long id);

    int deleteMeterDocimasyCertificateReportByIds(List<Long> ids);

    /**
     * 证书报告下载
     * @param response
     * @param id
     */
    void downLoad(HttpServletResponse response, Long id);
}
