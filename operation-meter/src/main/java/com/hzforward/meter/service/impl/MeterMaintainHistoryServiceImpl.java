package com.hzforward.meter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.hzforward.common.utils.StringUtils;
import com.hzforward.common.utils.bean.BeanUtils;
import com.hzforward.meter.domain.MeterMaintainHistory;
import com.hzforward.meter.domain.MeterTool;
import com.hzforward.meter.domain.req.MeterMaintainHistoryListReq;
import com.hzforward.meter.domain.res.MeterMaintainHistoryListRes;
import com.hzforward.meter.mapper.MeterMaintainHistoryMapper;
import com.hzforward.meter.mapper.MeterToolMapper;
import com.hzforward.meter.service.IMeterMaintainHistoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 量具维修记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-10
 */
@Service
public class MeterMaintainHistoryServiceImpl implements IMeterMaintainHistoryService {

    @Resource
    private MeterMaintainHistoryMapper meterMaintainHistoryMapper;
    @Resource
    private MeterToolMapper meterToolMapper;

    /**
     * 查询量具维修记录
     *
     * @param id 量具维修记录主键
     * @return 量具维修记录
     */
    @Override
    public MeterMaintainHistory selectMeterMaintainHistoryById(Long id) {
        MeterMaintainHistory detail = meterMaintainHistoryMapper.selectById(id);
        MeterMaintainHistoryListRes detailRes = BeanUtils.copyBeanPropAndReturn(detail, MeterMaintainHistoryListRes.class);
        MeterTool tool = meterToolMapper.selectById(detail.getToolId());
        detailRes.setCode(tool.getCode());
        detailRes.setName(tool.getName());
        return detailRes;
    }

    /**
     * 查询量具维修记录列表
     *
     * @param meterMaintainHistoryListReq 量具维修记录
     * @return 量具维修记录
     */
    @Override
    public PageInfo<MeterMaintainHistoryListRes> selectMeterMaintainHistoryList(MeterMaintainHistoryListReq meterMaintainHistoryListReq) {
        List<MeterMaintainHistory> list = meterMaintainHistoryMapper.selectList(new LambdaQueryWrapper<>());
        if (StringUtils.isEmpty(list)) {
            return new PageInfo<>();
        }
        PageInfo<MeterMaintainHistoryListRes> resList = BeanUtils.copyBeanPropAndReturnPage(list, MeterMaintainHistoryListRes.class);
        List<Long> toolIds = list.stream().map(MeterMaintainHistory::getToolId).collect(Collectors.toList());
        List<MeterTool> tools = meterToolMapper.selectBatchIds(toolIds);
        Map<Long, MeterTool> toolMap = tools.stream().collect(Collectors.toMap(MeterTool::getId, meterTool -> meterTool));

        for (MeterMaintainHistoryListRes meterMaintainHistoryListRes : resList.getList()) {
            meterMaintainHistoryListRes.setCode(StringUtils.isNotNull(toolMap.get(meterMaintainHistoryListRes.getToolId())) ? toolMap.get(meterMaintainHistoryListRes.getToolId()).getCode() : null);
            meterMaintainHistoryListRes.setName(StringUtils.isNotNull(toolMap.get(meterMaintainHistoryListRes.getToolId())) ? toolMap.get(meterMaintainHistoryListRes.getToolId()).getName() : null);
        }
        return resList;
    }

    /**
     * 新增量具维修记录
     *
     * @param meterMaintainHistory 量具维修记录
     * @return 结果
     */
    @Override
    public int insertMeterMaintainHistory(MeterMaintainHistory meterMaintainHistory) {
        return meterMaintainHistoryMapper.insert(meterMaintainHistory);
    }

    /**
     * 修改量具维修记录
     *
     * @param meterMaintainHistory 量具维修记录
     * @return 结果
     */
    @Override
    public int updateMeterMaintainHistory(MeterMaintainHistory meterMaintainHistory) {
        return meterMaintainHistoryMapper.updateById(meterMaintainHistory);
    }

    /**
     * 批量删除量具维修记录
     *
     * @param ids 需要删除的量具维修记录主键
     * @return 结果
     */
    @Override
    public int deleteMeterMaintainHistoryByIds(List<Long> ids) {
        return meterMaintainHistoryMapper.deleteBatchIds(ids);
    }

    /**
     * 删除量具维修记录信息
     *
     * @param id 量具维修记录主键
     * @return 结果
     */
    @Override
    public int deleteMeterMaintainHistoryById(Long id) {
        return meterMaintainHistoryMapper.deleteById(id);
    }
}
