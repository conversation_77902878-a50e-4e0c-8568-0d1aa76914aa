package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterDocimasyCompany;

/**
 * 检定单位Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterDocimasyCompanyService 
{
    /**
     * 查询检定单位
     * 
     * @param id 检定单位主键
     * @return 检定单位
     */
    public MeterDocimasyCompany selectMeterDocimasyCompanyById(Long id);

    /**
     * 查询检定单位列表
     * 
     * @param meterDocimasyCompany 检定单位
     * @return 检定单位集合
     */
    public List<MeterDocimasyCompany> selectMeterDocimasyCompanyList(MeterDocimasyCompany meterDocimasyCompany);

    /**
     * 新增检定单位
     * 
     * @param meterDocimasyCompany 检定单位
     * @return 结果
     */
    public int insertMeterDocimasyCompany(MeterDocimasyCompany meterDocimasyCompany);

    /**
     * 修改检定单位
     * 
     * @param meterDocimasyCompany 检定单位
     * @return 结果
     */
    public int updateMeterDocimasyCompany(MeterDocimasyCompany meterDocimasyCompany);


    /**
     * 删除检定单位信息
     * 
     * @param id 检定单位主键
     * @return 结果
     */
    public int deleteMeterDocimasyCompanyById(Long id);

    int deleteMeterDocimasyCompanyByIds(List<Long> ids);
}
