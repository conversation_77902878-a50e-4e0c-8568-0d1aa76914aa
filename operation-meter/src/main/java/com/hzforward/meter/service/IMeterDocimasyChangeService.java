package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterDocimasyChange;

/**
 * 量具检定履历记录修改 Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-28
 */
public interface IMeterDocimasyChangeService 
{
    /**
     * 查询量具检定
     * 
     * @param id 量具检定主键
     * @return 量具检定
     */
    public MeterDocimasyChange selectMeterDocimasyChangeById(Long id);

    /**
     * 查询量具检定列表
     * 
     * @param meterDocimasyChange 量具检定
     * @return 量具检定集合
     */
    public List<MeterDocimasyChange> selectMeterDocimasyChangeList(MeterDocimasyChange meterDocimasyChange);

    /**
     * 新增量具检定
     * 
     * @param meterDocimasyChange 量具检定
     * @return 结果
     */
    public int insertMeterDocimasyChange(MeterDocimasyChange meterDocimasyChange);

    /**
     * 修改量具检定
     * 
     * @param meterDocimasyChange 量具检定
     * @return 结果
     */
    public int updateMeterDocimasyChange(MeterDocimasyChange meterDocimasyChange);

    /**
     * 删除量具检定
     * 
     * @param ids 需要删除的量具检定主键集合
     * @return 结果
     */
    public int deleteMeterDocimasyChangeByIds(List<Long> ids);

}
