package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterSpotCheck;

/**
 * 量具检定履历Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-12
 */
public interface IMeterSpotCheckService 
{
    /**
     * 查询量具检定履历
     * 
     * @param id 量具检定履历主键
     * @return 量具检定履历
     */
    public MeterSpotCheck selectMeterSpotCheckById(Long id);

    /**
     * 查询量具检定履历列表
     * 
     * @param meterSpotCheck 量具检定履历
     * @return 量具检定履历集合
     */
    public List<MeterSpotCheck> selectMeterSpotCheckList(MeterSpotCheck meterSpotCheck);

    /**
     * 新增量具检定履历
     * 
     * @param meterSpotCheck 量具检定履历
     * @return 结果
     */
    public int insertMeterSpotCheck(MeterSpotCheck meterSpotCheck);

    /**
     * 修改量具检定履历
     * 
     * @param meterSpotCheck 量具检定履历
     * @return 结果
     */
    public int updateMeterSpotCheck(MeterSpotCheck meterSpotCheck);

    /**
     * 删除量具检定履历
     * 
     * @param ids 需要删除的量具检定履历主键集合
     * @return 结果
     */
    public int deleteMeterSpotCheckByIds(List<Long> ids);

}
