package com.hzforward.meter.service;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.hzforward.meter.domain.MeterTorqueTest;
import com.hzforward.meter.domain.req.MeterTorqueTestListReq;
import com.hzforward.meter.domain.res.MeterNotTestListRes;
import com.hzforward.meter.domain.res.MeterTorqueTestListRes;
import com.hzforward.meter.domain.res.MeterTorqueUnTestListRes;

/**
 * 扭力测试Service接口
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterTorqueTestService
{
    /**
     * 查询扭力测试
     *
     * @param id 扭力测试主键
     * @return 扭力测试
     */
    public MeterTorqueTest selectMeterTorqueTestById(Long id);

    /**
     * 查询扭力测试列表
     *
     * @param meterTorqueTestListReq 扭力测试
     * @return 扭力测试集合
     */
    public PageInfo<MeterTorqueTestListRes> selectMeterTorqueTestList(MeterTorqueTestListReq meterTorqueTestListReq,Integer pageNum,Integer pageSize);

    /**
     * 新增扭力测试
     *
     * @param meterTorqueTest 扭力测试
     * @return 结果
     */
    public int insertMeterTorqueTest(MeterTorqueTest meterTorqueTest);

    /**
     * 修改扭力测试
     *
     * @param meterTorqueTest 扭力测试
     * @return 结果
     */
    public int updateMeterTorqueTest(MeterTorqueTest meterTorqueTest);


    /**
     * 删除扭力测试信息
     *
     * @param id 扭力测试主键
     * @return 结果
     */
    public int deleteMeterTorqueTestById(Long id);

    int deleteMeterTorqueTestByIds(List<Long> ids);

    /**
     * 未测试列表
     * @param meterTorqueTestListReq
     * @return
     */
    PageInfo<MeterNotTestListRes> testList(MeterTorqueTestListReq meterTorqueTestListReq);

    /**
     * 导入扭力测试数据
     * @param meterTorqueTestList
     * @return
     */
    String importMeterTorqueTest(List<MeterTorqueTest> meterTorqueTestList);

    List<MeterTorqueUnTestListRes> exportMeterNotTorqueTest(MeterTorqueTestListReq meterTorqueTestListReq, Integer pageNum, Integer pageSize);

    List<MeterTorqueTestListRes> exportMeterTorqueTest(MeterTorqueTestListReq meterTorqueTestListReq);

    void sendUnMeterTorqueTestMail();

    void updateDailyUnMeterTorqueTest();
}
