package com.hzforward.meter.service;

import java.util.List;
import com.hzforward.meter.domain.MeterFirstProof;
import com.hzforward.meter.domain.req.MeterFirstProofListReq;

/**
 * 量具内校Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-01
 */
public interface IMeterFirstProofService 
{
    /**
     * 查询量具内校
     * 
     * @param id 量具内校主键
     * @return 量具内校
     */
    public MeterFirstProof selectMeterFirstProofById(Long id);

    /**
     * 查询量具内校列表
     * 
     * @param meterFirstProofListReq 量具内校
     * @return 量具内校集合
     */
    public List<MeterFirstProof> selectMeterFirstProofList(MeterFirstProofListReq meterFirstProofListReq);

    /**
     * 新增量具内校
     * 
     * @param meterFirstProof 量具内校
     * @return 结果
     */
    public int insertMeterFirstProof(MeterFirstProof meterFirstProof);

    /**
     * 修改量具内校
     * 
     * @param meterFirstProof 量具内校
     * @return 结果
     */
    public int updateMeterFirstProof(MeterFirstProof meterFirstProof);

    /**
     * 删除量具内校信息
     * 
     * @param id 量具内校主键
     * @return 结果
     */
    public int deleteMeterFirstProofById(Long id);

    int deleteMeterFirstProofByIds(List<Long> ids);
}
