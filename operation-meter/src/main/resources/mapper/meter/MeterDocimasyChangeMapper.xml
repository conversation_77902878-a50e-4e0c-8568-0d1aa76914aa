<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.newtorename.mapper.MeterDocimasyChangeMapper">

    <resultMap type="MeterDocimasyChange" id="MeterDocimasyChangeResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="operator"    column="operator"    />
        <result property="remark"    column="remark"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="docimasyTime"    column="docimasy_time"    />
        <result property="docimasyCompany"    column="docimasy_company"    />
        <result property="docimasyStatus"    column="docimasy_status"    />
        <result property="docimasyPeriod"    column="docimasy_period"    />
        <result property="endTime"    column="end_time"    />
    </resultMap>

    <sql id="selectMeterDocimasyChange">
        select id, create_time, update_time, delete_time, create_by, update_by, delete_by,operator, remark, code, name, docimasy_time, docimasy_company, docimasy_status, docimasy_period, end_time from meter_docimasy_change
    </sql>

    <select id="selectMeterDocimasyList" parameterType="MeterDocimasyChange" resultMap="MeterDocimasyChangeResult">
        <include refid="selectMeterDocimasyChange"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="docimasyTime != null "> and docimasy_time = #{docimasyTime}</if>
            <if test="docimasyCompany != null  and docimasyCompany != ''"> and docimasy_company = #{docimasyCompany}</if>
            <if test="docimasyStatus != null "> and docimasy_status = #{docimasyStatus}</if>
            <if test="docimasyPeriod != null "> and docimasy_period = #{docimasyPeriod}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
    </select>

    <select id="selectMeterDocimasyChangeById" parameterType="Long" resultMap="MeterDocimasyChangeResult">
        <include refid="selectMeterDocimasyChange"/>
        where id = #{id}
    </select>

    <insert id="insertMeterDocimasyChange" parameterType="MeterDocimasyChange" useGeneratedKeys="true" keyProperty="id">
        insert into meter_docimasy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="remark != null">remark,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="docimasyTime != null">docimasy_time,</if>
            <if test="docimasyCompany != null">docimasy_company,</if>
            <if test="docimasyStatus != null">docimasy_status,</if>
            <if test="docimasyPeriod != null">docimasy_period,</if>
            <if test="endTime != null">end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="docimasyTime != null">#{docimasyTime},</if>
            <if test="docimasyCompany != null">#{docimasyCompany},</if>
            <if test="docimasyStatus != null">#{docimasyStatus},</if>
            <if test="docimasyPeriod != null">#{docimasyPeriod},</if>
            <if test="endTime != null">#{endTime},</if>
        </trim>
    </insert>

    <update id="updateMeterDocimasyChange" parameterType="MeterDocimasyChange">
        update meter_docimasy
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="docimasyTime != null">docimasy_time = #{docimasyTime},</if>
            <if test="docimasyCompany != null">docimasy_company = #{docimasyCompany},</if>
            <if test="docimasyStatus != null">docimasy_status = #{docimasyStatus},</if>
            <if test="docimasyPeriod != null">docimasy_period = #{docimasyPeriod},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMeterDocimasyChangeById" parameterType="Long">
        update meter_docimasy set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteMeterDocimasyChangeByIds" parameterType="String">
        update meter_docimasy set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>