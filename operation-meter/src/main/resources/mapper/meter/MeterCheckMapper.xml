<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.meter.mapper.MeterCheckMapper">
    
    <resultMap type="MeterCheck" id="MeterCheckResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="remark"    column="remark"    />
        <result property="code"    column="code"    />
    </resultMap>

    <sql id="selectMeterCheckVo">
        select id, create_time, update_time, delete_time, create_by, update_by, delete_by, remark, code from meter_check
    </sql>

    <select id="selectMeterCheckList" parameterType="MeterCheck" resultMap="MeterCheckResult">
        <include refid="selectMeterCheckVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
        </where>
    </select>
    
    <select id="selectMeterCheckById" parameterType="Long" resultMap="MeterCheckResult">
        <include refid="selectMeterCheckVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMeterCheck" parameterType="MeterCheck" useGeneratedKeys="true" keyProperty="id">
        insert into meter_check
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="remark != null">remark,</if>
            <if test="code != null">code,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="code != null">#{code},</if>
         </trim>
    </insert>

    <update id="updateMeterCheck" parameterType="MeterCheck">
        update meter_check
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="code != null">code = #{code},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMeterCheckById" parameterType="Long">
        update meter_check set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteMeterCheckByIds" parameterType="String">
        update meter_check set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>