<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.meter.mapper.MeterToolMapper">

    <resultMap type="MeterTool" id="MeterToolResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="remark"    column="remark"    />
        <result property="code"    column="code"    />
        <result property="typeId"    column="type_id"    />
        <result property="name"    column="name"    />
        <result property="modelNo"    column="model_no"    />
        <result property="scope"    column="scope"    />
        <result property="factoryNo"    column="factory_no"    />
        <result property="produceCompany"    column="produce_company"    />
        <result property="accuracy"    column="accuracy"    />
        <result property="payTime"    column="pay_time"    />
        <result property="stashDept"    column="stash_dept"    />
        <result property="stashGroup"    column="stash_group"    />
        <result property="usePost"    column="use_post"    />
        <result property="safeguardPerson"    column="safeguard_person"    />
        <result property="torqueForceUseScope"    column="torque_force_use_scope"    />
        <result property="meterStatus"    column="meter_status"    />
        <result property="checkPeriod"    column="check_period"    />
        <result property="checkTime"    column="check_time"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="effectiveTime"    column="effective_time"    />
        <result property="nextCheckTime"    column="next_check_time"    />
        <result property="firstProofPeriod"    column="first_proof_period"    />
        <result property="torqueForceTestScope"    column="torque_force_test_scope"    />
        <result property="torqueForce"    column="torque_force"    />
        <result property="maxPermitError"    column="max_permit_error"    />
        <result property="calibrationIndicatingError"    column="calibration_indicating_error"    />
        <result property="calibrationUncertainty"    column="calibration_uncertainty"    />
        <result property="confirmJudgmentConclusion"    column="confirm_judgment_conclusion"    />
        <result property="confirmDate"    column="confirm_date"    />
        <result property="damageNo"    column="damage_no"    />
        <result property="maintainNo"    column="maintain_no"    />
        <result property="discardNo"    column="discard_no"    />
    </resultMap>

    <sql id="selectMeterToolVo">
        select id, create_time, update_time, delete_time, create_by, update_by, delete_by, remark, code, type_id, name, model_no, scope, factory_no, produce_company, accuracy, pay_time, stash_dept, stash_group, use_post, safeguard_person, torque_force_use_scope, meter_status,docimasy_status, docheck_period, check_time, check_status, effective_time, next_check_time, first_proof_period, torque_force_test_scope, torque_force, max_permit_error, calibration_indicating_error, calibration_uncertainty, confirm_judgment_conclusion, confirm_date, damage_no, maintain_no, discard_no from meter_tool
    </sql>

    <sql id="selectMeterTool">
        select id, create_time, update_time, create_by,remark, code, type_id, name, model_no, scope, factory_no, produce_company, accuracy, pay_time, safeguard_person, meter_status, docimasy_status,effective_time,docimasy_time,docimasy_company_code,next_check_time from meter_tool
    </sql>

    <select id="selectMeterToolList" parameterType="MeterTool" resultMap="MeterToolResult">
        <include refid="selectMeterToolVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="typeId != null "> and type_id = #{typeId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="modelNo != null  and modelNo != ''"> and model_no = #{modelNo}</if>
            <if test="scope != null  and scope != ''"> and scope = #{scope}</if>
            <if test="factoryNo != null  and factoryNo != ''"> and factory_no = #{factoryNo}</if>
            <if test="produceCompany != null  and produceCompany != ''"> and produce_company = #{produceCompany}</if>
            <if test="accuracy != null  and accuracy != ''"> and accuracy = #{accuracy}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="stashDept != null  and stashDept != ''"> and stash_dept = #{stashDept}</if>
            <if test="stashGroup != null  and stashGroup != ''"> and stash_group = #{stashGroup}</if>
            <if test="usePost != null  and usePost != ''"> and use_post = #{usePost}</if>
            <if test="safeguardPerson != null  and safeguardPerson != ''"> and safeguard_person = #{safeguardPerson}</if>
            <if test="torqueForceUseScope != null  and torqueForceUseScope != ''"> and torque_force_use_scope = #{torqueForceUseScope}</if>
            <if test="meterStatus != null "> and meter_status = #{meterStatus}</if>
            <if test="checkPeriod != null "> and check_period = #{checkPeriod}</if>
            <if test="checkTime != null "> and check_time = #{checkTime}</if>
            <if test="checkStatus != null "> and check_status = #{checkStatus}</if>
            <if test="effectiveTime != null "> and effective_time = #{effectiveTime}</if>
            <if test="nextCheckTime != null "> and next_check_time = #{nextCheckTime}</if>
            <if test="firstProofPeriod != null  and firstProofPeriod != ''"> and first_proof_period = #{firstProofPeriod}</if>
            <if test="torqueForceTestScope != null "> and torque_force_test_scope = #{torqueForceTestScope}</if>
            <if test="torqueForce != null  and torqueForce != ''"> and torque_force = #{torqueForce}</if>
            <if test="maxPermitError != null  and maxPermitError != ''"> and max_permit_error = #{maxPermitError}</if>
            <if test="calibrationIndicatingError != null  and calibrationIndicatingError != ''"> and calibration_indicating_error = #{calibrationIndicatingError}</if>
            <if test="calibrationUncertainty != null  and calibrationUncertainty != ''"> and calibration_uncertainty = #{calibrationUncertainty}</if>
            <if test="confirmJudgmentConclusion != null  and confirmJudgmentConclusion != ''"> and confirm_judgment_conclusion = #{confirmJudgmentConclusion}</if>
            <if test="confirmDate != null "> and confirm_date = #{confirmDate}</if>
            <if test="damageNo != null "> and damage_no = #{damageNo}</if>
            <if test="maintainNo != null "> and maintain_no = #{maintainNo}</if>
            <if test="discardNo != null "> and discard_no = #{discardNo}</if>
        </where>
    </select>

    <select id="selectMeterToolById" parameterType="Long" resultMap="MeterToolResult">
        <include refid="selectMeterToolVo"/>
        where id = #{id}
    </select>

    <insert id="insertMeterTool" parameterType="MeterTool" useGeneratedKeys="true" keyProperty="id">
        insert into meter_tool
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="remark != null">remark,</if>
            <if test="code != null">code,</if>
            <if test="typeId != null">type_id,</if>
            <if test="name != null">name,</if>
            <if test="modelNo != null">model_no,</if>
            <if test="scope != null">scope,</if>
            <if test="factoryNo != null">factory_no,</if>
            <if test="produceCompany != null">produce_company,</if>
            <if test="accuracy != null">accuracy,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="stashDept != null">stash_dept,</if>
            <if test="stashGroup != null">stash_group,</if>
            <if test="usePost != null">use_post,</if>
            <if test="safeguardPerson != null">safeguard_person,</if>
            <if test="torqueForceUseScope != null">torque_force_use_scope,</if>
            <if test="meterStatus != null">meter_status,</if>
            <if test="checkPeriod != null">check_period,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="effectiveTime != null">effective_time,</if>
            <if test="nextCheckTime != null">next_check_time,</if>
            <if test="firstProofPeriod != null">first_proof_period,</if>
            <if test="torqueForceTestScope != null">torque_force_test_scope,</if>
            <if test="torqueForce != null">torque_force,</if>
            <if test="maxPermitError != null">max_permit_error,</if>
            <if test="calibrationIndicatingError != null">calibration_indicating_error,</if>
            <if test="calibrationUncertainty != null">calibration_uncertainty,</if>
            <if test="confirmJudgmentConclusion != null">confirm_judgment_conclusion,</if>
            <if test="confirmDate != null">confirm_date,</if>
            <if test="damageNo != null">damage_no,</if>
            <if test="maintainNo != null">maintain_no,</if>
            <if test="discardNo != null">discard_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="code != null">#{code},</if>
            <if test="typeId != null">#{typeId},</if>
            <if test="name != null">#{name},</if>
            <if test="modelNo != null">#{modelNo},</if>
            <if test="scope != null">#{scope},</if>
            <if test="factoryNo != null">#{factoryNo},</if>
            <if test="produceCompany != null">#{produceCompany},</if>
            <if test="accuracy != null">#{accuracy},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="stashDept != null">#{stashDept},</if>
            <if test="stashGroup != null">#{stashGroup},</if>
            <if test="usePost != null">#{usePost},</if>
            <if test="safeguardPerson != null">#{safeguardPerson},</if>
            <if test="torqueForceUseScope != null">#{torqueForceUseScope},</if>
            <if test="meterStatus != null">#{meterStatus},</if>
            <if test="checkPeriod != null">#{checkPeriod},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="effectiveTime != null">#{effectiveTime},</if>
            <if test="nextCheckTime != null">#{nextCheckTime},</if>
            <if test="firstProofPeriod != null">#{firstProofPeriod},</if>
            <if test="torqueForceTestScope != null">#{torqueForceTestScope},</if>
            <if test="torqueForce != null">#{torqueForce},</if>
            <if test="maxPermitError != null">#{maxPermitError},</if>
            <if test="calibrationIndicatingError != null">#{calibrationIndicatingError},</if>
            <if test="calibrationUncertainty != null">#{calibrationUncertainty},</if>
            <if test="confirmJudgmentConclusion != null">#{confirmJudgmentConclusion},</if>
            <if test="confirmDate != null">#{confirmDate},</if>
            <if test="damageNo != null">#{damageNo},</if>
            <if test="maintainNo != null">#{maintainNo},</if>
            <if test="discardNo != null">#{discardNo},</if>
         </trim>
    </insert>

    <update id="updateMeterTool" parameterType="MeterTool">
        update meter_tool
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="code != null">code = #{code},</if>
            <if test="typeId != null">type_id = #{typeId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="modelNo != null">model_no = #{modelNo},</if>
            <if test="scope != null">scope = #{scope},</if>
            <if test="factoryNo != null">factory_no = #{factoryNo},</if>
            <if test="produceCompany != null">produce_company = #{produceCompany},</if>
            <if test="accuracy != null">accuracy = #{accuracy},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="stashDept != null">stash_dept = #{stashDept},</if>
            <if test="stashGroup != null">stash_group = #{stashGroup},</if>
            <if test="usePost != null">use_post = #{usePost},</if>
            <if test="safeguardPerson != null">safeguard_person = #{safeguardPerson},</if>
            <if test="torqueForceUseScope != null">torque_force_use_scope = #{torqueForceUseScope},</if>
            <if test="meterStatus != null">meter_status = #{meterStatus},</if>
            <if test="checkPeriod != null">check_period = #{checkPeriod},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="effectiveTime != null">effective_time = #{effectiveTime},</if>
            <if test="nextCheckTime != null">next_check_time = #{nextCheckTime},</if>
            <if test="firstProofPeriod != null">first_proof_period = #{firstProofPeriod},</if>
            <if test="torqueForceTestScope != null">torque_force_test_scope = #{torqueForceTestScope},</if>
            <if test="torqueForce != null">torque_force = #{torqueForce},</if>
            <if test="maxPermitError != null">max_permit_error = #{maxPermitError},</if>
            <if test="calibrationIndicatingError != null">calibration_indicating_error = #{calibrationIndicatingError},</if>
            <if test="calibrationUncertainty != null">calibration_uncertainty = #{calibrationUncertainty},</if>
            <if test="confirmJudgmentConclusion != null">confirm_judgment_conclusion = #{confirmJudgmentConclusion},</if>
            <if test="confirmDate != null">confirm_date = #{confirmDate},</if>
            <if test="damageNo != null">damage_no = #{damageNo},</if>
            <if test="maintainNo != null">maintain_no = #{maintainNo},</if>
            <if test="discardNo != null">discard_no = #{discardNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMeterToolById" parameterType="Long">
        update meter_tool set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteMeterToolByIds" parameterType="String">
        update meter_tool set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getMeterInfo" resultMap="MeterToolResult">
        <include refid="selectMeterTool"/>
        where code = #{code} and del_flag = "0"
    </select>
</mapper>
