<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.meter.mapper.MeterMaintainHistoryMapper">
    
    <resultMap type="MeterMaintainHistory" id="MeterMaintainHistoryResult">
        <result property="id"    column="id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="remark"    column="remark"    />
        <result property="maintainStartTime"    column="maintain_start_time"    />
        <result property="maintainEndTime"    column="maintain_end_time"    />
        <result property="toolId"    column="tool_id"    />
        <result property="damageCause"    column="damage_cause"    />
        <result property="maintainAmount"    column="maintain_amount"    />
    </resultMap>

    <sql id="selectMeterMaintainHistoryVo">
        select id, del_flag, create_time, update_time, delete_time, create_by, update_by, delete_by, remark, maintain_start_time, maintain_end_time, tool_id, damage_cause, maintain_amount from meter_maintain_history
    </sql>

    <select id="selectMeterMaintainHistoryList" parameterType="MeterMaintainHistory" resultMap="MeterMaintainHistoryResult">
        <include refid="selectMeterMaintainHistoryVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="maintainStartTime != null "> and maintain_start_time = #{maintainStartTime}</if>
            <if test="maintainEndTime != null "> and maintain_end_time = #{maintainEndTime}</if>
            <if test="toolId != null "> and tool_id = #{toolId}</if>
            <if test="damageCause != null  and damageCause != ''"> and damage_cause = #{damageCause}</if>
            <if test="maintainAmount != null "> and maintain_amount = #{maintainAmount}</if>
        </where>
    </select>
    
    <select id="selectMeterMaintainHistoryById" parameterType="Long" resultMap="MeterMaintainHistoryResult">
        <include refid="selectMeterMaintainHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMeterMaintainHistory" parameterType="MeterMaintainHistory" useGeneratedKeys="true" keyProperty="id">
        insert into meter_maintain_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="remark != null">remark,</if>
            <if test="maintainStartTime != null">maintain_start_time,</if>
            <if test="maintainEndTime != null">maintain_end_time,</if>
            <if test="toolId != null">tool_id,</if>
            <if test="damageCause != null">damage_cause,</if>
            <if test="maintainAmount != null">maintain_amount,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="maintainStartTime != null">#{maintainStartTime},</if>
            <if test="maintainEndTime != null">#{maintainEndTime},</if>
            <if test="toolId != null">#{toolId},</if>
            <if test="damageCause != null">#{damageCause},</if>
            <if test="maintainAmount != null">#{maintainAmount},</if>
         </trim>
    </insert>

    <update id="updateMeterMaintainHistory" parameterType="MeterMaintainHistory">
        update meter_maintain_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="maintainStartTime != null">maintain_start_time = #{maintainStartTime},</if>
            <if test="maintainEndTime != null">maintain_end_time = #{maintainEndTime},</if>
            <if test="toolId != null">tool_id = #{toolId},</if>
            <if test="damageCause != null">damage_cause = #{damageCause},</if>
            <if test="maintainAmount != null">maintain_amount = #{maintainAmount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMeterMaintainHistoryById" parameterType="Long">
        update meter_maintain_history set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteMeterMaintainHistoryByIds" parameterType="String">
        update meter_maintain_history set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>