<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.meter.mapper.MeterFirstProofMapper">
    
    <resultMap type="MeterFirstProof" id="MeterFirstProofResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="remark"    column="remark"    />
        <result property="measureCode"    column="measure_code"    />
        <result property="measureName"    column="measure_name"    />
        <result property="verifier"    column="verifier"    />
        <result property="verificationTime"    column="verification_time"    />
        <result property="standardVal"    column="standard_val"    />
        <result property="calibrationVal"    column="calibration_val"    />
        <result property="reviseVal"    column="revise_val"    />
        <result property="calibratorRange"    column="calibrator_range"    />
        <result property="multimeterVal"    column="multimeter_val"    />
        <result property="errorVal"    column="error_val"    />
    </resultMap>

    <sql id="selectMeterFirstProofVo">
        select id, create_time, update_time, delete_time, create_by, update_by, delete_by, remark, measure_code, measure_name, verifier, verification_time, standard_val, calibration_val, revise_val, calibrator_range, multimeter_val, error_val from meter_first_proof
    </sql>

    <select id="selectMeterFirstProofList" parameterType="MeterFirstProof" resultMap="MeterFirstProofResult">
        <include refid="selectMeterFirstProofVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="measureCode != null  and measureCode != ''"> and measure_code = #{measureCode}</if>
            <if test="measureName != null  and measureName != ''"> and measure_name like concat('%', #{measureName}, '%')</if>
            <if test="verifier != null "> and verifier = #{verifier}</if>
            <if test="verificationTime != null "> and verification_time = #{verificationTime}</if>
            <if test="standardVal != null  and standardVal != ''"> and standard_val = #{standardVal}</if>
            <if test="calibrationVal != null  and calibrationVal != ''"> and calibration_val = #{calibrationVal}</if>
            <if test="reviseVal != null  and reviseVal != ''"> and revise_val = #{reviseVal}</if>
            <if test="calibratorRange != null  and calibratorRange != ''"> and calibrator_range = #{calibratorRange}</if>
            <if test="multimeterVal != null  and multimeterVal != ''"> and multimeter_val = #{multimeterVal}</if>
            <if test="errorVal != null  and errorVal != ''"> and error_val = #{errorVal}</if>
        </where>
    </select>
    
    <select id="selectMeterFirstProofById" parameterType="Long" resultMap="MeterFirstProofResult">
        <include refid="selectMeterFirstProofVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMeterFirstProof" parameterType="MeterFirstProof" useGeneratedKeys="true" keyProperty="id">
        insert into meter_first_proof
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="remark != null">remark,</if>
            <if test="measureCode != null">measure_code,</if>
            <if test="measureName != null">measure_name,</if>
            <if test="verifier != null">verifier,</if>
            <if test="verificationTime != null">verification_time,</if>
            <if test="standardVal != null">standard_val,</if>
            <if test="calibrationVal != null">calibration_val,</if>
            <if test="reviseVal != null">revise_val,</if>
            <if test="calibratorRange != null">calibrator_range,</if>
            <if test="multimeterVal != null">multimeter_val,</if>
            <if test="errorVal != null">error_val,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="measureCode != null">#{measureCode},</if>
            <if test="measureName != null">#{measureName},</if>
            <if test="verifier != null">#{verifier},</if>
            <if test="verificationTime != null">#{verificationTime},</if>
            <if test="standardVal != null">#{standardVal},</if>
            <if test="calibrationVal != null">#{calibrationVal},</if>
            <if test="reviseVal != null">#{reviseVal},</if>
            <if test="calibratorRange != null">#{calibratorRange},</if>
            <if test="multimeterVal != null">#{multimeterVal},</if>
            <if test="errorVal != null">#{errorVal},</if>
         </trim>
    </insert>

    <update id="updateMeterFirstProof" parameterType="MeterFirstProof">
        update meter_first_proof
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="measureCode != null">measure_code = #{measureCode},</if>
            <if test="measureName != null">measure_name = #{measureName},</if>
            <if test="verifier != null">verifier = #{verifier},</if>
            <if test="verificationTime != null">verification_time = #{verificationTime},</if>
            <if test="standardVal != null">standard_val = #{standardVal},</if>
            <if test="calibrationVal != null">calibration_val = #{calibrationVal},</if>
            <if test="reviseVal != null">revise_val = #{reviseVal},</if>
            <if test="calibratorRange != null">calibrator_range = #{calibratorRange},</if>
            <if test="multimeterVal != null">multimeter_val = #{multimeterVal},</if>
            <if test="errorVal != null">error_val = #{errorVal},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMeterFirstProofById" parameterType="Long">
        update meter_first_proof set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteMeterFirstProofByIds" parameterType="String">
        update meter_first_proof set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>