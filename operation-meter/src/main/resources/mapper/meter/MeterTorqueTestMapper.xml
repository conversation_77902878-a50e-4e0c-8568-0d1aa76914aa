<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.meter.mapper.MeterTorqueTestMapper">
    
    <resultMap type="MeterTorqueTest" id="MeterTorqueTestResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="remark"    column="remark"    />
        <result property="code"    column="code"    />
        <result property="measureCode"    column="measure_code"    />
        <result property="measureName"    column="measure_name"    />
        <result property="useScope"    column="use_scope"    />
        <result property="valOne"    column="val_one"    />
        <result property="valTwo"    column="val_two"    />
        <result property="valThree"    column="val_three"    />
        <result property="averageVal"    column="average_val"    />
        <result property="result"    column="result"    />
        <result property="checkPerson"    column="check_person"    />
        <result property="checkTime"    column="check_time"    />
    </resultMap>

    <sql id="selectMeterTorqueTestVo">
        select id, create_time, update_time, delete_time, create_by, update_by, delete_by, remark, code, measure_code, measure_name, use_scope, val_one, val_two, val_three, average_val, result, check_person, check_time from meter_torque_test
    </sql>

    <select id="selectMeterTorqueTestList" parameterType="MeterTorqueTest" resultMap="MeterTorqueTestResult">
        <include refid="selectMeterTorqueTestVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="measureCode != null  and measureCode != ''"> and measure_code = #{measureCode}</if>
            <if test="measureName != null  and measureName != ''"> and measure_name like concat('%', #{measureName}, '%')</if>
            <if test="useScope != null  and useScope != ''"> and use_scope = #{useScope}</if>
            <if test="valOne != null  and valOne != ''"> and val_one = #{valOne}</if>
            <if test="valTwo != null  and valTwo != ''"> and val_two = #{valTwo}</if>
            <if test="valThree != null  and valThree != ''"> and val_three = #{valThree}</if>
            <if test="averageVal != null  and averageVal != ''"> and average_val = #{averageVal}</if>
            <if test="result != null  and result != ''"> and result = #{result}</if>
            <if test="checkPerson != null "> and check_person = #{checkPerson}</if>
            <if test="checkTime != null "> and check_time = #{checkTime}</if>
        </where>
    </select>
    
    <select id="selectMeterTorqueTestById" parameterType="Long" resultMap="MeterTorqueTestResult">
        <include refid="selectMeterTorqueTestVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMeterTorqueTest" parameterType="MeterTorqueTest" useGeneratedKeys="true" keyProperty="id">
        insert into meter_torque_test
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="remark != null">remark,</if>
            <if test="code != null">code,</if>
            <if test="measureCode != null">measure_code,</if>
            <if test="measureName != null">measure_name,</if>
            <if test="useScope != null">use_scope,</if>
            <if test="valOne != null">val_one,</if>
            <if test="valTwo != null">val_two,</if>
            <if test="valThree != null">val_three,</if>
            <if test="averageVal != null">average_val,</if>
            <if test="result != null">result,</if>
            <if test="checkPerson != null">check_person,</if>
            <if test="checkTime != null">check_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="code != null">#{code},</if>
            <if test="measureCode != null">#{measureCode},</if>
            <if test="measureName != null">#{measureName},</if>
            <if test="useScope != null">#{useScope},</if>
            <if test="valOne != null">#{valOne},</if>
            <if test="valTwo != null">#{valTwo},</if>
            <if test="valThree != null">#{valThree},</if>
            <if test="averageVal != null">#{averageVal},</if>
            <if test="result != null">#{result},</if>
            <if test="checkPerson != null">#{checkPerson},</if>
            <if test="checkTime != null">#{checkTime},</if>
         </trim>
    </insert>

    <update id="updateMeterTorqueTest" parameterType="MeterTorqueTest">
        update meter_torque_test
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="code != null">code = #{code},</if>
            <if test="measureCode != null">measure_code = #{measureCode},</if>
            <if test="measureName != null">measure_name = #{measureName},</if>
            <if test="useScope != null">use_scope = #{useScope},</if>
            <if test="valOne != null">val_one = #{valOne},</if>
            <if test="valTwo != null">val_two = #{valTwo},</if>
            <if test="valThree != null">val_three = #{valThree},</if>
            <if test="averageVal != null">average_val = #{averageVal},</if>
            <if test="result != null">result = #{result},</if>
            <if test="checkPerson != null">check_person = #{checkPerson},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMeterTorqueTestById" parameterType="Long">
        update meter_torque_test set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteMeterTorqueTestByIds" parameterType="String">
        update meter_torque_test set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>