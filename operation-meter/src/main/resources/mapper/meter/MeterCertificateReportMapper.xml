<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzforward.meter.mapper.MeterCertificateReportMapper">
    
    <resultMap type="MeterCertificateReport" id="MeterCertificateReportResult">
        <result property="id"    column="id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="deleteBy"    column="delete_by"    />
        <result property="remark"    column="remark"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="uploader"    column="uploader"    />
        <result property="uploadTime"    column="upload_time"    />
    </resultMap>

    <sql id="selectMeterCertificateReportVo">
        select id, create_time, update_time, delete_time, create_by, update_by, delete_by, remark, code, name, uploader, upload_time from meter_certificate_report
    </sql>

    <select id="selectMeterCertificateReportList" parameterType="MeterCertificateReport" resultMap="MeterCertificateReportResult">
        <include refid="selectMeterCertificateReportVo"/>
        <where>
            <if test="delFlag != null "> and del_flag = #{delFlag}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="uploader != null "> and uploader = #{uploader}</if>
            <if test="uploadTime != null "> and upload_time = #{uploadTime}</if>
        </where>
    </select>
    
    <select id="selectMeterCertificateReportById" parameterType="Long" resultMap="MeterCertificateReportResult">
        <include refid="selectMeterCertificateReportVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMeterCertificateReport" parameterType="MeterCertificateReport" useGeneratedKeys="true" keyProperty="id">
        insert into meter_certificate_report
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="remark != null">remark,</if>
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="uploader != null">uploader,</if>
            <if test="uploadTime != null">upload_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="uploader != null">#{uploader},</if>
            <if test="uploadTime != null">#{uploadTime},</if>
         </trim>
    </insert>

    <update id="updateMeterCertificateReport" parameterType="MeterCertificateReport">
        update meter_certificate_report
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="uploader != null">uploader = #{uploader},</if>
            <if test="uploadTime != null">upload_time = #{uploadTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMeterCertificateReportById" parameterType="Long">
        update meter_certificate_report set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id = #{id}
    </delete>

    <delete id="deleteMeterCertificateReportByIds" parameterType="String">
        update meter_certificate_report set del_flag = '2' , delete_time = now() , delete_by = #{deleteBy} where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>