package com.hzforward.quartz.task;

import com.hzforward.common.utils.spring.SpringUtils;
import com.hzforward.dataCheck.service.IPackingCheckService;
import com.hzforward.dinnerManage.service.IDinnerManageApplicationInfoService;
import com.hzforward.dinnerManage.service.IDinnerManageExamineApproveService;
import com.hzforward.equipmentManage.service.IMaintainReportService;
import com.hzforward.equipmentManage.service.IRepairWorkingHoursService;
import com.hzforward.oee.service.IOeeReportService;
import com.hzforward.oee.service.baseData.ISyncTidalSandData;
import com.hzforward.system.service.ISysUserService;
import org.springframework.stereotype.Component;

/**
 * 定时任务
 *
 * <AUTHOR>
 */
@Component("forwardTask")
public class ForwardTask
{
    // 定时推送用餐人数邮件
    public void sendMail(){
        SpringUtils.getBean(IDinnerManageExamineApproveService.class).pushOrderingInformation();
    }

    // 定时设置未取餐状态
    public void pickUpFlagSet(){
        SpringUtils.getBean(IDinnerManageApplicationInfoService.class).pickUpFlagSet();
    }

    // 生产设备管理   设备保养  定时生成设备保养计划
    public void createMaintainPlan() {
        SpringUtils.getBean(IMaintainReportService.class).autoCreateMaintainPlan();
    }

    // 定时同步钉钉用户信息
    public void syncDingtalkUsers() {
        SpringUtils.getBean(ISysUserService.class).syncDingTalkUser();
    }

    // 定时结算维修工每天工时
    public void calculateAllWorkingHours() {
        SpringUtils.getBean(IRepairWorkingHoursService.class).calculateAllWorkingHours();
    }

    // 定时同步前一天潮模砂线的汇总数据
    public void syncTidalSandData() {
        SpringUtils.getBean(ISyncTidalSandData.class).syncYesTodayTidalSandData();
    }

    // 汇总OEE数据
    public void summaryOEEData() { SpringUtils.getBean(IOeeReportService.class).summaryOEEData(); }

    // 发送电缆当日未完成装箱的合同号列表
    public void sendUnpackingEmail() { SpringUtils.getBean(IPackingCheckService.class).sendUnpackingEmail(); }
}
