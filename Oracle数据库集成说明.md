# Oracle数据库集成说明

## 概述

本项目已成功集成Oracle数据库支持，Oracle数据库配置为 `slave9` 数据源。以下是详细的配置信息和使用方法。

## 数据库连接信息

- **主机**: 10.18.0.35
- **端口**: 1521
- **服务名**: orcl
- **用户名**: fwd
- **密码**: oraclefwd
- **数据源名称**: slave9

## 已完成的配置

### 1. 依赖配置

在 `operation-admin/pom.xml` 中已添加Oracle JDBC驱动：

```xml
<!-- Oracle JDBC驱动 -->
<dependency>
    <groupId>com.oracle.database.jdbc</groupId>
    <artifactId>ojdbc8</artifactId>
</dependency>
```

### 2. 数据源配置

在 `operation-admin/src/main/resources/application-dev-druid.yml` 中已配置Oracle数据源：

```yaml
slave9:
  enabled: true
  driverClassName: oracle.jdbc.OracleDriver
  url: **************************************
  username: fwd
  password: oraclefwd
  initialSize: 5
  minIdle: 5
  maxActive: 20
  maxWait: 60000
  timeBetweenEvictionRunsMillis: 60000
  minEvictableIdleTimeMillis: 300000
  validationQuery: SELECT 1 FROM DUAL
  testWhileIdle: true
  testOnBorrow: false
  testOnReturn: false
  poolPreparedStatements: true
  maxPoolPreparedStatementPerConnectionSize: 20
  filters: stat,wall
  connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
```

### 3. 多数据源框架

项目已具备完整的多数据源支持框架：

- `DataSourceType.SLAVE9` - Oracle数据源类型枚举
- `DynamicDataSource` - 动态数据源路由
- `DynamicDataSourceContextHolder` - 数据源上下文管理
- `DruidConfig` - 数据源配置类

## 使用方法

### 方法1：使用注解切换数据源（推荐）

```java
@Service
public class YourService {
    
    @Autowired
    private YourMapper yourMapper;
    
    @DataSource(DataSourceType.SLAVE9)
    public List<YourEntity> queryFromOracle() {
        // 此方法内的所有数据库操作都会使用Oracle数据源
        return yourMapper.selectList(null);
    }
}
```

### 方法2：手动切换数据源

```java
@Service
public class YourService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    public String queryFromOracle() {
        try {
            // 手动切换到Oracle数据源
            DynamicDataSourceContextHolder.setDataSourceType(DataSourceType.SLAVE9.name());
            
            // 执行数据库操作
            return jdbcTemplate.queryForObject("SELECT 'Hello Oracle' FROM DUAL", String.class);
            
        } finally {
            // 清除数据源设置
            DynamicDataSourceContextHolder.clearDataSourceType();
        }
    }
}
```

## 测试接口

已创建测试控制器 `OracleTestController`，提供以下测试接口：

### 基础测试接口

1. **连接测试**: `GET /system/oracle/testConnection`
   - 测试Oracle数据库连接是否正常

2. **获取数据库时间**: `GET /system/oracle/getDatabaseTime`
   - 获取Oracle数据库当前时间

3. **获取数据库版本**: `GET /system/oracle/getVersion`
   - 获取Oracle数据库版本信息

### 高级测试接口

4. **获取表空间信息**: `GET /system/oracle/getTablespaces`
   - 查询数据库表空间信息

5. **获取用户表列表**: `GET /system/oracle/getUserTables`
   - 查询当前用户的所有表

6. **执行自定义查询**: `POST /system/oracle/executeQuery`
   - 执行自定义SQL查询（仅支持SELECT语句）

7. **获取连接池状态**: `GET /system/oracle/getConnectionPoolStatus`
   - 查询当前连接池状态

## 测试步骤

### 1. 启动应用

确保Oracle数据库服务正常运行，然后启动Spring Boot应用。

### 2. 访问Swagger文档

访问 `http://localhost:8080/swagger-ui/` 查看Oracle测试接口文档。

### 3. 执行基础测试

按以下顺序执行测试：

1. 首先执行连接测试确保数据库连接正常
2. 获取数据库时间验证查询功能
3. 获取数据库版本确认Oracle版本
4. 查看表空间和用户表了解数据库结构

### 4. 自定义查询测试

使用自定义查询接口执行一些简单的SELECT语句，例如：

```sql
SELECT * FROM USER_TABLES WHERE ROWNUM <= 10
SELECT SYSDATE FROM DUAL
SELECT USER FROM DUAL
```

## 注意事项

### 1. 数据源切换

- 使用 `@DataSource` 注解是推荐的方式，它会自动管理数据源的切换和清理
- 手动切换数据源时，务必在 `finally` 块中清理数据源设置

### 2. 连接池配置

- Oracle数据源配置了独立的连接池参数
- 验证查询使用 `SELECT 1 FROM DUAL`（Oracle标准语法）
- 连接池大小根据实际需求调整

### 3. 安全考虑

- 测试接口中的自定义查询已添加基础的SQL注入防护
- 生产环境中应该移除或限制测试接口的访问

### 4. 错误处理

- 所有数据库操作都包含了适当的异常处理
- 连接失败时会记录详细的错误日志

## 扩展使用

### 创建Oracle专用的Mapper

```java
@Mapper
public interface OracleDataMapper {
    
    @DataSource(DataSourceType.SLAVE9)
    List<Map<String, Object>> selectOracleData(@Param("sql") String sql);
    
    @DataSource(DataSourceType.SLAVE9)
    int countOracleRecords(@Param("tableName") String tableName);
}
```

### 在Service中使用

```java
@Service
public class OracleDataService {
    
    @Autowired
    private OracleDataMapper oracleDataMapper;
    
    public List<Map<String, Object>> getOracleData(String sql) {
        return oracleDataMapper.selectOracleData(sql);
    }
}
```

## 故障排除

### 常见问题

1. **连接超时**: 检查网络连接和防火墙设置
2. **用户权限不足**: 确认Oracle用户具有必要的查询权限
3. **驱动版本不兼容**: 确认Oracle JDBC驱动版本与数据库版本兼容

### 日志查看

查看应用日志中的Oracle相关信息：

```
grep -i oracle application.log
grep -i "slave9" application.log
```

## 总结

Oracle数据库已成功集成到项目中，可以通过多数据源框架灵活地在不同数据源之间切换。建议在实际使用前先通过测试接口验证连接和功能是否正常。
